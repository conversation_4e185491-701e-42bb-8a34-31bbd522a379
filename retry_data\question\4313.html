<html class="nprogress-busy"><head><meta charset="utf-8"><title>591客服知識庫</title><link rel="stylesheet" href="/static/font/font-awesome.min.css"><link href="/css/app.e5aa4.css" rel="stylesheet"><script type="text/javascript" charset="utf-8" async="" src="/js/questionDetail.d0a6b.js"></script><style type="text/css">.que-detail-page .index-left .button{width:74px;height:32px;line-height:32px;font-size:13px;margin-right:10px}.que-detail-page .index-left .button:hover{color:#fff}.que-detail-page .detail-box{padding:20px 30px;border:1px solid #e5e5e5;border-radius:4px}.que-detail-page .detail-box h3{font-size:18px;color:#333;margin-bottom:10px}.que-detail-page .detail-box h3 span{color:#f77d24}.que-detail-page .detail-box .add-fav{cursor:pointer}.que-detail-page .d-b-menu{margin:15px 0 25px;font-size:13px;color:#999}.que-detail-page .d-b-menu b{display:inline-block;font-weight:400;margin:0 10px}.que-detail-page .d-b-menu .r{float:right;color:#f77d24}.que-detail-page .d-b-menu .r .fa{margin-right:5px;font-size:16px}.que-detail-page .d-b-cnt{margin-bottom:25px;font-size:13px;color:#666}.que-detail-page .submit-ask{display:block;margin:32px auto 0}.que-detail-page .reply-list{margin:30px 0 10px;overflow:hidden}.que-detail-page .reply-list .reply-item{padding:15px 30px 25px;margin-bottom:30px;border-radius:4px;border:1px solid #e5e5e5}.que-detail-page .reply-list .reply-item h4{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;line-height:50px;font-size:13px}.que-detail-page .reply-list .reply-item h4 img{width:40px;height:40px;border-radius:50%}.que-detail-page .reply-list .reply-item h4 .time{color:#999}.que-detail-page .reply-list .reply-item h4 .button{-ms-flex-item-align:center;align-self:center}.que-detail-page .reply-list .reply-item h4 .delete{color:#666;cursor:pointer}.que-detail-page .reply-list .reply-item .reply-detail{padding:20px 0 30px}.que-detail-page .more-answer{font-size:18px;color:#666;margin-bottom:5px}.que-detail-page .editor-content table{width:100%;border:1px solid #ddd}.que-detail-page .editor-content table td,.que-detail-page .editor-content table th{border:1px solid #ddd;padding:2px 8px;line-height:2}.que-detail-page .editor-content img{max-width:100%;vertical-align:text-bottom}.que-detail-page .edit-btn{text-align:center;margin-top:20px}.que-detail-page .edit-btn .edit-cancle{background:#aaa}</style><style type="text/css">.head-info{line-height:26px;background:#f5f5f5;font-size:13px;color:#999}.head-info a{margin-left:20px;color:#333}.head-search{margin-top:22px}.head-search img{float:left}.head-search .srh-box{float:left;margin:20px 0 0 20px}.head-search .srh-box .srh-input{float:left;width:260px;height:38px;line-height:38px;border:2px solid #37a508;border-radius:4px 0 0 4px}.head-search .srh-box a,.head-search .srh-box button{float:left;width:80px;height:38px;line-height:36px;font-size:16px;color:#fff}.head-search .srh-box .srh-btn{border-radius:0 4px 4px 0}.head-search .srh-box .ask-btn{margin-left:10px}.head-nav{float:left;margin:20px 0 0 60px;color:#fff;font-size:16px;line-height:36px}.head-nav a{display:block;height:100%;color:#333}.head-nav li{position:relative;float:left;margin-right:35px}.head-nav .active a,.head-nav li:hover a{color:#30b940}.head-nav .active:after,.head-nav li:hover:after{display:block;content:"";height:3px;width:100%;background:#30b940}.head-pic{width:100%;height:90px;margin-top:20px;background:url(/static/banner.png) top no-repeat}</style><style type="text/css">.top-breadcrumb{height:20px;margin-top:10px;margin-bottom:40px;font-size:13px;color:#666}.top-breadcrumb .t-b-icon{color:#30b940}.top-breadcrumb span{margin-left:8px}.top-breadcrumb span .fa{font-size:16px;margin-left:8px}.top-breadcrumb span:first-child{margin-left:0}.top-breadcrumb .now{color:#30b940}</style><script type="text/javascript" src="/static/ueditor/ueditor.config.js?20210319" id="configScriptTag"></script><script type="text/javascript" src="/static/ueditor/ueditor.all.js?20210319" id="editorScriptTag"></script><style type="text/css">.checkbox[data-v-0fe26aab]{word-spacing:4px;vertical-align:bottom;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.checkbox i[data-v-0fe26aab]{display:inline-block;width:14px;height:14px;border-radius:2px;border:1px solid #ccc;background:#fff;position:relative;top:1px}.checkbox input[data-v-0fe26aab]{opacity:0;width:0;height:0;margin:0}.checkbox input:checked+i[data-v-0fe26aab]{background:#30b940;border-color:#30b940}.checkbox input:checked+i[data-v-0fe26aab]:before{content:"";position:absolute;left:2px;top:1px;width:11px;height:6px;border-bottom:2px solid #fff;border-left:2px solid #fff;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}</style><style type="text/css">.popbox{position:fixed;top:50%;left:50%;z-index:1011;background:#fff;border-radius:6px;overflow:hidden}.popbox h3{font-size:16px;line-height:40px;padding:0 20px}.popbox h3 span{float:right;color:#666;font-size:13px;cursor:pointer}.popbox .pb-cnt{padding:0 20px;font-size:13px}.popbox .pb-btn-area{position:absolute;bottom:0;left:0;z-index:1;width:100%;text-align:right;padding:0 20px 20px 0}.popbox .pb-btn-area button{height:32px;line-height:32px;border:0;background:#fff;cursor:pointer;border-radius:6px;margin-left:15px;padding:0 15px}.popbox .pb-btn-area button.pb-btn-ok{background:#daf3cf;color:#279005}.popbox .pb-btn-area button.pb-btn-ok:hover{background:#279005;color:#fff}</style><style type="text/css">.shadow{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1010;background:#000}</style><style type="text/css">.footer{margin-top:70px;margin-bottom:20px;padding-top:20px;text-align:center;color:#999;border-top:1px solid #f5f5f5}.footer span{margin:0 10px}</style><script src="/static/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript" defer="defer"></script><link href="/static/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet"><script src="/static/ueditor/third-party/codemirror/codemirror.js" type="text/javascript" defer="defer"></script><link rel="stylesheet" type="text/css" href="/static/ueditor/third-party/codemirror/codemirror.css"><script src="/static/ueditor/third-party/zeroclipboard/ZeroClipboard.js" type="text/javascript" defer="defer"></script><style type="text/css">.activeUser,.favorite,.hotTag,.rank{margin-bottom:40px}</style><style type="text/css">.rank .rank-page{height:60px;line-height:60px;font-size:18px;margin-bottom:20px;text-align:center;background:#fafafa;border-radius:5px}.rank ul{padding:0 20px}.rank li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;border-bottom:1px dotted #f5f5f5;line-height:50px}.rank li .flag{position:relative;display:inline-block;width:20px;height:30px;background:#476472;vertical-align:middle;margin-right:12px;color:#fff;text-align:center;line-height:20px;font-size:13px;font-style:normal}.rank li .flag:after{content:"";position:absolute;bottom:-4px;left:-4px;border:14px solid transparent;border-bottom-color:#fff}.rank li:first-child .flag{background:#fb6757}.rank li:nth-child(2) .flag{background:#fac525}.rank li:nth-child(3) .flag{background:#00a9fb}.rank li img{width:36px;height:36px;border-radius:50%;margin-right:12px}</style><style type="text/css">.activeUser .activeUser-box a{display:inline-block;width:64px;height:64px;border-radius:50%;overflow:hidden;margin:0 6px 12px}</style><style type="text/css">.to-top{display:none;position:fixed;bottom:150px;z-index:5;width:50px;height:50px;cursor:pointer}</style></head><body style=""><div id="app" style=""><div class="que-detail-page" style=""><div class="wiki-hd"><div class="head-info"><p class="page-cnt">
      您好，欢迎来到591客服知识库
      <a href="https://www.591.com.tw/admin.php?new_url=https://zsk.591.com.tw">[请登入]</a> <!----></p></div> <div class="page-cnt head-search clearfix"><a href="/" class="router-link-active"><img src="/static/logo.png"></a> <ul class="head-nav clearfix"><li class="router-link-active active"><a href="/">591房屋交易网</a></li> <li class=""><a href="/life">客服充电站</a></li> <li class=""><a href="/notification">通知事项</a></li></ul> <div class="srh-box"><input type="text" placeholder="请输入搜索内容" class="srh-input"> <button type="button" class="button srh-btn">搜索</button> <a href="/ask/591" class="button button-orange ask-btn">提問</a></div></div> <div class="head-pic"></div></div> <div class="top-breadcrumb com-width"><span><a href="/collection/591/1209" class="">客服督導相關<i class="fa fa-angle-double-right"></i></a></span><span><a href="/collection/591/1209/1658" class="">業務作業指導<i class="fa fa-angle-double-right"></i></a></span><span><strong class="now">问题详情</strong></span></div> <div class="com-width clearfix" style=""><div class="index-left" style=""><div class="detail-box"><h3><!---->新建案作業指導書</h3> <p class="clearfix d-b-menu"><span class="l">
            王菊芳&nbsp;&nbsp;&nbsp;浏览403次&nbsp;&nbsp;&nbsp;2020-11-03 11:46:48
          </span> <span class="r"><span class="add-fav"><i class="fa fa-heart"></i>加入最爱</span></span></p> <div class="d-b-cnt editor-content big-img-parent"><div><p><span style="font-family: 微软雅黑, Microsoft YaHei;">如題</span><br></p></div></div> <div class="d-b-button"><a href="/edit/591/4313" class="button">编辑</a> <button class="button button-orange">关闭提问</button></div></div> <div class="hr-line"></div> <ul class="reply-list big-img-parent"><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-03 14:21:54</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><h3><strong><span style="line-height: 172%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">1、作業的目的</span></strong></h3><p><span style="color: #333333; text-indent: 28px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">通過新建案審核，保證網站資訊相對真實有效，規範網站建案廣告刊登秩序，給房客提供良好的找屋環境，使591值得信賴</span></p><p><strong style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: rgb(51, 51, 51); font-size: 13px; box-sizing: border-box;"><span style="box-sizing: border-box;font-size: 16px;line-height: 24px"><br></span></strong></p><p><span style="color: #000000;"><strong style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: rgb(51, 51, 51); font-size: 13px; box-sizing: border-box;"><span style="box-sizing: border-box; font-size: 16px; line-height: 24px;">2．<span style="font-weight: normal; font-stretch: normal; font-size: 9px; line-height: normal;">&nbsp;&nbsp;</span></span></strong><strong style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: rgb(51, 51, 51); font-size: 13px; box-sizing: border-box;"><span style="box-sizing: border-box; font-size: 16px; line-height: 24px;">適用對象及範圍</span></strong></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #333333; font-size: 13px;">房屋交易事業部→客服部→二星含以上客服</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #333333; font-size: 13px;"><br></span></p><p><strong><span style="color: #333333; font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;">3、新建案刊登</span></strong></p><p><strong><span style="color: #333333; font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;">3.1新建案刊登審核</span></strong></p><p><strong><span style="color: #333333; font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;">3.1.1審核規範</span></strong></p><p><strong><span style="color: #333333; font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"></span></strong></p><table width="747" cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px;" width="142" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">一級分類</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="400" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">二級分類</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="142"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">會員身份</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="400" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">1</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">仲介身份不可刊登廣告</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">2.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">註冊的建設及代銷公司與該案場不符不可刊登廣告</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="142"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">建築執照</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="400" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">1.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">投資建設、建築設計、營造公司名稱要一致</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">2.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">建造、使照要必填其中一項，若未取得建照不可刊登</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">3.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">結構工程、棟戶規劃、樓層規劃、用途規劃、土地分區、停車方式、基地面積、建蔽率要核對一致</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">4.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">當土地分區為工業或商業用時，用途不可為住家；當樓棟規劃有店面或商鋪時，用途需為住商用</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">5.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">當有多張建照或使照時，建蔽率可為60%的標準值，無需計算平均值</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">6.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">當有多張建照或使照時，基地面積需加總後除3.3057計算出坪數</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">7.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">當查到建照或使照未詳細標準車位時，則為前院停車</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="142"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">圖片</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="400"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">封面：</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">1</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">、必須為外觀圖，若無外觀圖則按以下優先順序（優先順序：成屋外觀&gt;示意圖外觀&gt;現場結構體&gt;接待中心&gt;基地照片）<br> &nbsp; 2、預推案可以沒有封面<br> &nbsp; 3、若為3D圖可備註僅供參考字樣，若非3D圖則不可備註任何文字<br> &nbsp; 4、封面圖兩邊不能留白</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">廣告宣傳圖：建案宣傳的圖片儘量要求4張以上，若達不到不強制</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">3D</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">示意圖：包含模型照、外觀示意圖、室內空間示意圖共儘量5張以上，若達不到不強制</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">格局圖：全區的平面圖</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">建案實景圖：新成屋必須5張以上、預售屋必須3張以上(不同角度、遠近)</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">周邊環境圖：機能、交通、學區、綠地等必須7張照片以上</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">樣品屋/實品屋：各空間照片共儘量8張以上，若達不到不強制</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">平面家配圖：</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">1</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">、平面圖需符合建照用途（如規劃住家當成店面使用）</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">2</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">、平面圖不可違法二次施工</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">交通位置圖：以清晰易懂為原則</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">不可一圖多用</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">圖片需真實有效</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">需按照正確類別上傳相應的圖片，若有誤需協助移到正確位置</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">所有圖片上不可備註非保護電話外的聯絡電話</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">大小：最大支持10M，比例4:3</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">&nbsp;</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="142"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">資料內容</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="400" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">1.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">已完銷之建案不可刊登</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">2.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">無效建案名稱不可刊登</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">3.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">建案名稱內添加其他描述性的文字不可刊登，直接編輯掉無關的文字</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">4.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">單價填寫錯誤（別墅透天因沒有價格區間，無需確認）</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">5.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">非住宅用地建案廣告資訊內不可備註任何可作為住家用字樣</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">6.不可刊登BTO或客制化建案</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">7.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">文字不可備註聯絡電話</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">8.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">不可重複刊登</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">9.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">建案官網：不可放競品或非591網站的網址（如房地王、住展等），可直接刪除</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">10.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">事務所指建築設計、起造廠指投資建設、營造廠指營造公司</span></p></td></tr></tbody></table><p><span style="color: #333333; text-indent: 28px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-04 16:59:09</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="color: #333333; font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;">3.1.2審核流程</span></strong></p><p><strong><span style="color: #333333; font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"></span></strong></p><table width="811" cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td rowspan="7" style="border: 1px solid windowtext; padding: 0px 7px;" align="center" width="62" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">新刊登審核作業流程</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" align="center" width="62" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">刊登身份</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="90" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">審核狀態</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="129" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">審核人</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="174" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">審核內容</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="294" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">處理流程</span></p></td></tr><tr><td rowspan="2" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="62" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">編輯刊登</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="90" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">編輯狀態</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="129" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">由編輯自行審核</span></p></td><td rowspan="2" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="174" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">物件所有內容</span></p></td><td rowspan="2" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="294" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">不管是否符合要求均由編輯自行確認</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="90" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">客服狀態</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="129" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">系統預設已自動審核（客服無需審核編輯刊登的物件）</span></p></td></tr><tr style=";height:24px"><td rowspan="4" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="62" valign="middle" height="24"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">會員刊登</span></p></td><td rowspan="2" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="90" valign="middle" height="24"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">編輯狀態</span></p></td><td rowspan="2" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="129" valign="middle" height="24"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">暫由興興審核</span></p></td><td rowspan="2" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="174" valign="middle" height="24"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">建造執照或使用執照</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="294" valign="middle" height="24"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">符合要求，按審核通過</span></p></td></tr><tr style=";height:24px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="294" valign="middle" height="24"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">不符合要求，記錄問題點，放入待處理</span></p></td></tr><tr style=";height:48px"><td rowspan="2" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="90" valign="middle" height="48"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">客服狀態</span></p></td><td rowspan="2" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="129" valign="middle" height="48"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">系統預設已自動審核（客服無需審核編輯刊登的物件）</span></p></td><td rowspan="2" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="174" valign="middle" height="48"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">身份、真實性、圖片、其他基本資料</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="294" valign="middle" height="48"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">符合要求，按審核通過</span></p></td></tr><tr style=";height:48px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="294" valign="middle" height="48"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">不符合要求，記錄問題點，放入待處理</span></p></td></tr></tbody></table><p><strong><span style="color: #333333; font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><br></span></strong><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-05 13:52:57</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="color: #333333; font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;">3.1.3審核操作</span></strong></p><p><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">進入新客服後臺→客服→新建案審核。請見圖</span></p><p><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">骨幹審核基本資料、客服審核照片、做好記錄放待處理</span></p><p><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;"><img src="/upload/question/20201105/1604555571258354.png" alt="圖片.png" style="width: 805px; height: 471px;" width="805" height="471"><br></span></p><p><strong><span style="color: #333333; font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><br></span></strong><img src="/upload/question/20201105/1604555640323774.png" alt="圖片.png"></p><p><img src="/upload/question/20201105/1604555976848043.png" alt="圖片.png"></p><p><img src="/upload/question/20201105/1604556005475112.png" alt="圖片.png"></p><p><img src="/upload/question/20201105/1604556068619955.png" alt="圖片.png" style="width: 802px; height: 317px;" width="802" height="317"><img src="/upload/question/20201105/1604556109310844.png" alt="圖片.png"></p><p><img src="/upload/question/20201105/1604556140168024.png" alt="圖片.png" style="width: 814px; height: 418px;" width="814" height="418"></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-05 14:30:39</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">3.2 新刊登撥出</span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">3.2.1撥出流程</span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;"></span></strong></p><table width="791" cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td rowspan="5" style="border: 1px solid windowtext; padding: 0px 7px;" align="center" width="100" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">修改撥出作業流程</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" align="center" width="91" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">刊登身份</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" align="center" width="85" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">撥出狀態</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" align="center" width="66" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">撥出人</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" align="center" width="142" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">審核內容</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="307" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">處理流程</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="91" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">編輯刊登</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="85" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">編輯狀態</span></p></td><td rowspan="4" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="66" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">客服</span></p></td><td rowspan="4" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="142" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">物件所有內容</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="307" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">直接修改成功，做好記錄，並審核通過，由星星每天檢查後再按最終的審核通過</span></p></td></tr><tr style=";height:12px"><td rowspan="3" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="91" valign="middle" height="12"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">會員刊登</span></p></td><td rowspan="3" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="85" valign="middle" height="12"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">客服狀態</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="307" valign="top" height="12"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">需重新核實建照資料，提交給星星重新核實，無誤直接審核通過，有誤重新走待處理流程</span></p></td></tr><tr style=";height:12px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="307" valign="top" height="12"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">會員要求自行修改，則跟會員約定修改時間，逾期則關閉廣告</span></p></td></tr><tr style=";height:12px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="307" valign="top" height="12"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">若為編輯刊登的物件，則做好記錄，並提交給編輯群處理，無需跟蹤</span></p></td></tr><tr style=";height:12px"><td colspan="6" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="791" valign="top" height="12"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">PS</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">：以上撥出流程若無人接聽均只跟蹤兩天，兩天無人接聽則發手機簡訊通知關閉廣告</span></p></td></tr></tbody></table><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">&nbsp;</span></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">3.2.2撥出操作</span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;"></span></strong></p><p><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">進入新客服後臺→客服→新建案審核。請見圖</span></p><p><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;"><img src="/upload/question/20201105/1604557830738690.png" alt="圖片.png" style="width: 808px; height: 403px;" width="808" height="403"><br></span></p><p><img src="/upload/question/20201105/1604557858286528.png" alt="圖片.png" style="width: 815px; height: 432px;" width="815" height="432"></p><p><img src="/upload/question/20201105/1604557889512860.png" alt="圖片.png" style="width: 808px; height: 385px;" width="808" height="385"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;"></span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-05 14:41:49</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="color: #333333; font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;">4、新建案修改</span></strong></p><p><strong><span style="color: #333333; font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;">4.1新建案修改審核規範</span></strong></p><p><strong><span style="color: #333333; font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"></span></strong></p><table width="747" cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px;" width="142" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">一級分類</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="400" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">二級分類</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="142"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">會員身份</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="400" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">1</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">仲介身份不可刊登廣告</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">2.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">註冊的建設及代銷公司與該案場不符不可刊登廣告</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="142"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">建築執照</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="400" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">1.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">投資建設、建築設計、營造公司名稱要一致</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">2.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">建造、使照要必填其中一項，若未取得建照不可刊登</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">3.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">結構工程、棟戶規劃、樓層規劃、用途規劃、土地分區、停車方式、基地面積、建蔽率要核對一致</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">4.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">當土地分區為工業或商業用時，用途不可為住家；當樓棟規劃有店面或商鋪時，用途需為住商用</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">5.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">當有多張建照或使照時，建蔽率可為60%的標準值，無需計算平均值</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">6.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">當有多張建照或使照時，基地面積需加總後除3.3057計算出坪數</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">7.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">當查到建照或使照未詳細標準車位時，則為前院停車</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="142"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">圖片</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="400"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">封面：</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">1</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">、必須為外觀圖，若無外觀圖則按以下優先順序（優先順序：成屋外觀&gt;示意圖外觀&gt;現場結構體&gt;接待中心&gt;基地照片）<br> &nbsp; 2、預推案可以沒有封面<br> &nbsp; 3、若為3D圖可備註僅供參考字樣，若非3D圖則不可備註任何文字<br> &nbsp; 4、封面圖兩邊不能留白</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">廣告宣傳圖：建案宣傳的圖片儘量要求4張以上，若達不到不強制</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">3D</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">示意圖：包含模型照、外觀示意圖、室內空間示意圖共儘量5張以上，若達不到不強制</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">格局圖：全區的平面圖</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">建案實景圖：新成屋必須5張以上、預售屋必須3張以上(不同角度、遠近)</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">周邊環境圖：機能、交通、學區、綠地等必須7張照片以上</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">樣品屋/實品屋：各空間照片共儘量8張以上，若達不到不強制</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">平面家配圖：</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">1</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">、平面圖需符合建照用途（如規劃住家當成店面使用）</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">2</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">、平面圖不可違法二次施工</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">交通位置圖：以清晰易懂為原則</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">不可一圖多用</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">圖片需真實有效</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">需按照正確類別上傳相應的圖片，若有誤需協助移到正確位置</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">所有圖片上不可備註非保護電話外的聯絡電話</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">大小：最大支持10M，比例4:3</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">&nbsp;</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="142"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">資料內容</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="400" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">1.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">已完銷之建案不可刊登</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">2.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">無效建案名稱不可刊登</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">3.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">建案名稱內添加其他描述性的文字不可刊登，直接編輯掉無關的文字</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">4.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">單價填寫錯誤（別墅透天因沒有價格區間，無需確認）</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">5.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">非住宅用地建案廣告資訊內不可備註任何可作為住家用字樣</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">6.不可刊登BTO或客制化建案</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">7.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">文字不可備註聯絡電話</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">8.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">不可重複刊登</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">9.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">建案官網：不可放競品或非591網站的網址（如房地王、住展等），可直接刪除</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">10.</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">事務所指建築設計、起造廠指投資建設、營造廠指營造公司</span></p></td></tr></tbody></table><p><br></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">4.2審核流程（審核操作頁面目前由骨幹團隊審核，暫不分享）</span></strong><strong><span style="color: #333333; font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"></span></strong></p><p><strong><span style="color: #333333; font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"></span></strong></p><table align="center" width="791" cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td rowspan="3" style="border: 1px solid windowtext; padding: 0px 7px;" align="center" width="100" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">修改審核作業流程</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" align="center" width="91" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">刊登身份</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" align="center" width="85" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">審核狀態</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" align="center" width="66" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">審核人</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" align="center" width="142" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">審核內容</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="307" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">處理流程</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="91" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">編輯刊登</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="85" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">編輯狀態</span></p></td><td rowspan="2" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="66" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">興興</span></p></td><td rowspan="2" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="142" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">物件所有內容</span></p></td><td rowspan="2" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="307" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">符合要求，直接審核通過</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">不符合要求，記錄問題點，放入待處理</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="91" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">會員刊登</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="85" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">客服狀態</span></p></td></tr></tbody></table><p><strong><span style="color: #333333; font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"></span></strong><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-05 14:48:38</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">4.3修改撥出</span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">4.3.1修改撥出流程</span></strong><strong><br></strong></p><table width="791" cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td rowspan="5" style="border: 1px solid windowtext; padding: 0px 7px;" align="center" width="100" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">修改撥出作業流程</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" align="center" width="91" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">刊登身份</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" align="center" width="85" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">撥出狀態</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" align="center" width="66" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">撥出人</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" align="center" width="142" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">審核內容</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" align="center" width="307" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">處理流程</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="91" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">編輯刊登</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="85" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">編輯狀態</span></p></td><td rowspan="4" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="66" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">客服</span></p></td><td rowspan="4" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="142" valign="middle"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">物件所有內容</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="left" width="307" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">直接修改成功，做好記錄，並審核通過，由星星每天檢查後再按最終的審核通過</span></p></td></tr><tr style=";height:12px"><td rowspan="3" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="91" valign="middle" height="12"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">會員刊登</span></p></td><td rowspan="3" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="center" width="85" valign="middle" height="12"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">客服狀態</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="left" width="307" valign="top" height="12"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">需重新核實建照資料，提交給星星重新核實，無誤直接審核通過，有誤重新走待處理流程</span></p></td></tr><tr style=";height:12px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="left" width="307" valign="top" height="12"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">會員要求自行修改，則跟會員約定修改時間，逾期則關閉廣告</span></p></td></tr><tr style=";height:12px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" align="left" width="307" valign="top" height="12"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">若為編輯刊登的物件，則做好記錄，並提交給編輯群處理，無需跟蹤</span></p></td></tr><tr style=";height:12px"><td colspan="6" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" align="left" width="791" valign="top" height="12"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">PS</span><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">：以上撥出流程若無人接聽均只跟蹤兩天，兩天無人接聽則發手機簡訊通知關閉廣告</span></p></td></tr></tbody></table><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">&nbsp;</span></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;">4.3.2修改撥出操作</span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><img src="/upload/question/20201105/1604558908230245.png" alt="圖片.png" style="width: 820px; height: 432px;" width="820" height="432"><br></span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><img src="/upload/question/20201105/1604558968579153.png" alt="圖片.png" style="width: 815px; height: 414px;" width="815" height="414"><br></span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><img src="/upload/question/20201105/1604559125944050.png" alt="圖片.png"><br></span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><img src="/upload/question/20201105/1604559147510084.png" alt="圖片.png"></span></strong><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-05 14:53:39</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">5、新建案撥出話術</span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;"></span></strong></p><table width="757" cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px;" width="757" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:red">開頭問候語：先撥打案場電話，再撥註冊手機</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="757" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">您好！這邊是591客服中心~請問在591有刊登建案XXX嗎，請問是和您確認資料嗎？</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="757" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:red">確認話術</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="757" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">我們編輯用建照查詢到登記的資料為建蔽率是：48.2%、基地面積是：206坪，地上樓層是：3樓（根據聯絡內容具體確認），請問可以幫您修改嗎</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="757" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:red">非會員本人接聽</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="757" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">1.</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">瞭解，請問可以幫忙轉告一下刊登者，讓他來電591確認下廣告資料嗎</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">2.</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">好的，請問他有別的聯絡電話嗎</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="757" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:red">要求撥打其他人聯絡問題，例如秘書</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="757" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">1.</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">好的，麻煩您提供聯絡電話，他怎麼稱呼呢，客服聯絡他確認唷</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="757" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:red">資料不一致</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="757" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">1.</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">請問是有變更數據嗎</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">2.</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">不好意思，因建案目前刊登在591網站，需以登記的資料為准，若您後續有變更資料後，您可以提供變更後的資料給我們，我們再按變更後的資料刊登，需請您先填寫3樓，請問建蔽率、基地面積、樓層可以幫您修改嗎？</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">3.</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">很抱歉，若您需按您的資料填寫，需請您提供建照，我們可以按照您建照的資料改，但若您登記的建照尚未變更，我們需以查到的資料為准唷？</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="757" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:red">多個建照號</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="757" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">1.</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">查詢建案填寫3棟3戶住家，請問是有多個建照號嗎，需請您提供給客服核實唷</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="757" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:red">照片問題</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="757" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">1.</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">因建案要求封面不能有任何問題，需是建物整棟外觀圖，可以是3D圖，請問方便更換封面嗎</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">2.</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">請問方便上傳三張實景圖，需請您至案場現場拍一下現況外觀圖唷</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">3.</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">不好意思，雖然房屋正在蓋，您也需要上傳目前正在蓋的照片唷，需要外觀圖三張的，你可以上傳三張基地的照片，不同角度的就可以了，若不能上傳的話，建案是暫無法刊登的唷</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">4.</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">周邊環境圖可以拍攝周邊街道商圈的圖片，但不能是google街景圖唷</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">5.</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">查看照片有拍到聯絡電話，因網站有提供轉接電話，照片和特色都不能備註電話，需請您把電話馬賽克後重新上傳唷</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="757" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:red">約定處理時間，未處理需關閉</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="757" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">1.</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">瞭解，那我們可以約定個處理時間嗎，請您最晚下禮拜一上傳好五張實景圖,七張周邊環境圖，若您未能上傳，我們就先不跟進了，先做關閉，請您準備好照片再聯絡我們審核唷</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="757" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:red">其他注意事項：</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">談話過程中的語調、語氣、語速、服務態度、服務速度</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">嚴禁電話線上與客戶有不耐煩的語氣，談話過程中保持熱誠親切的態度、柔和的音調、語調高低起伏、聲音大小適中、談話速度適中、條理分明邏輯清楚、實際解決問題。</span></p></td></tr></tbody></table><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;"><br></span></strong><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-05 14:54:05</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">6、新建案簡訊發送模板</span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;"></span></strong></p><table style="width: 799px;" width="964" cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px;" width="964" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">【591提醒您】因建案H124303聯絡均無人接聽，客服中心現已將廣告H124303做關閉，若有疑問請您聯絡客服02-55722000</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="964" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">【591提醒您】因H123456與H234567為重複的建案，客服中心現已將廣告H123456做關閉，若有疑問請您聯絡客服02-55722000</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="964" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">【591提醒您】因仲介身份無法刊登新建案廣告，客服中心現已將廣告H123456做關閉，若有疑問請您聯絡客服02-55722000</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="964" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">【591提醒您】因建案H123456填寫營造公司不一致，客服無法聯絡到您，故客服中心現已將廣告H117075做關閉，若有疑問請您聯絡客服02-55722000</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="964" valign="top"><p style=";text-autospace:none"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">【591提醒您】因H123456圖片不完整，實景圖非實拍圖片，客服中心現已將廣告H110136做關閉，若有疑問請您聯絡客服02-55722000</span></p></td></tr></tbody></table><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;"></span></strong><br></p></div></div> <div><span class="button">我要編輯</span></div></li></ul> <h3 class="more-answer">我要補充</h3> <div style=""><div id="editor_64553452762332870" class="edui-default" style=""><div id="edui1" class="edui-editor  edui-default" style="width: 100%; z-index: 999;"><div id="edui1_toolbarbox" class="edui-editor-toolbarbox edui-default"><div id="edui1_toolbarboxouter" class="edui-editor-toolbarboxouter edui-default"><div class="edui-editor-toolbarboxinner edui-default"><div id="edui2" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui2&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui3" class="edui-box edui-button edui-for-fullscreen edui-default"><div id="edui3_state" onmousedown="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui3_body" unselectable="on" title="全屏" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui3&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui3&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui4" class="edui-box edui-button edui-for-source edui-default"><div id="edui4_state" onmousedown="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui4_body" unselectable="on" title="源代码" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui4&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui4&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui5" class="edui-box edui-button edui-for-undo edui-default"><div id="edui5_state" onmousedown="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui5_body" unselectable="on" title="撤销" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui5&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui5&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui6" class="edui-box edui-button edui-for-redo edui-default"><div id="edui6_state" onmousedown="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui6_body" unselectable="on" title="重做" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui6&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui6&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui7" class="edui-box edui-button edui-for-simpleupload edui-default"><div id="edui7_state" onmousedown="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui7_body" unselectable="on" title="单图上传" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui7&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui7&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui8" class="edui-box edui-button edui-for-imageleft edui-default"><div id="edui8_state" onmousedown="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui8_body" unselectable="on" title="左浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui8&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui8&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui9" class="edui-box edui-button edui-for-imageright edui-default"><div id="edui9_state" onmousedown="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui9_body" unselectable="on" title="右浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui9&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui9&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui10" class="edui-box edui-button edui-for-imagecenter edui-default"><div id="edui10_state" onmousedown="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui10_body" unselectable="on" title="居中" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui10&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui10&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui11" class="edui-box edui-separator  edui-default"></div><div id="edui12" class="edui-box edui-button edui-for-date edui-default"><div id="edui12_state" onmousedown="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui12_body" unselectable="on" title="日期" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui12&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui12&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui13" class="edui-box edui-button edui-for-time edui-default"><div id="edui13_state" onmousedown="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui13_body" unselectable="on" title="时间" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui13&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui13&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui14" class="edui-box edui-button edui-for-horizontal edui-default"><div id="edui14_state" onmousedown="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui14_body" unselectable="on" title="分隔线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui14&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui14&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui15" class="edui-box edui-separator  edui-default"></div><div id="edui16" class="edui-box edui-splitbutton edui-for-inserttable edui-default"><div title="插入表格" id="edui16_state" onmousedown="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui16_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onArrowClick();"></div></div></div></div><div id="edui19" class="edui-box edui-button edui-for-deletetable edui-default"><div id="edui19_state" onmousedown="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui19_body" unselectable="on" title="删除表格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui19&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui19&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui20" class="edui-box edui-button edui-for-insertrow edui-default"><div id="edui20_state" onmousedown="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui20_body" unselectable="on" title="前插入行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui20&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui20&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui21" class="edui-box edui-button edui-for-insertcol edui-default"><div id="edui21_state" onmousedown="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui21_body" unselectable="on" title="前插入列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui21&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui21&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui22" class="edui-box edui-button edui-for-mergeright edui-default"><div id="edui22_state" onmousedown="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui22_body" unselectable="on" title="右合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui22&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui22&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui23" class="edui-box edui-button edui-for-mergedown edui-default"><div id="edui23_state" onmousedown="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui23_body" unselectable="on" title="下合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui23&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui23&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui24" class="edui-box edui-button edui-for-deleterow edui-default"><div id="edui24_state" onmousedown="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui24_body" unselectable="on" title="删除行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui24&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui24&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui25" class="edui-box edui-button edui-for-deletecol edui-default"><div id="edui25_state" onmousedown="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui25_body" unselectable="on" title="删除列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui25&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui25&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui26" class="edui-box edui-button edui-for-splittorows edui-default"><div id="edui26_state" onmousedown="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui26_body" unselectable="on" title="拆分成行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui26&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui26&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui27" class="edui-box edui-button edui-for-splittocols edui-default"><div id="edui27_state" onmousedown="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui27_body" unselectable="on" title="拆分成列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui27&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui27&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui28" class="edui-box edui-button edui-for-splittocells edui-default"><div id="edui28_state" onmousedown="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui28_body" unselectable="on" title="完全拆分单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui28&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui28&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui29" class="edui-box edui-button edui-for-mergecells edui-default"><div id="edui29_state" onmousedown="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui29_body" unselectable="on" title="合并多个单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui29&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui29&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui36" class="edui-box edui-button edui-for-edittable edui-default"><div id="edui36_state" onmousedown="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui36_body" unselectable="on" title="表格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui36&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui36&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui41" class="edui-box edui-button edui-for-edittd edui-default"><div id="edui41_state" onmousedown="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui41_body" unselectable="on" title="单元格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui41&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui41&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui42" class="edui-box edui-separator  edui-default"></div><div id="edui43" class="edui-box edui-menubutton edui-for-lineheight edui-default"><div title="行间距" id="edui43_state" onmousedown="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui43_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onArrowClick();"></div></div></div></div><div id="edui52" class="edui-box edui-menubutton edui-for-rowspacingtop edui-default"><div title="段前距" id="edui52_state" onmousedown="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui52_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onArrowClick();"></div></div></div></div><div id="edui59" class="edui-box edui-menubutton edui-for-rowspacingbottom edui-default"><div title="段后距" id="edui59_state" onmousedown="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui59_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onArrowClick();"></div></div></div></div></div><div id="edui66" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui66&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui67" class="edui-box edui-combox edui-for-fontfamily edui-default"><div title="字体" id="edui67_state" onmousedown="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui67_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onButtonClick(event, this);">字体</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onArrowClick();"></div></div></div></div><div id="edui80" class="edui-box edui-combox edui-for-customstyle edui-default"><div title="自定义标题" id="edui80_state" onmousedown="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui80_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onButtonClick(event, this);">自定义标题</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onArrowClick();"></div></div></div></div><div id="edui86" class="edui-box edui-combox edui-for-paragraph edui-default"><div title="段落格式" id="edui86_state" onmousedown="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui86_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onButtonClick(event, this);">段落格式</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onArrowClick();"></div></div></div></div><div id="edui95" class="edui-box edui-combox edui-for-fontsize edui-default"><div title="字号" id="edui95_state" onmousedown="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui95_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onButtonClick(event, this);">字号</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onArrowClick();"></div></div></div></div><div id="edui110" class="edui-box edui-button edui-for-link edui-default"><div id="edui110_state" onmousedown="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui110_body" unselectable="on" title="超链接" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui110&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui110&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui111" class="edui-box edui-button edui-for-indent edui-default"><div id="edui111_state" onmousedown="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui111_body" unselectable="on" title="首行缩进" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui111&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui111&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui112" class="edui-box edui-button edui-for-bold edui-default"><div id="edui112_state" onmousedown="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui112_body" unselectable="on" title="加粗" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui112&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui112&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui113" class="edui-box edui-splitbutton edui-for-forecolor edui-default edui-colorbutton"><div title="字体颜色" id="edui113_state" onmousedown="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui113_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui113_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onArrowClick();"></div></div></div></div><div id="edui116" class="edui-box edui-splitbutton edui-for-backcolor edui-default edui-colorbutton"><div title="背景色" id="edui116_state" onmousedown="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui116_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui116_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onArrowClick();"></div></div></div></div><div id="edui119" class="edui-box edui-button edui-for-italic edui-default"><div id="edui119_state" onmousedown="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui119_body" unselectable="on" title="斜体" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui119&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui119&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui120" class="edui-box edui-button edui-for-underline edui-default"><div id="edui120_state" onmousedown="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui120_body" unselectable="on" title="下划线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui120&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui120&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui121" class="edui-box edui-button edui-for-strikethrough edui-default"><div id="edui121_state" onmousedown="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui121_body" unselectable="on" title="删除线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui121&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui121&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui122" class="edui-box edui-button edui-for-superscript edui-default"><div id="edui122_state" onmousedown="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui122_body" unselectable="on" title="上标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui122&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui122&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui123" class="edui-box edui-button edui-for-subscript edui-default"><div id="edui123_state" onmousedown="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui123_body" unselectable="on" title="下标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui123&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui123&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui124" class="edui-box edui-button edui-for-removeformat edui-default"><div id="edui124_state" onmousedown="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui124_body" unselectable="on" title="清除格式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui124&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui124&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui125" class="edui-box edui-button edui-for-blockquote edui-default"><div id="edui125_state" onmousedown="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui125_body" unselectable="on" title="引用" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui125&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui125&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui126" class="edui-box edui-button edui-for-pasteplain edui-default"><div id="edui126_state" onmousedown="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui126_body" unselectable="on" title="纯文本粘贴模式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui126&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui126&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui127" class="edui-box edui-menubutton edui-for-insertorderedlist edui-default"><div title="有序列表" id="edui127_state" onmousedown="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui127_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onArrowClick();"></div></div></div></div><div id="edui140" class="edui-box edui-menubutton edui-for-insertunorderedlist edui-default"><div title="无序列表" id="edui140_state" onmousedown="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui140_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onArrowClick();"></div></div></div></div><div id="edui147" class="edui-box edui-button edui-for-cleardoc edui-default"><div id="edui147_state" onmousedown="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui147_body" unselectable="on" title="清空文档" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui147&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui147&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div></div></div></div><div id="edui1_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;"><div id="edui1_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui1&quot;].showWordImageDialog();">点击上传</div><div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui1&quot;].hideToolbarMsg();">x</div><div id="edui1_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div><div style="height:0;overflow:hidden;clear:both;" class="edui-default"></div></div><div id="edui1_message_holder" class="edui-editor-messageholder edui-default"></div></div><div id="edui1_iframeholder" class="edui-editor-iframeholder edui-default" style="width: 100%; height: 300px; z-index: 999; overflow: hidden;"><iframe id="ueditor_0" width="100%" height="100%" frameborder="0" src="javascript:void(function(){document.open();document.write(&quot;<!DOCTYPE html><html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}
body{margin:8px;font-family:sans-serif;font-size:13px;}p{margin:5px 0;}</style><link rel='stylesheet' type='text/css' href='/static/ueditor/themes/iframe.css'/></head><body class='view' ></body><script type='text/javascript'  id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant0'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);</script></html>&quot;);document.close();}())"></iframe></div><div id="edui1_bottombar" class="edui-editor-bottomContainer edui-default"><table class="edui-default"><tbody class="edui-default"><tr class="edui-default"><td id="edui1_elementpath" class="edui-editor-bottombar edui-default"><div class="edui-editor-breadcrumb">元素路径:</div></td><td id="edui1_wordcount" class="edui-editor-wordcount edui-default">字数统计</td><td id="edui1_scale" class="edui-editor-scale edui-default" style="display: none;"><div class="edui-editor-icon edui-default"></div></td></tr></tbody></table></div><div id="edui1_scalelayer" class="edui-default"></div></div></div> <div class="edit-btn"><button type="button" class="button">提交回答</button> <label data-v-0fe26aab="" for="commonCheckbox" class="checkbox"><input data-v-0fe26aab="" type="checkbox" id="commonCheckbox" name="commonCheckbox"> <i data-v-0fe26aab=""></i> 需要彈窗，請勾選</label></div></div></div> <div class="index-right"><div class="right-bk"><h3 class="com-title"><a href="javascript:;">我的最爱</a></h3> <div class="item-box clearfix"><span>暫無數據</span></div></div> <div class="hotTag right-bk"><h3 class="com-title">热门标签</h3> <div class="hotTag-box item-box clearfix"><p>無數據</p></div></div> <div class="right-bk rank"><h3 class="com-title"><a href="javascript:;">冠軍排行榜</a></h3> <ul><li>暫無數據</li></ul></div> <div class="activeUser right-bk"><h3 class="com-title">活跃用户</h3> <div class="activeUser-box"><a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a></div></div> <img src="data:image/png;base64,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" class="to-top"></div></div> <!----> <!----> <!----> <div class="page-cnt footer"><p>T5客服知识库<span>|</span>zsk.591.com.tw</p></div></div></div><script type="text/javascript" src="/js/manifest.f192b.js"></script><script type="text/javascript" src="/js/vendor.17570.js"></script><script type="text/javascript" src="/js/app.58254.js"></script><div id="edui_fixedlayer" class="edui-default" style="position: fixed; left: 0px; top: 0px; width: 0px; height: 0px;"><div id="edui148" class="edui-popup  edui-bubble edui-default" onmousedown="return false;" style="display: none;"> <div id="edui148_body" class="edui-popup-body edui-default"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank" class="edui-default"></iframe> <div class="edui-shadow edui-default"></div> <div id="edui148_content" class="edui-popup-content edui-default">  </div> </div></div></div><div id="nprogress"><div class="bar" role="bar" style="transform: translate3d(-38.1976%, 0px, 0px); transition: 500ms;"><div class="peg"></div></div></div></body></html>