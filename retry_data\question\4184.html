<html class=" "><head><meta charset="utf-8"><title>591客服知識庫</title><link rel="stylesheet" href="/static/font/font-awesome.min.css"><link href="/css/app.e5aa4.css" rel="stylesheet"><script type="text/javascript" charset="utf-8" async="" src="/js/questionDetail.d0a6b.js"></script><style type="text/css">.que-detail-page .index-left .button{width:74px;height:32px;line-height:32px;font-size:13px;margin-right:10px}.que-detail-page .index-left .button:hover{color:#fff}.que-detail-page .detail-box{padding:20px 30px;border:1px solid #e5e5e5;border-radius:4px}.que-detail-page .detail-box h3{font-size:18px;color:#333;margin-bottom:10px}.que-detail-page .detail-box h3 span{color:#f77d24}.que-detail-page .detail-box .add-fav{cursor:pointer}.que-detail-page .d-b-menu{margin:15px 0 25px;font-size:13px;color:#999}.que-detail-page .d-b-menu b{display:inline-block;font-weight:400;margin:0 10px}.que-detail-page .d-b-menu .r{float:right;color:#f77d24}.que-detail-page .d-b-menu .r .fa{margin-right:5px;font-size:16px}.que-detail-page .d-b-cnt{margin-bottom:25px;font-size:13px;color:#666}.que-detail-page .submit-ask{display:block;margin:32px auto 0}.que-detail-page .reply-list{margin:30px 0 10px;overflow:hidden}.que-detail-page .reply-list .reply-item{padding:15px 30px 25px;margin-bottom:30px;border-radius:4px;border:1px solid #e5e5e5}.que-detail-page .reply-list .reply-item h4{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;line-height:50px;font-size:13px}.que-detail-page .reply-list .reply-item h4 img{width:40px;height:40px;border-radius:50%}.que-detail-page .reply-list .reply-item h4 .time{color:#999}.que-detail-page .reply-list .reply-item h4 .button{-ms-flex-item-align:center;align-self:center}.que-detail-page .reply-list .reply-item h4 .delete{color:#666;cursor:pointer}.que-detail-page .reply-list .reply-item .reply-detail{padding:20px 0 30px}.que-detail-page .more-answer{font-size:18px;color:#666;margin-bottom:5px}.que-detail-page .editor-content table{width:100%;border:1px solid #ddd}.que-detail-page .editor-content table td,.que-detail-page .editor-content table th{border:1px solid #ddd;padding:2px 8px;line-height:2}.que-detail-page .editor-content img{max-width:100%;vertical-align:text-bottom}.que-detail-page .edit-btn{text-align:center;margin-top:20px}.que-detail-page .edit-btn .edit-cancle{background:#aaa}</style><style type="text/css">.head-info{line-height:26px;background:#f5f5f5;font-size:13px;color:#999}.head-info a{margin-left:20px;color:#333}.head-search{margin-top:22px}.head-search img{float:left}.head-search .srh-box{float:left;margin:20px 0 0 20px}.head-search .srh-box .srh-input{float:left;width:260px;height:38px;line-height:38px;border:2px solid #37a508;border-radius:4px 0 0 4px}.head-search .srh-box a,.head-search .srh-box button{float:left;width:80px;height:38px;line-height:36px;font-size:16px;color:#fff}.head-search .srh-box .srh-btn{border-radius:0 4px 4px 0}.head-search .srh-box .ask-btn{margin-left:10px}.head-nav{float:left;margin:20px 0 0 60px;color:#fff;font-size:16px;line-height:36px}.head-nav a{display:block;height:100%;color:#333}.head-nav li{position:relative;float:left;margin-right:35px}.head-nav .active a,.head-nav li:hover a{color:#30b940}.head-nav .active:after,.head-nav li:hover:after{display:block;content:"";height:3px;width:100%;background:#30b940}.head-pic{width:100%;height:90px;margin-top:20px;background:url(/static/banner.png) top no-repeat}</style><style type="text/css">.top-breadcrumb{height:20px;margin-top:10px;margin-bottom:40px;font-size:13px;color:#666}.top-breadcrumb .t-b-icon{color:#30b940}.top-breadcrumb span{margin-left:8px}.top-breadcrumb span .fa{font-size:16px;margin-left:8px}.top-breadcrumb span:first-child{margin-left:0}.top-breadcrumb .now{color:#30b940}</style><script type="text/javascript" src="/static/ueditor/ueditor.config.js?20210319" id="configScriptTag"></script><script type="text/javascript" src="/static/ueditor/ueditor.all.js?20210319" id="editorScriptTag"></script><style type="text/css">.checkbox[data-v-0fe26aab]{word-spacing:4px;vertical-align:bottom;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.checkbox i[data-v-0fe26aab]{display:inline-block;width:14px;height:14px;border-radius:2px;border:1px solid #ccc;background:#fff;position:relative;top:1px}.checkbox input[data-v-0fe26aab]{opacity:0;width:0;height:0;margin:0}.checkbox input:checked+i[data-v-0fe26aab]{background:#30b940;border-color:#30b940}.checkbox input:checked+i[data-v-0fe26aab]:before{content:"";position:absolute;left:2px;top:1px;width:11px;height:6px;border-bottom:2px solid #fff;border-left:2px solid #fff;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}</style><style type="text/css">.popbox{position:fixed;top:50%;left:50%;z-index:1011;background:#fff;border-radius:6px;overflow:hidden}.popbox h3{font-size:16px;line-height:40px;padding:0 20px}.popbox h3 span{float:right;color:#666;font-size:13px;cursor:pointer}.popbox .pb-cnt{padding:0 20px;font-size:13px}.popbox .pb-btn-area{position:absolute;bottom:0;left:0;z-index:1;width:100%;text-align:right;padding:0 20px 20px 0}.popbox .pb-btn-area button{height:32px;line-height:32px;border:0;background:#fff;cursor:pointer;border-radius:6px;margin-left:15px;padding:0 15px}.popbox .pb-btn-area button.pb-btn-ok{background:#daf3cf;color:#279005}.popbox .pb-btn-area button.pb-btn-ok:hover{background:#279005;color:#fff}</style><style type="text/css">.shadow{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1010;background:#000}</style><style type="text/css">.footer{margin-top:70px;margin-bottom:20px;padding-top:20px;text-align:center;color:#999;border-top:1px solid #f5f5f5}.footer span{margin:0 10px}</style><script src="/static/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript" defer="defer"></script><link href="/static/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet"><script src="/static/ueditor/third-party/codemirror/codemirror.js" type="text/javascript" defer="defer"></script><link rel="stylesheet" type="text/css" href="/static/ueditor/third-party/codemirror/codemirror.css"><script src="/static/ueditor/third-party/zeroclipboard/ZeroClipboard.js" type="text/javascript" defer="defer"></script><style type="text/css">.activeUser,.favorite,.hotTag,.rank{margin-bottom:40px}</style><style type="text/css">.rank .rank-page{height:60px;line-height:60px;font-size:18px;margin-bottom:20px;text-align:center;background:#fafafa;border-radius:5px}.rank ul{padding:0 20px}.rank li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;border-bottom:1px dotted #f5f5f5;line-height:50px}.rank li .flag{position:relative;display:inline-block;width:20px;height:30px;background:#476472;vertical-align:middle;margin-right:12px;color:#fff;text-align:center;line-height:20px;font-size:13px;font-style:normal}.rank li .flag:after{content:"";position:absolute;bottom:-4px;left:-4px;border:14px solid transparent;border-bottom-color:#fff}.rank li:first-child .flag{background:#fb6757}.rank li:nth-child(2) .flag{background:#fac525}.rank li:nth-child(3) .flag{background:#00a9fb}.rank li img{width:36px;height:36px;border-radius:50%;margin-right:12px}</style><style type="text/css">.activeUser .activeUser-box a{display:inline-block;width:64px;height:64px;border-radius:50%;overflow:hidden;margin:0 6px 12px}</style><style type="text/css">.to-top{display:none;position:fixed;bottom:150px;z-index:5;width:50px;height:50px;cursor:pointer}</style></head><body style=""><div id="app" style=""><div class="que-detail-page" style=""><div class="wiki-hd"><div class="head-info"><p class="page-cnt">
      您好，欢迎来到591客服知识库
      <a href="https://www.591.com.tw/admin.php?new_url=https://zsk.591.com.tw">[请登入]</a> <!----></p></div> <div class="page-cnt head-search clearfix"><a href="/" class="router-link-active"><img src="/static/logo.png"></a> <ul class="head-nav clearfix"><li class="router-link-active active"><a href="/">591房屋交易网</a></li> <li class=""><a href="/life">客服充电站</a></li> <li class=""><a href="/notification">通知事项</a></li></ul> <div class="srh-box"><input type="text" placeholder="请输入搜索内容" class="srh-input"> <button type="button" class="button srh-btn">搜索</button> <a href="/ask/591" class="button button-orange ask-btn">提問</a></div></div> <div class="head-pic"></div></div> <div class="top-breadcrumb com-width"><span><a href="/collection/591/1209" class="">客服督導相關<i class="fa fa-angle-double-right"></i></a></span><span><a href="/collection/591/1209/1655" class="">崗位作業指導<i class="fa fa-angle-double-right"></i></a></span><span><strong class="now">问题详情</strong></span></div> <div class="com-width clearfix" style=""><div class="index-left" style=""><div class="detail-box"><h3><!---->客訴處理作業指導書</h3> <p class="clearfix d-b-menu"><span class="l">
            文宇&nbsp;&nbsp;&nbsp;浏览166次&nbsp;&nbsp;&nbsp;2020-07-01 12:21:14
          </span> <span class="r"><span class="add-fav"><i class="fa fa-heart"></i>加入最爱</span></span></p> <div class="d-b-cnt editor-content big-img-parent"><div><p><span style=";font-family:SimSun"></span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、目的</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、適用範圍</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、什麼是客戶投訴</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4、投訴處理流程及規範</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4.1投訴處理流程</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4.2投訴處理規範</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5.判斷</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5.1投訴分類及分級</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5.1.1投訴分類</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5.1.2投訴分級</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5.2投訴判斷標準</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">6、投訴處理</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">6.1錄音分析</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">6.2.討論</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">6.3撥出技巧（含話術案例）</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">7、學習與評估</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">7.1學習規劃</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">7.2評估標準</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">8、投訴整理</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">8.1客訴匯總表</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">8.2投訴報告</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">8.3報告提交時間</span></p><p><span style="font-size:14px;font-family:SimSun"></span><br></p><p><br></p></div></div> <div class="d-b-button"><a href="/edit/591/4184" class="button">编辑</a> <button class="button button-orange">关闭提问</button></div></div> <div class="hr-line"></div> <ul class="reply-list big-img-parent"><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-07-01 17:54:12</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p class="MsoListParagraph" style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:0;text-indent:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: black;">&nbsp; &nbsp; &nbsp;1、<span style="font-style: normal; font-variant: normal; font-weight: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span></span></strong><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: black;">目的</span></strong></span></p><p class="MsoListParagraph" style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:24px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">為了規範投訴處理工作，從而確保客戶的各類投訴能及時、合理的得到解決。</span></p><p class="MsoListParagraph" style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:24px"><strong style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2、 </strong><strong style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">適用範圍</strong></p><p class="MsoListParagraph" style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:24px"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">適用591房屋交易事業部---客服部：所有客戶投訴處理之作業流程</span></p><p class="MsoListParagraph" style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:24px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3、 </span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">什麼是客戶投訴</span></strong></span></p><p class="MsoListParagraph" style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:24px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">一般是因網友對網站的功能、收費、品質、對客服的服務態度等各方面的原因產生不滿，向網站上級部門或者臺灣總部、協力廠商（消基會、政府等）投訴部門反應情況、檢舉，並要求得到相應補償的一種方式。</span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-07-01 17:54:54</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p class="MsoListParagraph" style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:24px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"></span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp;&nbsp;&nbsp; 4、投訴處理流程及規範</span></strong></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp;&nbsp;&nbsp; 4.1投訴處理流程</span></strong></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><br></span></strong></p><p class="MsoListParagraph" style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:24px"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><img src="/upload/question/20200706/1594028701905826.png" alt="圖片.png"></span></strong><img src="/upload/question/20200706/1594028721842417.png" alt="圖片.png"></p><p class="MsoListParagraph" style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:24px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong><br></strong></span></p><p class="MsoListParagraph" style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:24px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong><br></strong></span></p><p class="MsoListParagraph" style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:24px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong><br></strong></span></p><p class="MsoListParagraph" style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:24px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong>4.2</strong><strong>投訴處理規範</strong></span></p><table width="425" cellspacing="0" cellpadding="0"><tbody><tr style=";height:45px" class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px;" width="57" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="50" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="510" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td rowspan="5" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="57"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>1</strong></span></p></td><td rowspan="5" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="50"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">站內：</span></p><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">記錄並回饋使用者需求</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">記錄並回饋使用者需求</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員不滿意解決方案</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">接聽客服</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員回饋的問題，客服已講解但會員不認同，需要升級投訴時</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">記錄會員需求並建回撥，回饋班級主任</span></p></td></tr></tbody></table><p class="MsoListParagraph" style="margin-top:24px;margin-right:0;margin-bottom: 24px;margin-left:0"><strong><span style="font-size:15px;font-family:SimSun">&nbsp;</span></strong></p><table width="425" cellspacing="0" cellpadding="0"><tbody><tr style=";height:45px" class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px;" width="57" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="50" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="510" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td rowspan="5" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="57"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>&nbsp;</strong></span></p><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>2</strong></span></p></td><td rowspan="5" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="50"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">總部：</span></p><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">聯絡用戶初步處理</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">聯絡用戶初步處理</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員不滿意解決方案或權益受損</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">當天處理591行政群高級專員</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員通過電話或上門回饋的問題至臺灣同事，轉過來的事務</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">當天處理591行政群高級專員需及時回復行政並回撥給會員處理問題，會員若不接受則需記錄需求回饋班主任</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">注：若會員已來電客服中心處理中，則有當天處理行政區高級專員提交給上一位跟進的客服回撥處理問題。</span></p></td></tr></tbody></table><p class="MsoListParagraph" style="margin-top:24px;margin-right:0;margin-bottom: 24px;margin-left:0"><strong><span style="font-size:15px;font-family:SimSun">&nbsp;</span></strong></p><table width="425" cellspacing="0" cellpadding="0"><tbody><tr style=";height:45px" class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px;" width="57" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="50" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="510" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td rowspan="5" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="57"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>&nbsp;</strong></span></p><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>3</strong></span></p></td><td rowspan="5" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="50"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">協力廠商：</span></p><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">判斷是否為服務類</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">判斷是否為服務類</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">核實是否為服務類錯誤導致協力廠商投訴</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">產品支援專員</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">收到協力廠商（政府、警察局等公文）需591配合核實相關資料時</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">產品支援專員依照公文內容，結合刊登的物件判斷是否屬於服務類問題或造成會員收到重大損失的</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">若是，轉督導聯絡處理；若否，（至編號7）則由產品支援專員回復公文</span></p></td></tr></tbody></table><p class="MsoListParagraph" style="margin-top:24px;margin-right:0;margin-bottom: 24px;margin-left:0"><strong><span style="font-size:15px;font-family:SimSun">&nbsp;</span></strong></p><table width="425" cellspacing="0" cellpadding="0"><tbody><tr style=";height:45px" class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px;" width="57" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="50" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="510" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td rowspan="5" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="57"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>&nbsp;</strong></span></p><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>4</strong></span></p></td><td rowspan="5" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="50"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">聽錄音並評級</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">結合錄音分享判斷屬於哪一類投訴並評級</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">依照投訴分級</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服主任</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">收到客服回饋/行政群高級專員回饋會員問題及需求時</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服主任需依照電話升級標準對錄音分析的結果評等級將問題轉交二線專員或督導處理</span></p></td></tr></tbody></table><p class="MsoListParagraph" style="margin-top:24px;margin-right:0;margin-bottom: 24px;margin-left:0"><strong><span style="font-size:15px;font-family:SimSun">&nbsp;</span></strong></p><table width="425" cellspacing="0" cellpadding="0"><tbody><tr style=";height:45px" class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px;" width="57" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="50" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="510" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td rowspan="4" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="57"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>&nbsp;</strong></span></p><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>5</strong></span></p></td><td rowspan="4" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="50"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">聯絡用戶處理</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">聯絡用戶處理</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">依照投訴分級</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">二線專員</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">二線專員收到客服主任提到的問題及錄音分析，與客服主任討論處理方式，達成一致後，聯絡會員傳達處理方案，若會員接受則結束，若會員不接受則需回饋主任並提交督導再次聯絡處理。</span></p></td></tr></tbody></table><p class="MsoListParagraph" style="margin-top:24px;margin-right:0;margin-bottom: 24px;margin-left:0"><strong><span style="font-size:15px;font-family:SimSun">&nbsp;</span></strong></p><table width="425" cellspacing="0" cellpadding="0"><tbody><tr style=";height:45px" class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px;" width="57" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="50" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="510" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td rowspan="4" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="57"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>&nbsp;</strong></span></p><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>6</strong></span></p></td><td rowspan="4" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="50"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">聽錄音</span></p><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">並聯絡用戶處理</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">聽錄音並聯絡用戶處理</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">依照投訴分級</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">督導</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:24px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、 客服主任依照投訴分級評級11級111級的問題時，或二線專員聯絡仍無法解決時</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、 協力廠商投訴：產品支援專員判斷為服務類問題時</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、 督導需結合錄音進行分析，並與團隊達成一致的處理方案，並聯絡會員處理。</span></p></td></tr></tbody></table><p class="MsoListParagraph" style="margin-top:24px;margin-right:0;margin-bottom: 24px;margin-left:0"><strong><span style="font-size:15px;font-family:SimSun">&nbsp;</span></strong></p><table width="425" cellspacing="0" cellpadding="0"><tbody><tr style=";height:45px" class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px;" width="57" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="50" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="510" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td rowspan="5" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="57"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>7</strong></span></p></td><td rowspan="5" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="50"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">產品支援專員處理並回函</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">產品支援專員處理並回函</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">收到協力廠商投訴</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">產品支援專員</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">接收協力廠商公文投訴時</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">產品支援專員依照公文內容，進行調取資料並回函</span></p></td></tr></tbody></table><p class="MsoListParagraph" style="margin-top:24px;margin-right:0;margin-bottom: 24px;margin-left:0"><strong><span style="font-size:15px;font-family:SimSun">&nbsp;</span></strong></p><table width="425" cellspacing="0" cellpadding="0"><tbody><tr style=";height:45px" class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px;" width="57" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="50" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="510" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td rowspan="5" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="57"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>8</strong></span></p></td><td rowspan="5" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="50"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">記錄客訴匯總表</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">記錄客訴匯總表</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">記錄客訴問題及解決方案，以便後續作為提升服務品質提供依據</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">二線專員/督導</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">處理好客訴問題時</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="59"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="452"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">處理好客訴問題時，二線專員或督導需及時記錄客訴匯總表，二線專員並于每月月初將文檔提交給督導同步。</span></p></td></tr></tbody></table><p class="MsoListParagraph" style="margin-top:24px;margin-right:0;margin-bottom: 24px;margin-left:0"><strong><span style="font-size:15px;font-family:SimSun">&nbsp;</span></strong></p><p>&nbsp;</p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-07-06 17:48:27</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;word-spacing:0px"><span style="color: black; font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;"></span></p><p style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:0;text-indent:22px"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><strong>5.</strong><strong>判断</strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><strong>&nbsp; &nbsp;5.1</strong><strong>投诉分类与分级</strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><strong>&nbsp;&nbsp; 5.1.1 </strong><strong>投诉分类</strong></span></p><p class="MsoListParagraph" style="margin-left:56px"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">l<span style="font: normal normal normal normal 14px 微软雅黑, Microsoft YaHei;">&nbsp; </span>站内投诉：客户在网站服务渠道（电话、申诉、信箱、在线、APP反馈）中明确提出对产品设计、规则流程、网站稳定/安全性、服务品质不满，且需要得到回应与解决</span></p><p class="MsoListParagraph" style="margin-left:56px"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">l<span style="font: normal normal normal normal 14px 微软雅黑, Microsoft YaHei;">&nbsp; </span>第三方投诉：客户至政府部门提出对591产品设计、规则流程、网站稳定/安全性、服务品质不满，相关单位来函要求回应与解决</span></p><p class="MsoListParagraph" style="margin-left:56px"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">l<span style="font: normal normal normal normal 14px 微软雅黑, Microsoft YaHei;">&nbsp; </span>总部投诉：客户致电或亲访台湾公司，明确提出对产品设计、规则流程、网站稳定/安全性、服务品质不满</span></p><p style="margin: 5px 0px; font-variant-ligatures: normal; font-variant-caps: normal; text-align: start; -webkit-text-stroke-width: 0px; word-spacing: 0px;"><span style="font-family: 微软雅黑, Microsoft YaHei; color: black; font-size: 14px;"><br></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><strong>&nbsp;&nbsp;&nbsp; 5.1.2</strong><strong>投诉分级</strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong>&nbsp;&nbsp; </strong></span></p><table cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px;" width="71"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong>级别</strong></span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="388"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong>说明</strong></span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="145"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong>处理人</strong></span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="71"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">I级</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="388"><p class="MsoListParagraph" style="margin-left:28px;text-align:justify;text-justify:inter-ideograph"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">l<span style="font: normal normal normal normal 14px 微软雅黑, Microsoft YaHei;">&nbsp; &nbsp; </span>对产品设计、规则流程、稳定性/安全性问题提出抱怨，仅影响体验/感受，判断站台无过失且并无对客户产生重大影响之客诉<span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><br></span></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="145"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">班级安排</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="71"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">II级</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="388"><p class="MsoListParagraph" style="margin-left:28px;text-align:justify;text-justify:inter-ideograph"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">l<span style="font: normal normal normal normal 14px 微软雅黑, Microsoft YaHei;">&nbsp; </span>因产品设计、规则流程、稳定性/安全性问题，产生严重不良后果之客诉（站台有过失/经济损失/行政处罚等）</span></p><p class="MsoListParagraph" style="margin-left:28px;text-align:justify;text-justify:inter-ideograph"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">l<span style="font: normal normal normal normal 14px 微软雅黑, Microsoft YaHei;">&nbsp; &nbsp; </span>对某位客服的服务品质不满</span></span></span></p><p class="MsoListParagraph" style="margin-left:28px;text-align:justify;text-justify:inter-ideograph"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">l<span style="font: normal normal normal normal 14px 微软雅黑, Microsoft YaHei;">&nbsp; </span>客诉者身份为VIP、大客户</span></p><p class="MsoListParagraph" style="margin-left:28px;text-align:justify;text-justify:inter-ideograph"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">l<span style="font: normal normal normal normal 14px 微软雅黑, Microsoft YaHei;">&nbsp; </span>I级客诉处理失败，升级</span></p><p class="MsoListParagraph" style="margin-left:28px;text-align:justify;text-justify:inter-ideograph"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">l<span style="font: normal normal normal normal 14px 微软雅黑, Microsoft YaHei;">&nbsp; &nbsp; </span>第三方投诉中的服务类客诉</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="145"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">客服督导</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="71"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">III级</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="388"><p class="MsoListParagraph" style="margin-left:28px;text-align:justify;text-justify:inter-ideograph"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">l<span style="font: normal normal normal normal 14px 微软雅黑, Microsoft YaHei;">&nbsp; &nbsp; </span>投诉内容涉及到法律、法规层面，且评估站台有缺失或风险之客诉</span></p><p class="MsoListParagraph" style="margin-left:28px;text-align:justify;text-justify:inter-ideograph"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">l<span style="font: normal normal normal normal 14px 微软雅黑, Microsoft YaHei;">&nbsp; &nbsp; </span>客户客诉到总部，并产生严重不良影响之客诉（情绪极其不稳定，对公司同事造成严重困扰）</span></p><p class="MsoListParagraph" style="margin-left:28px;text-align:justify;text-justify:inter-ideograph"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">l<span style="font: normal normal normal normal 14px 微软雅黑, Microsoft YaHei;">&nbsp; &nbsp; </span>客诉到公司高层</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="145"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">客服督导/客服主任/客服主管共同决策</span></p></td></tr></tbody></table><p style="margin-left:28px"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: red;">注：</span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: red;">1</span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: red;">、</span></strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: red;">会员对申请处理的结果仍不满意，坚持转主管，则需与会员确认好诉求，会员希望的处理方式是什么，若确认无法满足，也无法沟通，则提交客服主任升级电话。</span></span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, Microsoft YaHei; color: red; font-size: 14px;">2、二线专员/督导回拨处理好24小时内，依照T5-客诉汇总表做好记录，并将结果反馈班主任或产品支持专员</span></p><p style="text-indent:29px"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><strong><br></strong></span></p><p style="text-indent:29px"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><strong>5.2</strong><strong>投诉判断标准</strong></span></p><table width="612" cellspacing="0" cellpadding="0"><tbody><tr style=";height:27px" class="firstRow"><td colspan="4" style="border: 1px solid windowtext; background: white none repeat scroll 0% 0%; padding: 0px 2px;" width="612" valign="bottom" height="27" nowrap=""><p style="text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black;">投诉判断成立的标准</span></strong></span></p></td></tr><tr style=";height:31px"><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; background: white none repeat scroll 0% 0%; padding: 0px 2px;" width="119" height="31" nowrap=""><p style="text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black;">分类</span></strong></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; background: white none repeat scroll 0% 0%; padding: 0px 2px;" width="427" height="31" nowrap=""><p style="text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black;">说明</span></strong></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 2px;" width="66" height="31" nowrap=""><p style="text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black;">结果</span></strong></span></p></td></tr><tr style=";height:54px"><td rowspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 2px;" width="47" height="54"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">服务缺失</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 2px;" width="72" height="54" nowrap=""><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">服务态度</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 2px;" width="427" height="54"><p><span style="font-family: 微软雅黑, Microsoft YaHei; color: black; font-size: 14px;">1、语气语调起伏大不耐烦，敷衍，措辞过激，争执，凶、辱骂会员（包含线上回复）<br> &nbsp; 2、与会员争执主动挂机或断线未主动回电</span></p></td><td rowspan="2" style="border-color: currentcolor windowtext black currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 2px;" width="66" height="54" nowrap=""><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">成立</span></p></td></tr><tr style=";height:110px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 2px;" width="72" height="110" nowrap=""><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">服务差错</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 2px;" width="427" height="110"><p><span style="font-family: 微软雅黑, Microsoft YaHei; color: black; font-size: 14px;">1、提供错误/回复错误方案<br> &nbsp; 2、与会员讨论政治相关话题<br> &nbsp; 3、未按照规范提供服务<br> &nbsp; 4、未按处理流程处理问题<br> &nbsp; 5、已培训或辅导过的内容，严重缺乏沟通技巧</span></p></td></tr><tr style=";height:67px"><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 2px;" width="47" height="67"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">网站</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 2px;" width="72" height="67"><p><span style="font-family: 微软雅黑, Microsoft YaHei; color: black; font-size: 14px;">BUG、故障、<br> &nbsp; 规则、设计、流程</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 2px;" width="427" height="67"><p><span style="font-family: 微软雅黑, Microsoft YaHei; color: black; font-size: 14px;">在无服务缺失的前提下，将记录并反馈用户意见与建议，积极协助用户解决问题（如修复Bug/故障，评估优化流程，对会员造成损失的，评估给予补偿等）</span></p><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">注：有服务缺失，则按服务缺失判断处理</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 2px;" width="66" height="67" nowrap=""><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">不成立</span></p></td></tr></tbody></table><p class="MsoListParagraph" style="margin-left: 0px;"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong>&nbsp;</strong></span></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-07-06 17:50:35</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:74px"><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">6、投诉处理<br><br>6.1 录音分析</span></strong><span style="font-family: 微软雅黑, Microsoft YaHei;"><br><br><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1）投诉的问题分析：全面、完整、并详细记录<br><br>2）投诉时间分析：记录客诉时间、及时核实，并在规定的时间主动回复会员<br><br>3）投诉人分析：分析会员的性格特征及情绪变化，采取应对措施<br><br>4）责任分析：分析产生问题的根源是网站、客服、会员，提前思考解决方案<br><br>5）投诉主要要求分析：了解会员的主要问题及对会员造成的损失，结合会员提出的需求是否匹配。<br><br>6）投诉解决情况分析：分析会员反馈的问题及沟通技巧，并制定好下一步改善</span><br><br><br><strong>6.2 讨论</strong><br><br><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">客诉专员根据客户的问题及需求，与主任确认并讨论：回复的业务知识点及沟通技巧，客诉专员联络与会员沟通，仍无法达成一致，则记录会员需求，与团队再次讨论达成一致，并迅速有效解决。</span></span><strong><span style="font-size:15px;font-family:SimSun"><br></span></strong></p><p style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:74px"><strong><span style="font-size:15px;font-family:SimSun"><br></span></strong></p><p style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:74px"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><strong>6.3 </strong><strong>拨出流程</strong></span></p><p style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:74px"></p><table cellspacing="0" cellpadding="0" width="649"><tbody><tr style=";height:42px" class="firstRow"><td width="83" style="border: 1px solid windowtext; padding: 0px 2px;" height="42"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">投诉拨出步骤</span></p></td><td width="131" nowrap="" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 2px;" height="42"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">话术</span></p></td><td width="95" nowrap="" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 2px;" height="42"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">规范</span></p></td><td width="125" nowrap="" style="border-top: 1px solid windowtext; border-left: none; border-bottom: 1px solid windowtext; border-right: none; padding: 0px 2px;" height="42"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">技巧</span></p></td><td width="100" nowrap="" style="border: 1px solid windowtext; padding: 0px 2px;" height="42"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">注意事项</span></p></td></tr><tr style=";height:51px"><td width="83" rowspan="3" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="51"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">开头语</span></p></td><td width="131" rowspan="2" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="51"><p><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">您好，这里是591客服中心，请问是**先生/小姐吗？我是591客服主管，敝姓**，很抱歉打扰您</span></p></td><td width="95" rowspan="3" style="border-top: none; border-right: none; border-left: none; border-image: initial; border-bottom: 1px solid windowtext; padding: 0px 2px;" height="51"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">心平气和、欢快、亲切、有礼貌、抑扬顿挫，有亲和力</span></p></td><td width="125" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="51"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">语气温和、语调抑扬顿挫，语速适中</span></p></td><td width="100" rowspan="3" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="51"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">及时作出反应，并在规定时间内进行有效处理，不能及时完成的问题应按时跟进进展情况，并适时通知客户</span></p></td></tr><tr style=";height:92px"><td width="125" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="92"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">说明职位，姓氏，拉近距离，让会员感觉到你是他需要的有能力解决他问题的人</span></p></td></tr><tr style=";height:108px"><td width="131" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="108"><p><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">您好，这里是591客服中心，请问是**先生/小姐吗？我是591当班主管，敝姓**，很抱歉打扰您</span></p></td><td width="125" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="108"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">多用尊称及礼貌用词，让会员感受专业的，更好一层的贴心服务，让他感受到尊重。</span></p></td></tr><tr style=";height:62px"><td width="83" rowspan="3" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="62"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">复述</span></p></td><td width="131" rowspan="3" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="62"><p><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">关于您于**时间反馈的有****的问题，很抱歉造成您的困扰了，现联络您做回复，请问您现在方便接听吗？可能需要耽误您一些时间。</span></p></td><td width="95" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="62"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">复述的问题：简洁全面</span></p></td><td width="125" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="62"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">复述的问题需完整，依照会员的问题区分重点、梳理逻辑顺序。</span></p></td><td width="100" rowspan="3" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="62"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">专业度：以专业标准要求自己，维护公司专业形象。</span></p></td></tr><tr style=";height:90px"><td width="95" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="90"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">态度诚恳，真心诚意</span></p></td><td width="125" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="90"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">致歉：真诚、诚恳、语气语调需要更有力量感，客服主管的致歉更能让会员感受尊重，及处理事情的态度。</span></p></td></tr><tr style=";height:95px"><td width="95" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="95"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">确认会员是否方便接听 </span></p></td><td width="125" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="95"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">投诉的处理时间均比较长，与会员确认是否方便接聽，尊重他的時間</span></p></td></tr><tr style=";height:125px"><td width="83" rowspan="3" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="125"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">澄清</span></p></td><td width="131" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="125"><p><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">规则/流程<br> &nbsp; 关于您反馈照片不合理的问题，不好意思造成您的困扰，网站这样管理的目的是*****，因为之前出现****问题，造成*****困扰，还请您多多谅解唷</span></p></td><td width="95" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="125"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">陈述事实：会员问题、原因、结果</span></p></td><td width="125" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="125"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">表示歉意，主动承认错误，全面收集业务知识点，从不同的角度跟维度进行解释，获取会员的理解</span></p></td><td width="100" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="125"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">保证诚信原则，做到实事求是，有根有据，</span></p></td></tr><tr style=";height:136px"><td width="131" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="136"><p><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">设计<br> &nbsp; 关于您反馈***功能的问题，我们有与专员进一步确认，该功能的设计的目的是***，物件展示的规则是****，不好意思造成您困扰</span></p></td><td width="95" rowspan="2" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="136"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">若会员不认同，需照顾会员的情绪，采取低姿态、承认错误，及时安抚，真诚道歉，让客户恢复理智的状态再分析、解决问题。</span></p></td><td width="125" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="136"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">通过提出开放性的问题，引导客户讲述事实，提供资料</span></p></td><td width="100" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="136"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">从人性角度出发，尽可能多给予客户方便，多为客户着想 </span></p></td></tr><tr style=";height:170px"><td width="131" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="170"><p><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">态度<br> &nbsp; 关于您反馈****的问题，关于您反馈的***问题，我们有做核实，确实客服这边有需要不断提升的部分，后续客服中心将会做在岗教育训练，造成您的不便，还请多多见谅唷。</span></p></td><td width="125" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="170"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">当客户讲完整个事情过程后，采用封闭式的提问方式，总结问题的关键</span></p></td><td width="100" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="170"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">调整心态：不是客人有问题，是客人有一个问题需要解决，先解决心情，在处理事情</span></p></td></tr><tr style=";height:66px"><td width="83" rowspan="2" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="66"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">倾听</span></p></td><td width="131" rowspan="2" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="66"><p><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">是、了解、好的</span></p></td><td width="95" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="66"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">认真倾听：让会员先说，及时响应</span></p></td><td width="125" rowspan="2" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="66"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">及时回应并记录，针对她的问题进行总结、复述、并解释</span></p></td><td width="100" rowspan="2" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="66"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">同理心、尊重会员，不打断，不抢话，及时响应</span></p></td></tr><tr style=";height:72px"><td width="95" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="72"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">对会员体恤、尊重、耐心倾听，坚决避免争辩、打断客户。</span></p></td></tr><tr style=";height:187px"><td width="83" nowrap="" rowspan="3" style="border-top: none; border-left: 1px solid windowtext; border-bottom: none; border-right: 1px solid windowtext; padding: 0px 2px;" height="187"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">方案讨论</span></p></td><td width="131" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="187"><p><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">无法认同：不好意思唷，关于您反馈的****问题，，因为目前网站考虑****的部分，可能暂时无法****处理，我这边将记录下来，会反馈相关专员进行合理评估，看看后续是否能够做调整（优化）客服建议您*******。</span></p></td><td width="95" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="187"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">会员不认同则当做建议记录后续考虑优化</span></p></td><td width="125" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="187"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">再次表示歉意，说明公司的补偿机制，让会员了解您能做什么，委婉拒绝</span></p></td><td width="100" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="187"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">让会员了解您能做什么，坚定立场的同时不能激怒会员</span></p></td></tr><tr style=";height:272px"><td width="131" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="272"><p><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">要求补偿委婉拒绝：<br> &nbsp; 很抱歉，请问您的需求是什么呢？可能暂时无法按您的需求做补偿，这个部分网站慎重评估考虑，造成您的不便还请多多见谅。</span></p></td><td width="95" rowspan="2" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="272"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">当提出补偿时，可依照网站的补偿机制（因网站原因导致未铺光，则补偿未铺光的时间）坚定立场，礼貌复述委婉拒绝</span></p></td><td width="125" rowspan="2" style="border-top: none; border-bottom: none; border-left: none; border-image: initial; border-right: 1px solid windowtext; padding: 0px 2px;" height="272"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">同理心，需站在会员的角度考虑，并尊重及感谢他反馈的建议</span></p></td><td width="100" rowspan="2" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="272"><p><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">不承诺能力以外的事情，不轻易承诺结果。处理结果应认真履行、关注结果、跟踪回访。</span></p></td></tr><tr style=";height:146px"><td width="131" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="146"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">不接受不补偿的：<br> &nbsp; 很抱歉，关于您提出的***补偿需求，可能不在网站的补偿范围唷，我们也需要遵守网站的补偿机制。</span></p></td></tr><tr style=";height:119px"><td width="83" rowspan="2" style="border-top: 1px solid windowtext; border-left: 1px solid windowtext; border-bottom: none; border-right: none; padding: 0px 2px;" height="119"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">反饋</span></p></td><td width="131" rowspan="2" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="119"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">仍不认同：<br> &nbsp; 不好意思，先生，您看这样可以好，关于这个问题我这边在与团队商议下在回复您，造成您的不便请多多见谅唷。<br> &nbsp; <br> &nbsp; 好的，这个问题我这边会尽快做确认，确认好之后第一时间回复您结果唷，请问您什么时间方便联络呢？</span></p></td><td width="95" rowspan="2" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="119"><p><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">若解释2次会员均无法接受，需了解会员的需求？（由会员先提要求）无法达到则记录再与团队讨论</span></p></td><td width="125" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 2px;" height="119"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">了解会员的需求，若需求已超出补偿范围，委婉拒绝，并说明网站的处理方式，礼貌重复。</span></p></td><td width="100" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="119"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">主动征求客户的解决方案</span></p></td></tr><tr style=";height:125px"><td width="125" style="border-top: none; border-bottom: none; border-left: none; border-image: initial; border-right: 1px solid windowtext; padding: 0px 2px;" height="125"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">若会员仍不满意，需想在会员前面，记录会员的需求，合理评估对会员造成的影响，并与团队共同讨论</span></p></td><td width="100" style="border-top: none; border-bottom: none; border-left: none; border-image: initial; border-right: 1px solid windowtext; padding: 0px 2px;" height="125"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">根据客户的要求，给出解决方案，若不同意，则需与团队再次讨论，并迅速有效解决，直到客户满意</span></p></td></tr><tr style=";height:102px"><td width="83" rowspan="3" style="border: 1px solid windowtext; padding: 0px 2px;" height="102"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">結束</span></p></td><td width="131" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="102"><p><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">不好意思造成您的困扰了，非常感谢您的反馈与建议，我们将尽快确认，确认好在第一时间回复您结果。</span></p></td><td width="95" rowspan="3" style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid windowtext; padding: 0px 2px;" height="102"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客诉专员需要表达：表示歉意，再次感谢，向客户表决心</span></p></td><td width="125" rowspan="3" style="border-top: 1px solid windowtext; border-left: none; border-bottom: 1px solid black; border-right: 1px solid windowtext; padding: 0px 2px;" height="102"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">再次对客户带来的不便表示歉意<br> &nbsp; 再次感谢客户对网站的支持与信任<br> &nbsp; 向客户表决心，让客户知道我们会努力改进工作</span></p></td><td width="100" rowspan="3" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 2px;" height="102"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">做好收尾的寒暄，歉意、感谢、后续网站如何做的决心</span></p></td></tr><tr style=";height:59px"><td width="131" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="59"><p><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">网站后续将会对此部分慎重考虑，非常感谢您对591的支持</span></p></td></tr><tr style=";height:70px"><td width="131" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="70"><p><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">网站后续将加强人员的教育培训，不断提升服务质量</span></p></td></tr></tbody></table><p><strong><span style="font-size:13px;font-family: SimSun">&nbsp;</span></strong></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong>5.3.4 </strong><strong>常用的投诉处理话术</strong></span></p><table cellspacing="0" cellpadding="0" width="649"><tbody><tr style=";height:38px" class="firstRow"><td width="82" nowrap="" style="border: 1px solid windowtext; padding: 0px 2px;" height="38"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">分类</span></p></td><td width="66" nowrap="" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 2px;" height="38"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">学习阶段</span></p></td><td width="142" nowrap="" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 2px;" height="38"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">回复要点</span></p></td><td width="359" nowrap="" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 2px;" height="38"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">回复话术</span></p></td></tr><tr style=";height:101px"><td width="82" rowspan="3" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="101"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">一则广告刊登一间房屋信息/照片</span></p></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="101"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">试用期</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="101"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br> &nbsp; 1、说明刊登规则<br> &nbsp; 2、解释备注的资料</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="101"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、非常抱歉照成您的困扰唷，网站刊登规则有说明一则广告刊登一间房屋信息/照片，如果您有两间不同的房屋需请您刊登2则广告唷。<br> &nbsp; 2、您刊登的物件R***特殊有备注：******的两个不同房间的照片/资讯。</span></p></td></tr><tr style=";height:104px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="104"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">二星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="104"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、否认是多间照片技巧<br> &nbsp; 4、建议</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="104"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、客服查询到您上传的第*张照片，跟第*张照片，他的门是不同方向，他的装潢风格是不同的，他的卫浴设备也不同，冷气的位置不一样，窗户开立的方向不同唷；<br> &nbsp; 4、客服建议您可以拍摄同一间不同角度的照片或者房屋周边环境上传唷</span></p></td></tr><tr style=";height:150px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="150"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">三星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="150"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5、这样做的原因<br> &nbsp; 6、抱怨网站规矩多<br> &nbsp; 7、若会员不认同规则，情绪也比较大，当建议记录并反馈专员考虑</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="150"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5、很抱歉造成您的不便，可能每个网站均有不同的经营方式<br> &nbsp; 6、可能591网站相对其他网站规则比较严格一些，网站也希望您能更快的租出去，但并非针对您个人特别规定唷，还请您多多谅解.<br> &nbsp; 7、真的很抱歉造成您的困扰了，关于您反馈的问题，客服将记录下来呈报相关专员后续考虑优化唷。非常感谢您对591的支持与信任</span></p></td></tr><tr style=";height:88px"><td width="82" rowspan="3" style="border-top: none; border-left: 1px solid windowtext; border-bottom: 1px solid black; border-right: 1px solid windowtext; padding: 0px 2px;" height="88"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">无关照片规则<br> &nbsp; （照片超过1/5）</span></p></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="88"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">试用期</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="88"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">照片超过1/5<br> &nbsp; 1、说明照片违规原因<br> &nbsp; 2、解释刊登规则</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="88"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、很抱歉，客服查看您的照片有PS文字超过照片的1/5唷<br> &nbsp; 2、网站为了提供更好的照片，展示给房客查看，有对针对照片做规范，麻烦您后续上传符合的房屋照片唷。</span></p></td></tr><tr style=";height:96px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="96"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">二星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="96"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、反馈1/5的计算方法不明确<br> &nbsp; 4、为什么之前可以</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="96"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、不好意思先生，关于照片不超过1/5的规则及标准，是依照照片平均分5等分计算，在帮助中心-规则/检举-刊登规则内亦有上传标准的案例，造成您的困扰，请多多见谅<br> &nbsp; 4、请问您是指哪一笔物件有上传的照片呢，客服来核实看看唷。</span></p></td></tr><tr style=";height:230px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="230"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">三星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="230"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5、不认同规则反馈所有都不要P文字，需回复这样考虑的原因，并告知网站规则也有根据其他站台的照片质量比对。<br> &nbsp; 6、不想受到电话一直打扰</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="230"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5、非常感谢您的建议，照片规则的制定有结合到其他站台参考，也有结合到用户的接受程度进行调整，因为部分会员怕照片被别人借用，想PS文字进行预防，所有我们也有结合广大会员建议，可以PS文字，不能超过1/5影响照片直观效果即可，如果网站不考虑用户建议可能会出现很多很多问题，所以规则的制定我们其实有考虑到部分中介反馈照片盗用的问题进行制定，亦有结合到其他网站的照片质量比对。还请多多见谅唷<br> &nbsp; 6、关于一直收到电话打扰的问题，有说明因会员有付费刊登，审核有问题客服中心均不能擅自做修改或者删除处理，均会电话确认帮修改，造成您的困扰还请多多见谅，客服建议您后续刊登时多多留意唷，若日后刊登的资讯符合网站相关规定，客服中心将不会打扰您唷。</span></p></td></tr><tr style=";height:89px"><td width="82" rowspan="3" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="89"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">无关照片规则<br> &nbsp; （照片上传卡照）</span></p></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="89"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">试用期</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="89"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、说明违规的原因<br> &nbsp; 2、说明刊登规则</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="89"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、很抱歉，客服查看您上传的第几张照片为卡通照<br> &nbsp; 2、网站有相关规则说明，不可上传与房屋无关的人物照或卡通照唷，麻烦您后续上传与房屋相关的图片，造成您的不便还请多多见谅</span></p></td></tr><tr style=";height:139px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="139"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">二星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="139"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、反馈是告诉别人可以养宠物，建议在特色或可养宠物的栏位说明<br> &nbsp; 4、反馈别人的可以，请会员提供物件编号核实，并说明网站均一视同仁处理</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="139"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、不好意思，若先生是想说明房屋可以养宠物，建议您在可养宠物的栏位勾选，或在特色标题备注亦可达到刊登的效果<br> &nbsp; 4、网站均一视同仁管理，请问您查看的物件地址是哪里的呢，或者提供物件编号，客服在详细核实看看唷，非常感谢您对591的支持与监督。</span></p></td></tr><tr style=";height:176px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="176"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">三星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="176"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5、不认同规则，需要告知管控的原因<br> &nbsp; 6、反馈网站管很多，需要告知不管控会出现的问题</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="176"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5、很抱歉先生，网站为了提供更好的找屋环境给房客，有对照片质量做管理，需要上传与房屋相关的图片，或者可以拍周边环境图。<br> &nbsp; 6、网站刊登的广告在全台湾是最多的，若其他会员查看照片可以放，大家都会效仿，这样会导致房客找房时，看到的都是卡通照或者其他人物照等等，对有需求找房的房客角度考虑也是比较困扰，房客可能优先通过照片展示了解房屋的内部环境是否符合需求，然后在进一步约屋主看房，若大家都上传卡通照网站后续无法更好的管理和维护找屋环境。还请多多见谅。</span></p></td></tr><tr style=";height:139px"><td width="82" rowspan="3" style="border-top: none; border-left: 1px solid windowtext; border-bottom: 1px solid black; border-right: 1px solid windowtext; padding: 0px 2px;" height="139"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">填写真实的房屋资讯</span></p></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="139"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">试用期</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="139"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">金额填写不实<br> &nbsp; 1、说明刊登规则<br> &nbsp; 2、并建议会员修改金额</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="139"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、很抱歉，客服查询R***物件金额填写为**元，网站需要以真实的房屋资讯刊登，可能无法填写虚假的金额刊登唷，麻烦您修改<br> &nbsp; 2、客服建议您可以填写自己比较理想的金额刊登，可以在特色备注亦可面洽，或者建议您可了解此地段的租金行情，结合自己的房屋提供的设备装潢等制定一个合理的租金唷，这样也比较方便房屋更快的成交。</span></p></td></tr><tr style=";height:201px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="201"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">二星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="201"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、会员不认同规则，需说明管理的目的<br> &nbsp; 4、说明判断的依据</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="201"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、很抱歉，网站也需要依照相关法律条文经验，若刊登虚假的房屋资讯可能会被网友检举而被罚款，且网站一样需要承担相关的责任，所以网站刊登规则有说明需填写资料的房屋资讯刊登，还请您多多谅解唷<br> &nbsp; 4、假设您作为房客找房子，若找10间有9间房屋的金额都填写虚假，网站不做维护，也会导致房客流失，如果网站没有房客，效果不好，那后续屋主也不会在选择网站刊登广告唷，所以刊登的房屋资讯客服中心均有专员做审核，若结合此区域的刊登行情查看差太多，均会联络刊登者确认，并修改为真实的金额刊登唷。还请您多多配合。</span></p></td></tr><tr style=";height:275px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="275"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">三星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="275"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5、会员威胁若修改资料就告我们侵权，说明网站的服务条款并与相关专员确认</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="275"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5、不好意思先生造成您困扰了，可能每一个网站均有不同的管理方式，非常感谢您能选择591，并在网站刊登广告，我们也希望会员所刊登的物件能通过591更快的成交，如果房客查看到金额差太多，跟自己的预算不符，这样也会失去一个客户的机会唷，若刊登太低价格，房客联络过来发现价格不符，可能他后续就不会相信591而影响刊登效果，或者房客会检举，从而产生不必要的风险唷。可以建议您修改真实的价格刊登更能否顺利成交。<br> &nbsp; ----（不认同）不好意思，您在注册网站已有同意网站的刊登规则、服务条款，还请您多多配合，关于此问题我们在与团队确认看看，确认好在回复您。<br> &nbsp; ---与团队商量解决方案：可取消退点退现金等<br> &nbsp; ---与法务确认解决方案：可我们终止合约等<br> &nbsp; 需要及时反馈团队讨论解决方案。</span></p></td></tr><tr style=";height:113px"><td width="82" nowrap="" rowspan="3" style="border-top: none; border-left: 1px solid windowtext; border-bottom: 1px solid black; border-right: 1px solid windowtext; padding: 0px 2px;" height="113"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">更换房屋资讯</span></p></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="113"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">试用期</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="113"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、确认修改的原因<br> &nbsp; 2、说明刊登规则</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="113"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、您好，有看到您所刊登的物件金额坪数由***改为***，请问是因为什么原因需要修改呢？<br> &nbsp; 2、不好意思唷，因网站规则有说明一则广告刊登一间房屋资讯，刊登中途无法因为效果不好或租掉或无法带看等原因做修改唷。还请您多多见谅</span></p></td></tr><tr style=";height:154px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="154"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">二星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="154"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、若房屋在租售，需刊登之前的房屋资讯不可更换<br> &nbsp; 4、若有其他的房屋资讯需另外刊登一笔</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="154"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、若您目前该房屋若还需要出租，可以继续刊登唷<br> &nbsp; 若没有要出租需要将物件关闭，但需要提醒您关闭时间照常计算，在有效期内若需出租可以在关闭中物件开启唷<br> &nbsp; 4、若您亦有其他房屋需要刊登，麻烦您另外刊登一笔新的物件。可能无法从这个物件资讯直接做修改唷。</span></p></td></tr><tr style=";height:177px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="177"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">三星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="177"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5、说明网站的管理方式，均一视同仁</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="177"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5、很抱歉造成您的困扰，网站规则是针对一个物件刊登30天，而不是您购买的30天的时间，若该物件因为其他原因无法刊登，则无法放在网站上，亦无法更换其他房屋资讯，譬如您在租车行租一辆车，今天因为天气或您个人的原因没有外出，那其实这个租车的费用，也是无法做退还或者更换其他的时间使用唷。很抱歉，网站已有成立10多年的时间，均依照此刊登规则，并非此次因为个人而制定。所有的会员若刊登的物件修改资料后，均会有客服做审核，网站均一视同仁处理，非常感谢您对591的支持与信任。还请多多见谅唷。</span></p></td></tr><tr style=";height:92px"><td width="82" nowrap="" rowspan="3" style="border-top: none; border-left: 1px solid windowtext; border-bottom: 1px solid black; border-right: 1px solid windowtext; padding: 0px 2px;" height="92"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">已成交</span></p></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="92"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">试用期</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="92"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、说明刊登规则不可以更换<br> &nbsp; 2、确认是否成交，若成交帮下架</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="92"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、很抱歉，网站刊登一则广告刊登一间房屋信息，若成交了物件需及时下架，无法更换其他房屋刊登唷。<br> &nbsp; 2、请问这件**地址的房子是已经签约租掉了吗？若租掉则需要将物件下架唷，您看方便客服帮您做成交下架处理吗？</span></p></td></tr><tr style=";height:169px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="169"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">二星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="169"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、反馈时间未到放着，需说明制定成交下架的原因<br> &nbsp; 4、会员反馈我不是透过你们成交，说明非591成交也需下架</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="169"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、不好意思，若房屋广告已成交，广告效果也是有达到，需及时下架唷，网站也是为了刊登数据的真实性，这样房客才会愿意来网站找房子，若刊登资料不真实，会导致房客流失，从而亦会影响房屋刊登的效果。<br> &nbsp; 4、不好意思先生，若您通过其他方式成交，也需要下架，在勾选下架时可选择非591成交，还请您多多配合，因为591网站刊登可能是您选择的其中一种铺光方式来促进房屋更快成交。网站可能无法保证每一笔物件均能透过591成交。</span></p></td></tr><tr style=";height:117px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="117"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">三星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="117"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4、会员反口回复没有成交，已告知备注需要修改，不改会导致其他人效仿，无法管理网站，另后续有房客检举亦会再次联络确认</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="117"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4、若房屋尚未签约完成，建议您可备注已收订，因为不修改备注可能其他会员成交了也认为可以备注已成交，物件不下架，从而导致网站找屋环境会收到影响唷，网站也无法更好的管理好找屋环境，还请您多多见谅，另若后续有房客房客房屋已成交还放着网站上铺光，客服中心也是需要联络您确认。感谢您对网站的支持。</span></p></td></tr><tr style=";height:111px"><td width="82" nowrap="" rowspan="3" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="111"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">物件被关闭流程</span></p></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="111"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">试用期</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="111"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、说明物件关闭的原因<br> &nbsp; 2、确认资料帮修改并立即恢复</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="111"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、很抱歉造成您的困扰，客服查询您所刊登的物件R***因***刊登错误，客服中心有2天联络您均无人接听，为了保证网站刊登资料真实有效，故有做暂时关闭处理<br> &nbsp; 2、请问您正确的资料是***呢，客服来帮忙修改并马上帮您做回复刊登唷</span></p></td></tr><tr style=";height:150px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="150"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">二星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="150"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、若会员反馈关闭时间不合理，需要补偿，关闭的时间可以做补偿<br> &nbsp; 4、若会员不认同流程，可说明关闭的原因是为了保障铺光在网站上的资料真实有效，不认同的部分可以当建议做详细记录</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="150"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、不好意思造成您的困扰，您看这样可以吗，客服与专员确认看看，请您稍等<br> &nbsp; 4、客服与专员申请关闭的时间帮您做延长（关闭一天延长一天，不足一天不做延长），请您稍晚确认看看哟。关于您反馈关闭物件的流程不合理的部分，客服将记录下来转交相关专员后续考虑优化，造成您的困扰还请多多见谅。</span></p></td></tr><tr style=";height:89px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="89"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">三星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="89"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5、会员反馈您们是老大，想怎样就怎样，可以解释错误资讯曝光在网站上会有罚款的风险</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="89"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5、很抱歉造成您的不便，因网站也需要依照相关法规经营，而刊登虚假或错误的资讯若一直曝光在网站上可能会存在罚款的风险唷，客服建议您后续刊登上来及时检查，在刊登的24小时内会员均可以做修改唷。造成您的不便还请多多见谅。</span></p></td></tr><tr style=";height:72px"><td width="82" rowspan="3" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="72"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">账号限制/停权规则</span></p></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="72"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">试用期</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="72"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、帮恢复说明违规停权的原因</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="72"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、非常抱歉造成您使用上不便，客服马上帮您做恢复，请您重新登入即可，查看到违规原因是：****，请您后续多多留意看看唷。</span></p></td></tr><tr style=";height:82px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="82"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">二星</span></p></td><td width="142" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="82"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、要求补偿委婉拒绝</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="82"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">不好意思唷，账号做停权（限制），但您所刊登的物件均有正常铺光在网站上，房客依然可以查阅并联络到您唷，客服现在马上帮您恢复账号，您5分钟重新登入即可，此部分无法做赔偿唷。</span></p></td></tr><tr style=";height:232px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="232"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">三星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="232"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、不认同停权规则，觉得我们没有通知<br> &nbsp; 4、购买套餐的结合有效期可以特殊申请</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="232"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、非常抱歉造成您的困扰，新刊登违规第1-3次发站内简讯通知您<br> &nbsp; 第四次网站会考虑可能您对规则不太了解，会学习规则<br> &nbsp; 第五次限制刊登，第六次才会做停权。<br> &nbsp; 客服查询在****时间，客服专员也有联络跟您说明此问题。还请您后续多多留意下就可以唷<br> &nbsp; 关于您反馈停权电话通知的部分，客服会记录下来提交专员后续考虑优化，给您带来困扰，客服深表歉意。<br> &nbsp; 4、非常抱歉，客服查询您有购买***套餐，其有效期到***时候，时间还是比较长，建议您**时间做刊登即可，为了后续更换的使用网站，建议您多多留意这部分唷。（若快要过期的可以跟主任申请特殊恢复权限）</span></p></td></tr><tr style=";height:109px"><td width="82" nowrap="" rowspan="3" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="109"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">网站功能</span></p></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="109"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">试用期</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="109"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、了解会员反馈的具体哪一个功能不好，测试并记录<br> &nbsp; 2、表示歉意并记录建议后续考虑优化</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="109"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、不好意思造成您的困扰，请问您是在哪一个页面操作哪一个画面觉得功能需要改善呢，客服与您同步操作测试看看唷<br> &nbsp; 2、非常感谢您的建议唷，客服将记录下来反馈相关专员后续考虑优化，请您留意后续的使用唷，感谢您对591的支持与关注</span></p></td></tr><tr style=";height:192px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="192"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">二星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="192"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、建议比较多要求专员回复，需说建议需要评估，可能无法当下立即优化<br> &nbsp; 4、说明网站也会收集大家的建议，为提供更好便利的服务给会员</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="192"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、关于您所反馈的建议客服有做详细记录：<br> &nbsp; 建议1：****<br> &nbsp; 建议2：****<br> &nbsp; 建议3：****<br> &nbsp; 建议4：****<br> &nbsp; 请您放心，客服都详细记录好，网站将进一步评估，若采纳将会设计研发，可能无法立即实现，还需请您留意后续的使用唷。<br> &nbsp; 4、网站亦会结合会员的建议评估考虑，为提供跟方便更优质的服务给会员使用，请您放心关于建议的部分我们一定会慎重评估唷。非常感谢您对591的喜爱与支持</span></p></td></tr><tr style=";height:173px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="173"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">三星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="173"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5、要求给具体的答复时间，可跟踪几天在回复会员<br> &nbsp; 6、需要将建议呈报给主任，并参考主任给的方向回复</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="173"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5、非常感谢您的热心，您看这样可以吗？客服这边会将建议提交，不管结果如何，客服在跟踪，并于***时间回复电话给您唷<br> &nbsp; 6、过几天在回复：<br> &nbsp; 您好，先生，关于您反馈的****建议，客服已提交给相关专员，可能团队需要在评估唷，可能无法承诺您什么时间会进行优化，但请您放心，网站均会结合我们会员的宝贵建议进行考虑唷。请您后续在留意使用的部分。非常感谢您对591的支持及提成的宝贵建议。</span></p></td></tr><tr style=";height:154px"><td width="82" nowrap="" rowspan="3" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="154"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">服务缺失</span></p></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="154"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">试用期</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="154"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、了解会员反馈的问题是什么<br> &nbsp; 2、针对会员反馈的问题表示歉意，并加强人员的教育培训<br> &nbsp; 3、将会员带会业务上来</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="154"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、抱歉造成困扰，请问您遇到的具体问题是什么呢？客服来帮您处理<br> &nbsp; 2、不好意思造成您的困扰，关于客服态度不好的问题，客服会反馈专员核实，并对该客服做教育训练，非常感谢您对591的支持与监督，客服将不断提升自己，给会员提供更优质的服务<br> &nbsp; 3、请问先生业务上是否有其他问题呢，客服来帮您处理唷。</span></p></td></tr><tr style=";height:121px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="121"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">二星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="121"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4、要求赔偿，需要核实并反馈专员，并表示歉意<br> &nbsp; 5、了解会员的需求</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="121"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4、非常抱歉，关于您反馈的***的问题，客服将记录下来转交相关专员核实<br> &nbsp; 在回复您唷，造成您的不便还请多多见谅。<br> &nbsp; 5、请问您的需求是什么呢？关于补偿的部分客服将一起做好记录呈报相关专员考虑，确认好在回复您，请问您什么时间方便联络呢？</span></p></td></tr><tr style=";height:222px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="222"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">三星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="222"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">6、核实录音并说明记录存在问题回复会员<br> &nbsp; 7、了解会员的需求，评估考虑，委婉拒绝<br> &nbsp; 8、无法达到要求则与会员或团队商议</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="222"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">6、您好先生，关于您反馈的问题，我们有核实录音<br> &nbsp; 确实我们在*****地方做的不够好，很抱歉造成您的困扰了，后续我们将会对该客服做教育训练，并不断提升网站的服务品质，非常感谢您的支持与监督<br> &nbsp; 7、关于您反馈补充的部分，可能比较没有办法，因为网站的补偿规则是*****，我代表网站及该客服对您表示歉意，真的很抱歉。请您多多见谅，我们一定会不断加强跟提升服务品质。<br> &nbsp; 8、好的，你看这样可以吗，我们在跟团队商议下，然后在回复电话您唷。请问您什么时间方便联络呢？</span></p></td></tr><tr style=";height:103px"><td width="82" nowrap="" rowspan="3" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="103"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">公文配合</span></p></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="103"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">试用期</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="103"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、了解公文的内容，询问会员的需求<br> &nbsp; 2、记录会员问题反馈主任后在回拨会员</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="103"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、很抱歉造成您的困扰，请问您是指*****的问题，现在有收到公文对吗？<br> &nbsp; 请问您的需求是什么呢？<br> &nbsp; 2、好的了解，关于这个问题客服记录下来与专员确认看看稍后在回复您唷<br> &nbsp; 请问您的什么时候方便联络呢？</span></p></td></tr><tr style=";height:100px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="100"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">二星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="100"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、了解公文的内容与专员确认后在给以指引</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="100"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、不好意思造成您的困扰，这个部分需要麻烦您反馈跟相关发函单位，并请相关单位发公文至我公司，我们收到将如何做查询并提供证明唷，造成您的不便还请多多见谅</span></p></td></tr><tr style=";height:120px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="120"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">三星</span></p></td><td width="142" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="120"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4、反馈需要被罚款，希望网站能证明是系统的原因导致，资料跳掉或刊登错误，需说明网站提供的证明是真实的，无法提供不实资讯，网站也需要承担责任</span></p></td><td width="359" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="120"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4、不好意思造成您的困扰，经核实您所刊登的物件*****，什么时间做修改，什么时间下架等，公文回复网站均按查询结果如实回复，无法做虚假的证据，因为网站也需要负相关责任的。还请多多见谅</span></p></td></tr></tbody></table><p style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:74px"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><strong><br></strong></span><br></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"></span></span><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-07-07 10:21:36</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong>7.</strong><strong>学习与评估</strong></span></p><p style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong>7.1 </strong><strong>学习规划</strong></span></p><table cellspacing="0" cellpadding="0" width="649"><tbody><tr style=";height:40px" class="firstRow"><td width="649" nowrap="" colspan="7" style="border: 1px solid windowtext; padding: 0px 2px;" height="40"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">T5--</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客诉学习规划及评估方式</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">-</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">二星（用时</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">个月）</span></strong></span></p></td></tr><tr style=";height:32px"><td width="54" nowrap="" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="32"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">时间</span></strong></span></p></td><td width="399" nowrap="" colspan="4" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="32"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">学习内容及标准</span></strong></span></p></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="32"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">用时</span></p></td><td width="130" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="32"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">评估标准</span></p></td></tr><tr style=";height:43px"><td width="54" nowrap="" rowspan="3" style="border-top: none; border-left: 1px solid windowtext; border-bottom: 1px solid black; border-right: 1px solid windowtext; padding: 0px 2px;" height="43"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第1周</span></p></td><td width="75" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="43"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">依照学习规划沟通达成共识</span></p></td><td width="125" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="43"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">自学T5-投诉诉处理作业指导书</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="43"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">完成自学作业--如何处理好电话纠纷</span></p></td><td width="66" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="43"><br></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="43"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3H</span></p></td><td width="130" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="43"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">作业70分</span></p></td></tr><tr style=";height:43px"><td width="75" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="43"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">学习T5-投诉诉处理作业指导书</span></p></td><td width="125" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="43"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">学习T5-客诉处理汇总分析文档的编写</span></p></td><td width="94" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="43"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">讲解作业</span></p></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="43"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">电话拨测规则类</span></p></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="43"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3H</span></p></td><td width="130" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="43"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">笔试考80分</span></p></td></tr><tr style=";height:41px"><td width="75" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">电话拨测规则类</span></p></td><td width="125" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">练习写客诉汇总分析文档</span></p></td><td width="94" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><br></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><br></td><td width="66" nowrap="" rowspan="5" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3H/天</span></p></td><td width="130" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">掌握电话纠纷技巧：<br> &nbsp; 开头语技巧、倾听技巧、结束语技巧</span></p></td></tr><tr style=";height:44px"><td width="54" nowrap="" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="44"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第2周</span></p></td><td width="75" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="44"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">电话拨测/实操：修改规则，停权规则</span></p></td><td width="125" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="44"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">练习写客诉汇总分析文档</span></p></td><td width="94" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="44"><br></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="44"><br></td><td width="130" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="44"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">掌握电话纠纷技巧：复述/澄清技巧</span></p></td></tr><tr style=";height:44px"><td width="54" nowrap="" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="44"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第3周</span></p></td><td width="75" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="44"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">电话拨测/实操：网站功能设计、网站流程</span></p></td><td width="125" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="44"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">练习写客诉汇总分析文档</span></p></td><td width="94" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="44"><br></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="44"><br></td><td width="130" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="44"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">掌握电话纠纷技巧：方案讨论技巧</span></p></td></tr><tr style=";height:44px"><td width="54" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="44"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第4周</span></p></td><td width="75" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="44"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">电话拨测：操作失误、服务态度</span></p></td><td width="125" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="44"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">练习写客诉汇总分析文档</span></p></td><td width="94" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="44"><br></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="44"><br></td><td width="130" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="44"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">掌握电话纠纷技巧：反馈技巧</span></p></td></tr><tr style=";height:100px"><td width="54" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="100"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第5-8周</span></p></td><td width="75" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="100"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">处理好自己的电话纠纷问题</span></p></td><td width="125" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="100"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">拨测规则、流程、功能/设计各2通</span></p></td><td width="94" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="100"><br></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="100"><br></td><td width="130" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="100"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">达到二星需掌握的电话纠纷评估标准，转出录音不因沟通技巧：1个。</span></p></td></tr></tbody></table><p style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:0"><strong><span style="font-size:13px;font-family:SimSun">&nbsp;</span></strong></p><table cellspacing="0" cellpadding="0" width="649"><tbody><tr style=";height:38px" class="firstRow"><td width="649" nowrap="" colspan="5" style="border: 1px solid windowtext; padding: 0px 2px;" height="38"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">T5--</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客诉学习规划及评估方式</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">-</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">三星（</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">个月）</span></strong></span></p></td></tr><tr style=";height:34px"><td width="92" nowrap="" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="34"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">时间</span></strong></span></p></td><td width="331" nowrap="" colspan="2" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="34"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">学习内容及标准</span></strong></span></p></td><td width="95" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="34"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">用时</span></p></td><td width="132" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="34"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">评估标准</span></p></td></tr><tr style=";height:34px"><td width="92" nowrap="" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="34"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第1周</span></p></td><td width="180" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="34"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">电话拨测规则类</span></p></td><td width="100" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="34"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">练习写客诉汇总分析文档</span></p></td><td width="95" nowrap="" rowspan="5" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="34"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3H/天</span></p></td><td width="132" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="34"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">掌握电话纠纷技巧：复述/澄清技巧</span></p></td></tr><tr style=";height:39px"><td width="92" nowrap="" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="39"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第2周</span></p></td><td width="180" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="39"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">电话拨测/实操：修改规则，停权规则</span></p></td><td width="100" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="39"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">练习写客诉汇总分析文档</span></p></td><td width="132" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="39"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">掌握电话纠纷技巧：方案讨论技巧</span></p></td></tr><tr style=";height:47px"><td width="92" nowrap="" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="47"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第3周</span></p></td><td width="180" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="47"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">电话拨测/实操：网站功能设计、网站流程</span></p></td><td width="100" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="47"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">练习写客诉汇总分析文档</span></p></td><td width="132" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="47"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">掌握电话纠纷技巧：方案讨论技巧</span></p></td></tr><tr style=";height:41px"><td width="92" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="41"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第4周</span></p></td><td width="180" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">电话拨测：操作失误、服务态度</span></p></td><td width="100" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">练习写客诉汇总分析文档</span></p></td><td width="132" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">掌握电话纠纷技巧：反馈技巧</span></p></td></tr><tr style=";height:69px"><td width="92" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="69"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第5周-8周</span></p></td><td width="180" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="69"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">实操团队转出的电话升级的电话</span></p></td><td width="100" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="69"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">拨测规则、流程、功能/设计各2通</span></p></td><td width="132" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="69"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">达到三星需掌握电话纠纷评估标准<br> &nbsp; 月转出录音为：0；能协助协助团队解决纠纷。</span></p></td></tr></tbody></table><p><br></p><p style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong>7.2 </strong><strong>评估标准</strong></span></p><p style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"></span></p><table cellspacing="0" cellpadding="0"><tbody><tr style=";height:36px" class="firstRow"><td width="337" nowrap="" colspan="3" style="border: 1px solid windowtext; padding: 0px 2px;" height="36"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">学习电话纠纷评估标准</span></strong></span></p></td><td width="50" rowspan="2" style="border-top: 1px solid windowtext; border-left: none; border-bottom: 1px solid black; border-right: 1px solid windowtext; padding: 0px 2px;" height="36"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">是否掌握</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br> &nbsp; </span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">是在括号打√</span></strong></span></p></td><td width="66" nowrap="" rowspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 2px;" height="36"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">试用期</span></strong></span></p><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">掌握</span></strong></span></p></td><td width="50" nowrap="" rowspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 2px;" height="36"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">二星掌握</span></strong></span></p></td><td width="65.33333333333333" nowrap="" rowspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 2px;" height="36"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">三星掌握</span></strong></span></p></td><td style="border: none;" width="2" height="36"><br></td></tr><tr style=";height:36px"><td width="35" nowrap="" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="36"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">项目</span></strong></span></p></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="36"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">分类</span></strong></span></p></td><td width="236" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="36"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">细项说明</span></strong></span></p></td><td style="border: none;" width="0" height="36"><br></td></tr><tr style=";height:37px"><td width="35" nowrap="" rowspan="2" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="37"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">流程</span></p></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="37"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">处理流程</span></p></td><td width="236" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="37"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">清楚投诉处理流程</span></p></td><td width="50" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="37"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">（）</span></p></td><td width="66" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="37"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">√</span></p></td><td width="50" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="37"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">√</span></p></td><td width="65.33333333333333" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="37"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">√</span></p></td><td style="border: none;" width="2" height="37"><br></td></tr><tr style=";height:36px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="36"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">拨出流程</span></p></td><td width="236" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="36"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">熟悉投诉拨出流程</span></p></td><td width="50" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="36"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">（）</span></p></td><td width="66" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="36"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">/</span></p></td><td width="50" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="36"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">√</span></p></td><td width="85" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="36"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">√</span></p></td><td style="border: none;" width="25.333333333333332" height="36"><br></td></tr><tr style=";height:170px"><td width="35" nowrap="" rowspan="8" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="170"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">技巧</span></p></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="170"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">开头语技巧</span></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="170"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">心平气和、欢快、亲切、有礼貌、抑扬顿挫，有亲和力<br> &nbsp; 1、语气温和、语调抑扬顿挫，语速适中<br> &nbsp; 2、说明职位，姓氏，拉近距离，让会员感觉到你是他需要的有能力解决他问题的人<br> &nbsp; 如：我是591客服主管，敝姓**，很抱歉打扰您<br> &nbsp; 3、多用尊称及礼貌用词，让会员感受专业的，更好一层的贴心服务，让他感受到尊重。<br> &nbsp; 如：您、请问、不好意思、抱歉、<br> &nbsp; 4、专业度：以专业标准要求自己，维护公司专业形象。</span></p></td><td width="50" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="170"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（）<br> &nbsp; 2、（）<br> &nbsp; 3、（）<br> &nbsp; 4、（）</span></p></td><td width="66" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="170"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（√）</span></p></td><td width="50" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="170"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（√）<br> &nbsp; 2、（√）<br> &nbsp; 3、（√）<br> &nbsp; 4、（√）</span></p></td><td width="70.33333333333333" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="170"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（√）<br> &nbsp; 2、（√）<br> &nbsp; 3、（√）<br> &nbsp; 4、（√）</span></p></td><td style="border: none;" width="2" height="170"><br></td></tr><tr style=";height:152px"><td width="66" nowrap="" rowspan="2" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="152"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">复述/澄清技巧</span></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="152"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">复述：简洁全面、态度诚恳，真心诚意<br> &nbsp; 1、复述的问题需完整，依照会员的问题区分重点、梳理逻辑顺序。<br> &nbsp; 2、致歉：真诚、诚恳、语气语调需要更有力量感，客服主管的致歉更能让会员感受尊重，及处理事情的态度。<br> &nbsp; 3、投诉的处理时间均比较长，与会员确认是否方便接听，尊重他的时间<br> &nbsp; 如：请问您现在方便接听吗？可能需要耽误您一些时间。</span></p></td><td width="50" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="152"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（）<br> &nbsp; 2、（）<br> &nbsp; 3、（）</span></p></td><td width="66" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="152"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、（√）</span></p></td><td width="50" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="152"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（√）<br> &nbsp; 2、（√）<br> &nbsp; 3、（√）</span></p></td><td width="85" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="152"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（√）<br> &nbsp; 2、（√）<br> &nbsp; 3、（√）</span></p></td><td style="border: none;" width="27.333333333333332" height="152"><br></td></tr><tr style=";height:123px"><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="123"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">澄清：陈述事实：会员问题、原因、结果，需照顾会员的情绪，采取低姿态、承认错误，及时安抚，真诚道歉，让客户恢复理智的状态再分析、解决问题。<br> &nbsp; 1、通过提出开放性的问题，引导客户讲述事实，提供数据<br> &nbsp; 2、当客户讲完整个事情过程后，采用封闭式的提问方式，总结问题的关键<br> &nbsp; 3、保证诚信原则，做到实事求是，有根有据<br> &nbsp; 4、从人性角度出发，尽可能多给予客户方便，多为客户着想<br> &nbsp; 5、调整心态：不是客人有问题，是客人有一个问题需要解决，先解决心情，在处理事情</span></p></td><td width="50" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="123"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（）<br> &nbsp; 2、（）<br> &nbsp; 3、（）<br> &nbsp; 4、（）<br> &nbsp; 5、（）</span></p></td><td width="66" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="123"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（√）<br> &nbsp; 3、（√）</span></p></td><td width="50" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="123"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（√）<br> &nbsp; 3、（√）<br> &nbsp; 4、（√）<br> &nbsp; 5、（√）</span></p></td><td width="85" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="123"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（√）<br> &nbsp; 2、（√）<br> &nbsp; 3、（√）<br> &nbsp; 4、（√）<br> &nbsp; 5、（√）</span></p></td><td style="border: none;" width="0" height="123"><br></td></tr><tr style=";height:112px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="112"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">倾听技巧</span></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="112"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">认真倾听：让会员先说，及时响应，对会员体恤、尊重、耐心倾听，坚决避免争辩、打断客户。<br> &nbsp; 1、及时响应并做详细记录。如：好的、了解、是、是的<br> &nbsp; 2、针对她的问题进行总结、复述、并解释<br> &nbsp; 3、同理心、尊重会员，不打断，不抢话，及时响应</span></p></td><td width="50" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="112"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（）<br> &nbsp; 2、（）<br> &nbsp; 3、（）</span></p></td><td width="66" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="112"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（√）<br> &nbsp; 2、（√）<br> &nbsp; 3、（√）</span></p></td><td width="50" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="112"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（√）<br> &nbsp; 2、（√）<br> &nbsp; 3、（√）</span></p></td><td width="85" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="112"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（√）<br> &nbsp; 2、（√）<br> &nbsp; 3、（√）</span></p></td><td style="border: none;" width="30.33333333333333" height="112"><br></td></tr><tr style=";height:66px"><td width="66" nowrap="" rowspan="2" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="66"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">方案讨论技巧</span></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="66"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">会员不认同则当做建议记录后续考虑优化<br> &nbsp; 1、再次表示歉意，说明公司的补偿机制，让会员了解您能做什么，委婉拒绝</span></p></td><td width="50" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="66"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">（）</span></p></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="66"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">√</span></p></td><td width="50" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="66"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">√</span></p></td><td width="85" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="66"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">√</span></p></td><td style="border: none;" width="32.33333333333333" height="66"><br></td></tr><tr style=";height:126px"><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="126"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">当提出补偿时，可依照网站的补偿机制（因网站原因导致未铺光，则补偿未铺光的时间）坚定立场，礼貌复述委婉拒绝<br> &nbsp; 1、同理心，需站在会员的角度考虑，并尊重及感谢他回馈的建议<br> &nbsp; 2、让会员了解您能做什么，坚定立场的同时不能激怒会员<br> &nbsp; 3、不承诺能力以外的事情，不轻易承诺结果。</span></p></td><td width="50" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="126"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（）<br> &nbsp; 2、（）<br> &nbsp; 3、（）</span></p></td><td width="66" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="126"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（√）<br> &nbsp; 3、（√）</span></p></td><td width="50" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="126"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（√）<br> &nbsp; 3、（√）</span></p></td><td width="85" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="126"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（√）<br> &nbsp; 2、（√）<br> &nbsp; 3、（√）</span></p></td><td style="border: none;" width="0" height="126"><br></td></tr><tr style=";height:66px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="66"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">反馈技巧</span></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="66"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">若解释2次会员均无法接受，需了解会员的需求？（由会员先提要求）无法达到则记录再与团队讨论<br> &nbsp; 1、了解会员的需求，若需求已超出补偿范围，委婉拒绝，并说明网站的处理方式，礼貌重复。<br> &nbsp; 2、若会员仍不满意，需想在会员前面，记录会员的需求，合理评估对会员造成的影响，并与团队共同讨论<br> &nbsp; 3、及时作出反应，并在规定时间内进行有效处理，不能及时完成的问题应按时跟进进展情况，并适时通知客户<br> &nbsp; 4、处理结果应认真履行、关注结果、跟踪回访。</span></p></td><td width="50" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="66"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（）<br> &nbsp; 2、（）<br> &nbsp; 3、（）<br> &nbsp; 4、（）</span></p></td><td width="66" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="66"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、（√）</span></p></td><td width="50" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="66"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（√）<br> &nbsp; 2、（√）<br> &nbsp; 3、（√）<br> &nbsp; 4、（√）</span></p></td><td width="85" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="66"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（√）<br> &nbsp; 2、（√）<br> &nbsp; 3、（√）<br> &nbsp; 4、（√）</span></p></td><td style="border: none;" width="34.33333333333333" height="66"><br></td></tr><tr style=";height:111px"><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="111"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">结束语技巧</span></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="111"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">做好收尾的寒暄，歉意、感谢、后续网站如何做的决心<br> &nbsp; 客诉专员需要表达：表示歉意，再次感谢，向客户表决心<br> &nbsp; 1、再次对客户带来的不便表示歉意<br> &nbsp; 2、再次感谢客户对网站的支持与信任<br> &nbsp; 3、向客户表决心，让客户知道我们会努力改进工作</span></p></td><td width="50" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="111"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（）<br> &nbsp; 2、（）<br> &nbsp; 3、（）</span></p></td><td width="66" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="111"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（√）</span></p></td><td width="50" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="111"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（√）<br> &nbsp; 2、（√）</span></p></td><td width="85" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="111"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、（√）<br> &nbsp; 2、（√）<br> &nbsp; 3、（√）</span></p></td><td style="border: none;" width="36.33333333333333" height="111"><br></td></tr><tr style=";height:36px"><td width="619.3333333333334" colspan="7" rowspan="2" valign="top" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="36"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">二星学习目标：<br> &nbsp; 能处理好自己的电话纠纷问题。良好的沟通技巧：与会员解释并安抚好会员，帮会员处理好问题。电话不用在转出。若转出，则核实电话录音是否已充分解释会员仍不接受。<br> &nbsp; 评估标准：<br> &nbsp; 达到二星需掌握的电话纠纷评估标准，转出录音不因沟通技巧：1个。<br> &nbsp; 注：若均未遇到电话纠纷电话，可通过拨测规则、流程、功能/设计各2通进行评估<br> &nbsp; 若出现因沟通技巧不足的转出电话，需评估是否为学习内的类别，若是，连续出现4次，则重新学习。<br> &nbsp; <br> &nbsp; 三星学习标准：<br> &nbsp; 能处理好台湾窗口提交的电话疑难问题、自己或团队提交的电话纠纷问题。<br> &nbsp; 评估标准：达到三星需掌握电话纠纷评估标准，月转出录音为：0；能协助协助团队解决纠纷。<br> &nbsp; 注：若出现因沟通技巧不足的转出电话，需评估是否为学习内的类别，若是，连续出现2次，则重新学习。</span></p></td><td style="border: none;" width="2" height="36"><br></td></tr><tr style=";height:224px"><td style="border: none;" width="0" height="224"><br></td></tr></tbody></table><p style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:0"><span style="color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">PS：试用期内于转正时评估</span></p><p style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong><br></strong></span><br></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-07-07 10:25:09</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p class="MsoListParagraph" style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:0"><br></p><p class="MsoListParagraph" style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong>8.</strong><strong>投诉整理</strong></span></p><p class="MsoListParagraph" style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:0;text-indent:15px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">8.1</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">客诉处理完毕后的</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">24</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">小时内，将详细记录至客诉记录汇总表</span></strong></span></p><table cellspacing="0" cellpadding="0" width="644"><tbody><tr style=";height:29px" class="firstRow"><td width="644" nowrap="" colspan="20" style="border: 1px solid windowtext; padding: 0px 2px;" height="29"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">T5-</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客诉记录汇总表</span></strong></span></p></td></tr><tr style=";height:22px"><td width="28" nowrap="" rowspan="2" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="22"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">序号</span></p></td><td width="28" nowrap="" rowspan="2" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="22"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">班级</span></p></td><td width="28" rowspan="2" style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid windowtext; padding: 0px 2px;" height="22"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">投诉管道</span></p></td><td width="28" rowspan="2" style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid windowtext; padding: 0px 2px;" height="22"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客诉时间</span></p></td><td width="21" rowspan="2" style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid windowtext; padding: 0px 2px;" height="22"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客诉级别</span></p></td><td width="236" nowrap="" colspan="7" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="22"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客诉原因</span></p></td><td width="274" nowrap="" colspan="8" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="22"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">处理结果</span></p></td></tr><tr style=";height:41px"><td width="25" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">会员身份</span></p></td><td width="25" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">会员ID</span></p></td><td width="28" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">会员问题</span></p></td><td width="28" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">业务类型</span></p></td><td width="25" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">是否听录音</span></p></td><td width="28" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客诉类型</span></p></td><td width="25" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客诉关键点</span></p></td><td width="25" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">服务缺失</span></p></td><td width="28" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">建议</span></p></td><td width="47" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">处理结果</span></p></td><td width="28" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">改善方案</span></p></td><td width="25" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">是否成立</span></p></td><td width="25" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">会员是否接受</span></p></td><td width="28" nowrap="" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">提交人</span></p></td><td width="28" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="41"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">最终处理人</span></p></td></tr><tr style=";height:23px"><td width="28" nowrap="" valign="bottom" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 2px;" height="23"><br></td><td width="28" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="23"><br></td><td width="28" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="23"><br></td><td width="28" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="23"><br></td><td width="21" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="23"><br></td><td width="25" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="23"><br></td><td width="25" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="23"><br></td><td width="28" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="23"><br></td><td width="28" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="23"><br></td><td width="25" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="23"><br></td><td width="28" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="23"><br></td><td width="25" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="23"><br></td><td width="25" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="23"><br></td><td width="28" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="23"><br></td><td width="47" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="23"><br></td><td width="28" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="23"><br></td><td width="25" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="23"><br></td><td width="25" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="23"><br></td><td width="28" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="23"><br></td><td width="28" nowrap="" valign="bottom" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 2px;" height="23"><br></td></tr></tbody></table><p class="MsoListParagraph" style="margin-left:0"><span style="color: #C00000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">注：客服督导每月分析、汇报客诉情况，并针对异常问题提出解决建议，年终总结时，需分析年度客诉情况，并制定下年相关工作计划</span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-07-07 10:29:04</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin-top:16px;margin-right:0;margin-bottom: 16px;margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong>8.2</strong><strong>客诉专员每月需整理《客服部投诉案例分析》，并制作《投诉总结报告》，模板参见“客服資料</strong><strong>\</strong><strong>報告</strong><strong>\</strong><strong>客訴報告”。</strong></span></p><p><img src="/upload/question/20200707/1594088885580743.png" alt="image.png"></p><p><br></p><p><img src="/upload/question/20200707/1594088919339150.png" alt="image.png"></p><p><br></p><p class="MsoListParagraph" style="margin-top:21px;margin-right:0;margin-bottom: 21px;margin-left:24px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong>8.3</strong><strong>报告呈交时间</strong></span></p><p class="MsoListParagraph" style="margin-top:21px;margin-right:0;margin-bottom: 21px;margin-left:24px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">公司管理部每月10号前会检查前一月的《投诉总结报告》，请提交负责人“唐永红”审阅。</span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li></ul> <h3 class="more-answer">我要補充</h3> <div style=""><div id="editor_7900215429445945" class="edui-default" style=""><div id="edui1" class="edui-editor  edui-default" style="width: 100%; z-index: 999;"><div id="edui1_toolbarbox" class="edui-editor-toolbarbox edui-default"><div id="edui1_toolbarboxouter" class="edui-editor-toolbarboxouter edui-default"><div class="edui-editor-toolbarboxinner edui-default"><div id="edui2" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui2&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui3" class="edui-box edui-button edui-for-fullscreen edui-default"><div id="edui3_state" onmousedown="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui3_body" unselectable="on" title="全屏" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui3&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui3&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui4" class="edui-box edui-button edui-for-source edui-default"><div id="edui4_state" onmousedown="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui4_body" unselectable="on" title="源代码" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui4&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui4&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui5" class="edui-box edui-button edui-for-undo edui-default"><div id="edui5_state" onmousedown="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui5_body" unselectable="on" title="撤销" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui5&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui5&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui6" class="edui-box edui-button edui-for-redo edui-default"><div id="edui6_state" onmousedown="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui6_body" unselectable="on" title="重做" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui6&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui6&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui7" class="edui-box edui-button edui-for-simpleupload edui-default"><div id="edui7_state" onmousedown="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui7_body" unselectable="on" title="单图上传" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui7&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui7&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"><iframe style="display: block; width: 20px; height: 20px; overflow: hidden; border: 0px; margin: 0px; padding: 0px; position: absolute; top: 0px; left: 0px; opacity: 0; cursor: pointer;"></iframe></div></div></div></div></div><div id="edui8" class="edui-box edui-button edui-for-imageleft edui-default"><div id="edui8_state" onmousedown="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui8_body" unselectable="on" title="左浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui8&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui8&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui9" class="edui-box edui-button edui-for-imageright edui-default"><div id="edui9_state" onmousedown="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui9_body" unselectable="on" title="右浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui9&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui9&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui10" class="edui-box edui-button edui-for-imagecenter edui-default"><div id="edui10_state" onmousedown="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui10_body" unselectable="on" title="居中" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui10&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui10&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui11" class="edui-box edui-separator  edui-default"></div><div id="edui12" class="edui-box edui-button edui-for-date edui-default"><div id="edui12_state" onmousedown="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui12_body" unselectable="on" title="日期" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui12&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui12&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui13" class="edui-box edui-button edui-for-time edui-default"><div id="edui13_state" onmousedown="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui13_body" unselectable="on" title="时间" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui13&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui13&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui14" class="edui-box edui-button edui-for-horizontal edui-default"><div id="edui14_state" onmousedown="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui14_body" unselectable="on" title="分隔线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui14&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui14&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui15" class="edui-box edui-separator  edui-default"></div><div id="edui16" class="edui-box edui-splitbutton edui-for-inserttable edui-default"><div title="插入表格" id="edui16_state" onmousedown="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui16_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onArrowClick();"></div></div></div></div><div id="edui19" class="edui-box edui-button edui-for-deletetable edui-default"><div id="edui19_state" onmousedown="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui19_body" unselectable="on" title="删除表格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui19&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui19&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui20" class="edui-box edui-button edui-for-insertrow edui-default"><div id="edui20_state" onmousedown="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui20_body" unselectable="on" title="前插入行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui20&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui20&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui21" class="edui-box edui-button edui-for-insertcol edui-default"><div id="edui21_state" onmousedown="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui21_body" unselectable="on" title="前插入列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui21&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui21&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui22" class="edui-box edui-button edui-for-mergeright edui-default"><div id="edui22_state" onmousedown="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui22_body" unselectable="on" title="右合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui22&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui22&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui23" class="edui-box edui-button edui-for-mergedown edui-default"><div id="edui23_state" onmousedown="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui23_body" unselectable="on" title="下合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui23&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui23&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui24" class="edui-box edui-button edui-for-deleterow edui-default"><div id="edui24_state" onmousedown="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui24_body" unselectable="on" title="删除行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui24&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui24&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui25" class="edui-box edui-button edui-for-deletecol edui-default"><div id="edui25_state" onmousedown="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui25_body" unselectable="on" title="删除列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui25&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui25&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui26" class="edui-box edui-button edui-for-splittorows edui-default"><div id="edui26_state" onmousedown="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui26_body" unselectable="on" title="拆分成行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui26&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui26&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui27" class="edui-box edui-button edui-for-splittocols edui-default"><div id="edui27_state" onmousedown="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui27_body" unselectable="on" title="拆分成列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui27&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui27&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui28" class="edui-box edui-button edui-for-splittocells edui-default"><div id="edui28_state" onmousedown="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui28_body" unselectable="on" title="完全拆分单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui28&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui28&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui29" class="edui-box edui-button edui-for-mergecells edui-default"><div id="edui29_state" onmousedown="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui29_body" unselectable="on" title="合并多个单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui29&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui29&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui36" class="edui-box edui-button edui-for-edittable edui-default"><div id="edui36_state" onmousedown="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui36_body" unselectable="on" title="表格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui36&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui36&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui41" class="edui-box edui-button edui-for-edittd edui-default"><div id="edui41_state" onmousedown="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui41_body" unselectable="on" title="单元格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui41&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui41&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui42" class="edui-box edui-separator  edui-default"></div><div id="edui43" class="edui-box edui-menubutton edui-for-lineheight edui-default"><div title="行间距" id="edui43_state" onmousedown="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui43_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onArrowClick();"></div></div></div></div><div id="edui52" class="edui-box edui-menubutton edui-for-rowspacingtop edui-default"><div title="段前距" id="edui52_state" onmousedown="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui52_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onArrowClick();"></div></div></div></div><div id="edui59" class="edui-box edui-menubutton edui-for-rowspacingbottom edui-default"><div title="段后距" id="edui59_state" onmousedown="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui59_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onArrowClick();"></div></div></div></div></div><div id="edui66" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui66&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui67" class="edui-box edui-combox edui-for-fontfamily edui-default"><div title="字体" id="edui67_state" onmousedown="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui67_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onButtonClick(event, this);">字体</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onArrowClick();"></div></div></div></div><div id="edui80" class="edui-box edui-combox edui-for-customstyle edui-default"><div title="自定义标题" id="edui80_state" onmousedown="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui80_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onButtonClick(event, this);">自定义标题</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onArrowClick();"></div></div></div></div><div id="edui86" class="edui-box edui-combox edui-for-paragraph edui-default"><div title="段落格式" id="edui86_state" onmousedown="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui86_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onButtonClick(event, this);">段落格式</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onArrowClick();"></div></div></div></div><div id="edui95" class="edui-box edui-combox edui-for-fontsize edui-default"><div title="字号" id="edui95_state" onmousedown="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui95_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onButtonClick(event, this);">字号</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onArrowClick();"></div></div></div></div><div id="edui110" class="edui-box edui-button edui-for-link edui-default"><div id="edui110_state" onmousedown="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui110_body" unselectable="on" title="超链接" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui110&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui110&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui111" class="edui-box edui-button edui-for-indent edui-default"><div id="edui111_state" onmousedown="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui111_body" unselectable="on" title="首行缩进" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui111&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui111&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui112" class="edui-box edui-button edui-for-bold edui-default"><div id="edui112_state" onmousedown="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui112_body" unselectable="on" title="加粗" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui112&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui112&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui113" class="edui-box edui-splitbutton edui-for-forecolor edui-default edui-colorbutton"><div title="字体颜色" id="edui113_state" onmousedown="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui113_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui113_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onArrowClick();"></div></div></div></div><div id="edui116" class="edui-box edui-splitbutton edui-for-backcolor edui-default edui-colorbutton"><div title="背景色" id="edui116_state" onmousedown="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui116_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui116_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onArrowClick();"></div></div></div></div><div id="edui119" class="edui-box edui-button edui-for-italic edui-default"><div id="edui119_state" onmousedown="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui119_body" unselectable="on" title="斜体" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui119&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui119&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui120" class="edui-box edui-button edui-for-underline edui-default"><div id="edui120_state" onmousedown="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui120_body" unselectable="on" title="下划线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui120&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui120&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui121" class="edui-box edui-button edui-for-strikethrough edui-default"><div id="edui121_state" onmousedown="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui121_body" unselectable="on" title="删除线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui121&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui121&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui122" class="edui-box edui-button edui-for-superscript edui-default"><div id="edui122_state" onmousedown="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui122_body" unselectable="on" title="上标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui122&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui122&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui123" class="edui-box edui-button edui-for-subscript edui-default"><div id="edui123_state" onmousedown="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui123_body" unselectable="on" title="下标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui123&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui123&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui124" class="edui-box edui-button edui-for-removeformat edui-default"><div id="edui124_state" onmousedown="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui124_body" unselectable="on" title="清除格式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui124&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui124&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui125" class="edui-box edui-button edui-for-blockquote edui-default"><div id="edui125_state" onmousedown="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui125_body" unselectable="on" title="引用" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui125&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui125&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui126" class="edui-box edui-button edui-for-pasteplain edui-default"><div id="edui126_state" onmousedown="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui126_body" unselectable="on" title="纯文本粘贴模式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui126&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui126&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui127" class="edui-box edui-menubutton edui-for-insertorderedlist edui-default"><div title="有序列表" id="edui127_state" onmousedown="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui127_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onArrowClick();"></div></div></div></div><div id="edui140" class="edui-box edui-menubutton edui-for-insertunorderedlist edui-default"><div title="无序列表" id="edui140_state" onmousedown="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui140_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onArrowClick();"></div></div></div></div><div id="edui147" class="edui-box edui-button edui-for-cleardoc edui-default"><div id="edui147_state" onmousedown="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui147_body" unselectable="on" title="清空文档" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui147&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui147&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div></div></div></div><div id="edui1_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;"><div id="edui1_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui1&quot;].showWordImageDialog();">点击上传</div><div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui1&quot;].hideToolbarMsg();">x</div><div id="edui1_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div><div style="height:0;overflow:hidden;clear:both;" class="edui-default"></div></div><div id="edui1_message_holder" class="edui-editor-messageholder edui-default"></div></div><div id="edui1_iframeholder" class="edui-editor-iframeholder edui-default" style="width: 100%; height: 300px; z-index: 999; overflow: hidden;"><iframe id="ueditor_0" width="100%" height="100%" frameborder="0" src="javascript:void(function(){document.open();document.write(&quot;<!DOCTYPE html><html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}
body{margin:8px;font-family:sans-serif;font-size:13px;}p{margin:5px 0;}</style><link rel='stylesheet' type='text/css' href='/static/ueditor/themes/iframe.css'/></head><body class='view' ></body><script type='text/javascript'  id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant0'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);</script></html>&quot;);document.close();}())"></iframe></div><div id="edui1_bottombar" class="edui-editor-bottomContainer edui-default"><table class="edui-default"><tbody class="edui-default"><tr class="edui-default"><td id="edui1_elementpath" class="edui-editor-bottombar edui-default"><div class="edui-editor-breadcrumb">元素路径:</div></td><td id="edui1_wordcount" class="edui-editor-wordcount edui-default">字数统计</td><td id="edui1_scale" class="edui-editor-scale edui-default" style="display: none;"><div class="edui-editor-icon edui-default"></div></td></tr></tbody></table></div><div id="edui1_scalelayer" class="edui-default"></div></div></div> <div class="edit-btn"><button type="button" class="button">提交回答</button> <label data-v-0fe26aab="" for="commonCheckbox" class="checkbox"><input data-v-0fe26aab="" type="checkbox" id="commonCheckbox" name="commonCheckbox"> <i data-v-0fe26aab=""></i> 需要彈窗，請勾選</label></div></div></div> <div class="index-right"><div class="right-bk"><h3 class="com-title"><a href="javascript:;">我的最爱</a></h3> <div class="item-box clearfix"><span>錯誤，請您登入後再操作！</span></div></div> <div class="hotTag right-bk"><h3 class="com-title">热门标签</h3> <div class="hotTag-box item-box clearfix"><p>無數據</p></div></div> <div class="right-bk rank"><h3 class="com-title"><a href="javascript:;">冠軍排行榜</a></h3> <ul><li><div><span class="number">1</span> <img src="/static/timg.jpeg"> <span>王菊芳</span></div> <span class="score">612分</span></li><li><div><span class="number">2</span> <img src="/upload/head/10177.gif?time=1556271256"> <span>黃雪靜</span></div> <span class="score">258分</span></li><li><div><span class="number">3</span> <img src="/static/timg.jpeg"> <span>田霞</span></div> <span class="score">240分</span></li><li><div><span class="number">4</span> <img src="/static/timg.jpeg"> <span>曹超雲</span></div> <span class="score">168分</span></li><li><div><span class="number">5</span> <img src="/static/timg.jpeg"> <span>文宇</span></div> <span class="score">96分</span></li><li><div><span class="number">6</span> <img src="/upload/head/10217.png?time=1548059679"> <span>黃興興</span></div> <span class="score">78分</span></li><li><div><span class="number">7</span> <img src="/static/timg.jpeg"> <span>黃佳麗</span></div> <span class="score">62分</span></li><li><div><span class="number">8</span> <img src="/static/timg.jpeg"> <span>马泽鹏</span></div> <span class="score">46分</span></li><li><div><span class="number">9</span> <img src="/static/timg.jpeg"> <span>陳敏南</span></div> <span class="score">40分</span></li><li><div><span class="number">10</span> <img src="/static/timg.jpeg"> <span>Cs-刘婷</span></div> <span class="score">10分</span></li><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></ul></div> <div class="activeUser right-bk"><h3 class="com-title">活跃用户</h3> <div class="activeUser-box"><a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a></div></div> <img src="data:image/png;base64,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" class="to-top"></div></div> <!----> <!----> <!----> <div class="page-cnt footer"><p>T5客服知识库<span>|</span>zsk.591.com.tw</p></div></div></div><script type="text/javascript" src="/js/manifest.f192b.js"></script><script type="text/javascript" src="/js/vendor.17570.js"></script><script type="text/javascript" src="/js/app.58254.js"></script><div id="edui_fixedlayer" class="edui-default" style="position: fixed; left: 0px; top: 0px; width: 0px; height: 0px;"><div id="edui148" class="edui-popup  edui-bubble edui-default" onmousedown="return false;" style="display: none;"> <div id="edui148_body" class="edui-popup-body edui-default"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank" class="edui-default"></iframe> <div class="edui-shadow edui-default"></div> <div id="edui148_content" class="edui-popup-content edui-default">  </div> </div></div></div></body></html>