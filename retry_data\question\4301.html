<html class=" "><head><meta charset="utf-8"><title>591客服知識庫</title><link rel="stylesheet" href="/static/font/font-awesome.min.css"><link href="/css/app.e5aa4.css" rel="stylesheet"><script type="text/javascript" charset="utf-8" async="" src="/js/questionDetail.d0a6b.js"></script><style type="text/css">.que-detail-page .index-left .button{width:74px;height:32px;line-height:32px;font-size:13px;margin-right:10px}.que-detail-page .index-left .button:hover{color:#fff}.que-detail-page .detail-box{padding:20px 30px;border:1px solid #e5e5e5;border-radius:4px}.que-detail-page .detail-box h3{font-size:18px;color:#333;margin-bottom:10px}.que-detail-page .detail-box h3 span{color:#f77d24}.que-detail-page .detail-box .add-fav{cursor:pointer}.que-detail-page .d-b-menu{margin:15px 0 25px;font-size:13px;color:#999}.que-detail-page .d-b-menu b{display:inline-block;font-weight:400;margin:0 10px}.que-detail-page .d-b-menu .r{float:right;color:#f77d24}.que-detail-page .d-b-menu .r .fa{margin-right:5px;font-size:16px}.que-detail-page .d-b-cnt{margin-bottom:25px;font-size:13px;color:#666}.que-detail-page .submit-ask{display:block;margin:32px auto 0}.que-detail-page .reply-list{margin:30px 0 10px;overflow:hidden}.que-detail-page .reply-list .reply-item{padding:15px 30px 25px;margin-bottom:30px;border-radius:4px;border:1px solid #e5e5e5}.que-detail-page .reply-list .reply-item h4{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;line-height:50px;font-size:13px}.que-detail-page .reply-list .reply-item h4 img{width:40px;height:40px;border-radius:50%}.que-detail-page .reply-list .reply-item h4 .time{color:#999}.que-detail-page .reply-list .reply-item h4 .button{-ms-flex-item-align:center;align-self:center}.que-detail-page .reply-list .reply-item h4 .delete{color:#666;cursor:pointer}.que-detail-page .reply-list .reply-item .reply-detail{padding:20px 0 30px}.que-detail-page .more-answer{font-size:18px;color:#666;margin-bottom:5px}.que-detail-page .editor-content table{width:100%;border:1px solid #ddd}.que-detail-page .editor-content table td,.que-detail-page .editor-content table th{border:1px solid #ddd;padding:2px 8px;line-height:2}.que-detail-page .editor-content img{max-width:100%;vertical-align:text-bottom}.que-detail-page .edit-btn{text-align:center;margin-top:20px}.que-detail-page .edit-btn .edit-cancle{background:#aaa}</style><style type="text/css">.head-info{line-height:26px;background:#f5f5f5;font-size:13px;color:#999}.head-info a{margin-left:20px;color:#333}.head-search{margin-top:22px}.head-search img{float:left}.head-search .srh-box{float:left;margin:20px 0 0 20px}.head-search .srh-box .srh-input{float:left;width:260px;height:38px;line-height:38px;border:2px solid #37a508;border-radius:4px 0 0 4px}.head-search .srh-box a,.head-search .srh-box button{float:left;width:80px;height:38px;line-height:36px;font-size:16px;color:#fff}.head-search .srh-box .srh-btn{border-radius:0 4px 4px 0}.head-search .srh-box .ask-btn{margin-left:10px}.head-nav{float:left;margin:20px 0 0 60px;color:#fff;font-size:16px;line-height:36px}.head-nav a{display:block;height:100%;color:#333}.head-nav li{position:relative;float:left;margin-right:35px}.head-nav .active a,.head-nav li:hover a{color:#30b940}.head-nav .active:after,.head-nav li:hover:after{display:block;content:"";height:3px;width:100%;background:#30b940}.head-pic{width:100%;height:90px;margin-top:20px;background:url(/static/banner.png) top no-repeat}</style><style type="text/css">.top-breadcrumb{height:20px;margin-top:10px;margin-bottom:40px;font-size:13px;color:#666}.top-breadcrumb .t-b-icon{color:#30b940}.top-breadcrumb span{margin-left:8px}.top-breadcrumb span .fa{font-size:16px;margin-left:8px}.top-breadcrumb span:first-child{margin-left:0}.top-breadcrumb .now{color:#30b940}</style><script type="text/javascript" src="/static/ueditor/ueditor.config.js?20210319" id="configScriptTag"></script><script type="text/javascript" src="/static/ueditor/ueditor.all.js?20210319" id="editorScriptTag"></script><style type="text/css">.checkbox[data-v-0fe26aab]{word-spacing:4px;vertical-align:bottom;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.checkbox i[data-v-0fe26aab]{display:inline-block;width:14px;height:14px;border-radius:2px;border:1px solid #ccc;background:#fff;position:relative;top:1px}.checkbox input[data-v-0fe26aab]{opacity:0;width:0;height:0;margin:0}.checkbox input:checked+i[data-v-0fe26aab]{background:#30b940;border-color:#30b940}.checkbox input:checked+i[data-v-0fe26aab]:before{content:"";position:absolute;left:2px;top:1px;width:11px;height:6px;border-bottom:2px solid #fff;border-left:2px solid #fff;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}</style><style type="text/css">.popbox{position:fixed;top:50%;left:50%;z-index:1011;background:#fff;border-radius:6px;overflow:hidden}.popbox h3{font-size:16px;line-height:40px;padding:0 20px}.popbox h3 span{float:right;color:#666;font-size:13px;cursor:pointer}.popbox .pb-cnt{padding:0 20px;font-size:13px}.popbox .pb-btn-area{position:absolute;bottom:0;left:0;z-index:1;width:100%;text-align:right;padding:0 20px 20px 0}.popbox .pb-btn-area button{height:32px;line-height:32px;border:0;background:#fff;cursor:pointer;border-radius:6px;margin-left:15px;padding:0 15px}.popbox .pb-btn-area button.pb-btn-ok{background:#daf3cf;color:#279005}.popbox .pb-btn-area button.pb-btn-ok:hover{background:#279005;color:#fff}</style><style type="text/css">.shadow{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1010;background:#000}</style><style type="text/css">.footer{margin-top:70px;margin-bottom:20px;padding-top:20px;text-align:center;color:#999;border-top:1px solid #f5f5f5}.footer span{margin:0 10px}</style><script src="/static/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript" defer="defer"></script><link href="/static/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet"><script src="/static/ueditor/third-party/codemirror/codemirror.js" type="text/javascript" defer="defer"></script><link rel="stylesheet" type="text/css" href="/static/ueditor/third-party/codemirror/codemirror.css"><script src="/static/ueditor/third-party/zeroclipboard/ZeroClipboard.js" type="text/javascript" defer="defer"></script><style type="text/css">.activeUser,.favorite,.hotTag,.rank{margin-bottom:40px}</style><style type="text/css">.rank .rank-page{height:60px;line-height:60px;font-size:18px;margin-bottom:20px;text-align:center;background:#fafafa;border-radius:5px}.rank ul{padding:0 20px}.rank li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;border-bottom:1px dotted #f5f5f5;line-height:50px}.rank li .flag{position:relative;display:inline-block;width:20px;height:30px;background:#476472;vertical-align:middle;margin-right:12px;color:#fff;text-align:center;line-height:20px;font-size:13px;font-style:normal}.rank li .flag:after{content:"";position:absolute;bottom:-4px;left:-4px;border:14px solid transparent;border-bottom-color:#fff}.rank li:first-child .flag{background:#fb6757}.rank li:nth-child(2) .flag{background:#fac525}.rank li:nth-child(3) .flag{background:#00a9fb}.rank li img{width:36px;height:36px;border-radius:50%;margin-right:12px}</style><style type="text/css">.activeUser .activeUser-box a{display:inline-block;width:64px;height:64px;border-radius:50%;overflow:hidden;margin:0 6px 12px}</style><style type="text/css">.to-top{display:none;position:fixed;bottom:150px;z-index:5;width:50px;height:50px;cursor:pointer}</style></head><body style=""><div id="app" style=""><div class="que-detail-page" style=""><div class="wiki-hd"><div class="head-info"><p class="page-cnt">
      您好，欢迎来到591客服知识库
      <a href="https://www.591.com.tw/admin.php?new_url=https://zsk.591.com.tw">[请登入]</a> <!----></p></div> <div class="page-cnt head-search clearfix"><a href="/" class="router-link-active"><img src="/static/logo.png"></a> <ul class="head-nav clearfix"><li class="router-link-active active"><a href="/">591房屋交易网</a></li> <li class=""><a href="/life">客服充电站</a></li> <li class=""><a href="/notification">通知事项</a></li></ul> <div class="srh-box"><input type="text" placeholder="请输入搜索内容" class="srh-input"> <button type="button" class="button srh-btn">搜索</button> <a href="/ask/591" class="button button-orange ask-btn">提問</a></div></div> <div class="head-pic"></div></div> <div class="top-breadcrumb com-width"><span><a href="/collection/591/1209" class="">客服督導相關<i class="fa fa-angle-double-right"></i></a></span><span><a href="/collection/591/1209/1658" class="">業務作業指導<i class="fa fa-angle-double-right"></i></a></span><span><strong class="now">问题详情</strong></span></div> <div class="com-width clearfix" style=""><div class="index-left" style=""><div class="detail-box"><h3><!---->線上回復作業指導書</h3> <p class="clearfix d-b-menu"><span class="l">
            王菊芳&nbsp;&nbsp;&nbsp;浏览111次&nbsp;&nbsp;&nbsp;2020-10-26 17:02:40
          </span> <span class="r"><span class="add-fav"><i class="fa fa-heart"></i>加入最爱</span></span></p> <div class="d-b-cnt editor-content big-img-parent"><div><p><span style="font-family: 微软雅黑, Microsoft YaHei;">線上回復作業指導書</span><br></p></div></div> <div class="d-b-button"><a href="/edit/591/4301" class="button">编辑</a> <button class="button button-orange">关闭提问</button></div></div> <div class="hr-line"></div> <ul class="reply-list big-img-parent"><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">曹超雲</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-27 11:23:20</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin-top: 15px;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">一、&nbsp;作業目的</span></strong></p><p style="margin-top: 15px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">使網站回復類事務達到統一，完美，讓會員滿意，並在一定程度上可給回復專員一定方向上的指導。</span></p><p style="margin-top: 15px;"><strong><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">二、&nbsp;適用範圍</span></strong></p><p style="margin-top: 15px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">台灣房屋交易事業部 → 客服部 → 高級客服</span></p><p style="margin-top: 15px;"><strong><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">三、&nbsp;線上問題類型</span></strong></p><p style="margin-top: 15px;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">（一） 申訴</span></strong></p><p style="margin-top: 15px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、回覆規範及要求</span></p><p style="margin-top: 15px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1 回覆規範</span></p><p style="margin-top: 15px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><table width="0" cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border-width: 1px; border-style: solid; border-color: black black windowtext; border-image: initial; background: white; padding: 0px 7px;" width="121" valign="top"><p style="text-align:center"><span style="font-size: 14px;"><strong><span style="font-family: 微软雅黑, sans-serif; color: black; font-size: 14px;">分類</span></strong></span></p></td><td style="border-top: 1px solid black; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid black; background: white; padding: 0px 7px;" width="520" valign="top"><p style="text-align:center"><span style="font-size: 14px;"><strong><span style="font-family: 微软雅黑, sans-serif; color: black; font-size: 14px;">規範</span></strong></span></p></td></tr><tr><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="121"><p style="text-align:center"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">完整性</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" width="520" valign="top"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font-family: Wingdings; font-size: 14px;">Ø</span>&nbsp;提醒類：</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">1.物件暫時關閉，需提醒關閉時間照常計算</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">2.物件刊登錯誤申請取消，需提醒廣告資料會隨之刪除，需重新填寫</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">3.仲介需刪除頭像時，需提醒刪除後店鋪也會隨之關閉</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">4.套餐到期需延長使用時間的，需提醒在有效期內使用完畢，逾期作廢</span></p><p><span style="font-size: 14px;"><span style="font-family: Wingdings; font-size: 14px;">Ø<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; font-size: 14px;">&nbsp; </span></span><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">解釋類：</span></span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">1.會員申訴提出建議某項功能時，請粗略說明我們不提供該功能的原因，並重點重複會員的建議後記錄下來進行改善</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">2.回覆前請理清會員的問題個數，一對一針對性回復</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">3.重複刊登已超出可取消範圍之回覆，請使用簡潔明瞭的原因話術：“因廣告於XX時間刊登，至今已曝光XX天”</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">4.套餐到期但已跨月，無法延長使用時間的，請說明原因並適當的提供一些建議</span></p></td></tr><tr><td style="border-top: none; border-left: 1px solid black; border-bottom: 1px solid windowtext; border-right: 1px solid black; padding: 0px 7px;" width="121"><p style="text-align:center"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">主動性</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; padding: 0px 7px; word-break: break-all;" width="520" valign="top"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-family: Wingdings; font-size: 14px;">Ø</span>&nbsp; 檢舉類：</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">會員線上舉報廣告不實或有誤，若留下廣告編號則需跟蹤到廣告處理完成之後再回覆舉報者處理結果；若未留下廣告編號則回覆，請舉報人留下相應廣告資訊由客服中心進行查確認</span></p><p><span style="font-size: 14px;"><span style="font-family: Wingdings; font-size: 14px;">Ø<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; font-size: 14px;">&nbsp; </span></span><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">糾紛類：</span></span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">若可通過客服中心來幫忙聯絡從而幫會員解決糾紛，客服中心需幫忙聯絡處理；但若不在客服中心服務範圍，或無法幫會員處理的問題，則可直接說明客服中心的立場從而拒絕，但是要盡力提供建議，安撫會員情緒</span></p><p><span style="font-size: 14px;"><span style="font-family: Wingdings; font-size: 14px;">Ø<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; font-size: 14px;">&nbsp; </span></span><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">投訴類：</span></span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">1.會員對網站使用，規則，客服服務不滿，已造成會員困擾或不便之申訴皆歸類為服務投訴</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">2.會員自行選擇了服務投訴，客服在回覆時需判斷是否為投訴，若否將分類修正為正對應類型</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">3.會員選擇為非投訴類別時，客服在回覆時需將其內容選擇為是，將其分類選擇為投訴</span></p><p><span style="font-size: 14px;"><span style="font-family: Wingdings; font-size: 14px;">Ø<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; font-size: 14px;">&nbsp; </span></span><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">其他類：</span></span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">1.會員要求電話聯絡的，需主動優先電話聯絡處理</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">2.會員同一天多次申訴，此情況需電話聯絡說明處理，且不可隨意刪除會員相同的申訴內容，每條申訴均需回覆</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">3.會員同一天多次申訴，但對於客服已回覆的上條申訴內容並未查看，此情況應電話聯絡會員確認說明</span></p></td></tr><tr><td style="border-right: 1px solid black; border-bottom: 1px solid black; border-left: 1px solid black; border-image: initial; border-top: none; padding: 0px 7px;" width="121"><p style="text-align:center"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">申請類</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; padding: 0px 7px;" width="520" valign="top"><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">1.當日重複儲值要求刷退，聯絡無人接聽時（專員同意則直接處理回覆結果）</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">2.要求退現、套餐延長、發票改開超過申報日等均優先申請</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">3.其它情況會員要求賠償等狀況優先反饋專員申請</span></p></td></tr><tr><td style="border-right: 1px solid black; border-bottom: 1px solid black; border-left: 1px solid black; border-image: initial; border-top: none; padding: 0px 7px;" width="121"><p style="text-align:center"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">優先電話聯絡類</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; padding: 0px 7px; word-break: break-all;" width="520" valign="top"><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">1.內容不詳細或無法理解會員問題時</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">2.發票已開立未收到，查詢已超過15天</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">3.物件無法搜尋、顯示異常、網站功能異常（客服測試均正常）</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">4.儲值後廣告無法/未開啟（需聯絡協助開並瞭解原因）</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">5.更改會員身份，賬號有多筆刊登物件，申訴未說明是否已離職</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">6.更改仲介認證資料，需告知先取消認證再重新上傳認證</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">7.對網站使用、規則、客服服務不滿，已造成嚴重困擾或損失時</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">8.需要取消套餐物件，核實當日為套餐有效期最後一日時</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">9.退點退現，先電話聯絡，無人接聽，內容明確取消廣告可直接幫其取消<br></span></p></td></tr><tr><td style="border-right: 1px solid black; border-bottom: 1px solid black; border-left: 1px solid black; border-image: initial; border-top: none; padding: 0px 7px;" width="121" valign="top"><p style="text-align:center"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">處理時間</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; padding: 0px 7px;" width="520" valign="top"><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">1個工作日內完成：</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">半小時內回復之問題：取消退點、開啟或修改廣告、點數異常、功能異常問題</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">1小時內回復之問題：變更身份、物件搜尋</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">2小時內回復之問題：其他類（如收費標準、如何刊登、如何儲值、檢舉、糾紛）</span></p></td></tr></tbody></table><p style="margin-top: 10px;"><span style="font-size: 14px;"><span style="line-height: 150%; font-family: 微软雅黑, sans-serif;">1.2&nbsp; 回復要</span><span style="font-family: 微软雅黑, sans-serif;">求</span></span></p><p style="margin: 10px 0px 8px 21px; line-height: 150%;"><span style="font-size: 14px;"><span style="line-height: 150%; font-family: Wingdings; font-size: 14px;">u<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; font-size: 14px;">&nbsp; </span></span><span style="line-height: 150%; font-family: 微软雅黑, sans-serif; font-size: 14px;">符號為半型輸入法</span></span></p><p style="margin: 10px 0px 8px 21px; line-height: 150%;"><span style="font-size: 14px;"><span style="line-height: 150%; font-family: Wingdings; font-size: 14px;">u<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; font-size: 14px;">&nbsp; </span></span><span style="line-height: 150%; font-family: 微软雅黑, sans-serif; font-size: 14px;">無錯別字和簡體字</span></span></p><p style="margin: 10px 0px 8px 21px; line-height: 150%;"><span style="font-size: 14px;"><span style="line-height: 150%; font-family: Wingdings; font-size: 14px;">u<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; font-size: 14px;">&nbsp; </span></span><span style="line-height: 150%; font-family: 微软雅黑, sans-serif; font-size: 14px;">回覆時需使用英文格式</span></span></p><p style="margin: 10px 0px 8px 21px; line-height: 150%;"><span style="font-size: 14px;"><span style="line-height: 150%; font-family: Wingdings; font-size: 14px;">u<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; font-size: 14px;">&nbsp; </span></span><span style="line-height: 150%; font-family: 微软雅黑, sans-serif; font-size: 14px;">回覆的句子最長不超過25字</span></span></p><p style="margin: 10px 0px 8px 21px; line-height: 150%;"><span style="font-size: 14px;"><span style="line-height: 150%; font-family: Wingdings; font-size: 14px;">u<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; font-size: 14px;">&nbsp; </span></span><span style="line-height: 150%; font-family: 微软雅黑, sans-serif; font-size: 14px;">建議性的問題需記錄，提交至產品專員</span></span></p><p style="margin: 10px 0px 8px 21px; line-height: 150%;"><span style="font-size: 14px;"><span style="line-height: 150%; font-family: Wingdings; font-size: 14px;">u<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; font-size: 14px;">&nbsp; </span></span><span style="line-height: 150%; font-family: 微软雅黑, sans-serif; font-size: 14px;">回覆要相對及時、內容準確、親切全面</span></span></p><p style="margin: 10px 0px 8px 21px; line-height: 150%;"><span style="font-size: 14px;"><span style="line-height: 150%; font-family: Wingdings; font-size: 14px;">u<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; font-size: 14px;">&nbsp; </span></span><span style="line-height: 150%; font-family: 微软雅黑, sans-serif; font-size: 14px;">回覆承諾會員的需兌現</span></span></p><p style="margin: 10px 0px 8px 21px; line-height: 150%;"><span style="font-size: 14px;"><span style="line-height: 150%; font-family: Wingdings; font-size: 14px;">u<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; font-size: 14px;">&nbsp; </span></span><span style="line-height: 150%; font-family: 微软雅黑, sans-serif; font-size: 14px;">回覆內容盡量避免多次重複使用同樣語句</span></span></p><p style="margin: 10px 0px 8px 21px; line-height: 150%;"><span style="font-size: 14px;"><span style="line-height: 150%; font-family: Wingdings; font-size: 14px;">u<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; font-size: 14px;">&nbsp; </span></span><span style="line-height: 150%; font-family: 微软雅黑, sans-serif; font-size: 14px;">回覆取消套餐使用“【】、&lt;&gt;”這樣的格式，例如：【新手套餐】、&lt;新手套餐&gt;</span></span></p><p style="margin: 10px 0px 8px 21px; line-height: 150%;"><span style="font-size: 14px;"><span style="line-height: 150%; font-family: Wingdings; font-size: 14px;">u<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; font-size: 14px;">&nbsp; </span></span><span style="line-height: 150%; font-family: 微软雅黑, sans-serif; font-size: 14px;">取消廣告退還“筆數或點數”需回覆完整，若取消多筆廣告可用&lt;s****、s****&gt;</span></span></p><p style="margin: 10px 0px 8px 21px; line-height: 150%;"><span style="font-size: 14px;"><span style="line-height: 150%; font-family: Wingdings; font-size: 14px;">u<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; font-size: 14px;">&nbsp; </span></span><span style="line-height: 150%; font-family: 微软雅黑, sans-serif; font-size: 14px;">繳費後明確表示需購買套餐的類型，請優先協助購買並回覆購買方法</span></span></p><p style="margin: 10px 0px 8px 21px; line-height: 150%;"><span style="font-size: 14px;"><span style="line-height: 150%; font-family: Wingdings; font-size: 14px;">u<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; font-size: 14px;">&nbsp; </span></span><span style="line-height: 150%; font-family: 微软雅黑, sans-serif; font-size: 14px;">跟進的問題，需三天內依處理進度主動回覆給會員，不可拖延或不跟蹤</span></span></p><p style="text-indent: 0px; margin: 10px 0px 8px 21px; line-height: 150%;"><span style="font-size: 14px;"><span style="font-family: 微软雅黑, sans-serif; text-indent: 29px; font-size: 14px;">&nbsp; &nbsp; &nbsp;</span><span style="color: red; font-family: 微软雅黑, sans-serif; text-indent: 29px; font-size: 14px;">PS：9點上班後，回復專員需先檢查一遍留言、申訴、信箱，首先處理留言；其次，按申訴及時性要求處理問題。</span></span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">曹超雲</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-27 11:34:10</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin-top: 10px;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;2、回覆範例</span></strong></p><p style="margin: 10px 0px 8px 28px; line-height: 150%;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.1</span></strong><strong> </strong><strong><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">提醒類</span></strong></span></p><p style="margin: 10px 0px 8px 28px; line-height: 150%;"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1）暫時關閉</span></p><p style="margin: 10px 0px 8px 28px; line-height: 150%;"><img src="/upload/question/20201027/1603769320914847.png" alt="image.png"></p><p style="text-indent: 29px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">回復要素：關閉時間照常計算、不可保留，有效期可開啟</span></p><p style="text-indent: 29px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2）刊登錯誤要求取消廣告</span></p><p style="text-indent: 28px; margin-top: 10px;"><img src="/upload/question/20201027/1603769337588632.png" alt="image.png"></p><p style="margin-left: 28px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">回復要素：物件編號、退還多少點數/筆數，取消後資料會被刪除</span></p><p style="margin-left: 28px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3）申請證書/頭像需刪除</span></p><p style="margin-left: 28px; margin-top: 10px;"><img src="/upload/question/20201027/1603769348532957.png" alt="image.png"></p><p style="margin-left: 28px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">回復要素：提醒刪除後店鋪會關閉，重新上傳審核後方可開通</span></p><p style="text-indent: 29px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4）套餐需延長時</span></p><p style="text-indent: 28px; margin-top: 10px;"><img src="/upload/question/20201027/1603769372840194.png" alt="image.png"></p><p style="margin-left: 28px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">回復要素：提醒有效期內使用完畢，逾期作廢~</span></p><p style="margin-left: 28px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>2.2&nbsp; </strong><strong>解釋類</strong></span></p><p style="margin-left: 28px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1）會員提出建議某項功能時</span></p><p style="margin-left:28px"><img src="/upload/question/20201027/1603769392951620.png" alt="image.png"></p><p style="margin-left: 28px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">回復要素：未提供該功能，重點回復會員的建議記錄下來進行改善。</span></p><p style="text-indent: 29px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">其他解釋類：</span></p><ul class="custom_dot list-paddingleft-1"><li class="list-dot list-dot-paddingleft"><p style="text-indent: 29px; margin-top: 10px;"><span style="font-size: 14px; color: #333333; font-family: Wingdings;">Ø</span><span style="font-size: 14px; box-sizing: border-box; color: #333333; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">回復前請理清會員的問題個數，一對一針對性回復</span></p></li><li class="list-dot list-dot-paddingleft"><p style="text-indent: 29px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="color: #333333; font-family: Wingdings; font-size: 14px;">Ø</span><span style="box-sizing: border-box; color: #333333; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;</span>對會員回饋廣告曝光效果不好的問題，請告知一些原因並提供一些建議性的方案</span></p></li><li class="list-dot list-dot-paddingleft"><p style="text-indent: 29px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="color: #333333; font-family: Wingdings; font-size: 14px;">Ø</span><span style="box-sizing: border-box; color: #333333; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;</span>重複刊登已超出可取消範圍之回復，請使用簡潔明瞭的原因話術：“因廣告於XX時間刊登，至今已曝光XX天”</span></p></li><li class="list-dot list-dot-paddingleft"><p style="text-indent: 29px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="color: #333333; font-family: Wingdings; font-size: 14px;">Ø</span><span style="box-sizing: border-box; color: #333333; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;</span>套餐到期但已跨月，無法延長使用時間的，請說明原因並適當的提供一些建議</span></p></li></ul><p style="margin-left: 28px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>2.3 </strong><strong>電話聯絡類</strong></span></p><p style="line-height: 150%; margin-top: 10px;"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &nbsp; &nbsp; &nbsp;1）物件顯示異常</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<img src="/upload/question/20201027/1603769407106503.png" alt="image.png"></span></p><p style="margin-left: 42px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;回復要素：電話聯絡、告知未顯示原因</span></p><p style="margin-left: 42px; margin-bottom: 0px; line-height: 150%; margin-top: 10px;"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2）網站bug導致物件搜尋不到</span></p><p style="margin-left:42px">&nbsp; &nbsp;&nbsp;<img src="/upload/question/20201027/1603769432466615.png" alt="image.png"></p><p style="margin-left: 42px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &nbsp; 回復要素：客服測試搜尋、電話聯絡、告知未顯示原因</span></p><p style="margin-left: 42px; margin-bottom: 0px; line-height: 150%; margin-top: 10px;"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3）無法開啟物件，套餐到期</span></p><p style="text-indent:42px">&nbsp; &nbsp;<img src="/upload/question/20201027/1603769447863634.png" alt="image.png">&nbsp;</p><p style="text-indent:42px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &nbsp;回復要素：電話聯絡、告知無法開啟的原因、提供解決方案&nbsp;</span></p><p style="text-indent:42px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;對於套餐問題，若在處理範圍內，需優先拒絕，無法拒絕時再幫&nbsp; 特殊處理</span></p><p style="margin-left: 42px; line-height: 150%; margin-top: 10px;"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4）其他電話聯絡類</span></p><ul class="custom_dot list-paddingleft-1"><li class="list-dot list-dot-paddingleft"><p style="margin-left: 42px; line-height: 150%; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font-size: 14px; text-indent: 29px; color: #333333; font-family: Wingdings;">Ø</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: &quot;Times New Roman&quot;; text-indent: 29px; box-sizing: border-box; color: #333333;">&nbsp;</span>對於發票未收到之問題，查詢若超過</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">15</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">天請電話聯絡確認並回復告知寄送及補寄流程。</span></p><p style="margin-left: 42px; line-height: 150%; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font-size: 14px; text-indent: 29px; color: #333333; font-family: Wingdings;">Ø</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: &quot;Times New Roman&quot;; text-indent: 29px; box-sizing: border-box; color: #333333;">&nbsp;</span>會員所描述之申訴問題無法理解或不詳細時，請電話聯絡確認根本問題並解決</span><span style="font-size: 14px; text-indent: 29px; color: #333333; font-family: Wingdings;">&nbsp;&nbsp;</span></p></li><li class="list-dot list-dot-paddingleft"><p style="margin-left: 42px; line-height: 150%; margin-top: 10px;"><span style="font-size: 14px; text-indent: 29px; color: #333333; font-family: Wingdings;">Ø</span><span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; text-indent: 29px; box-sizing: border-box; color: #333333;">&nbsp;</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員身份問題，帳號有多筆廣告，申訴中未說明是否已離職需電話聯絡確認處理</span></p></li></ul><p style="margin-left: 28px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>2.4 </strong><strong>跟進類</strong></span></p><p style="margin-left: 14px; margin-bottom: 0px; line-height: 150%; margin-top: 10px;"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;&nbsp;&nbsp; 1）需跟進的申訴問題，請勾選“待跟進”並於<strong><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">三天內</span></strong>依處理進度主動回復給會員，不可延或不跟蹤</span></p><p style=";text-indent:42px;line-height:150%"><img src="/upload/question/20201027/1603769515642639.png" alt="image.png"></p><p style="text-indent:42px;line-height:150%"><span style="position: absolute; z-index: 251657728; left: 0px; margin-left: 288px; margin-top: 524px; width: 134px; height: 40px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br><br></span></p><p style="margin-left: 42px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><img src="/upload/question/20201027/1603769551527119.png" alt="image.png"></span></p><p style="margin-left: 42px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">回復要素：明確問題、跟進、回復處理結果</span></p><p style="margin-left: 42px; margin-bottom: 0px; line-height: 150%; margin-top: 10px;"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2）檢舉類：</span></p><p style="margin-left: 42px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1）會員舉報廣告不實或有誤，若留下廣告編號則需跟蹤到廣告處理完成之後再回復舉報者處理結果；若未留下廣告編號則回復，請舉報人留下相應廣告資訊由客服中心進行查確認</span></p><p style="margin-left:42px"><img src="/upload/question/20201027/1603769575540644.png" alt="image.png"></p><p style="margin-left:42px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">回復要素：明確舉報問題、電話聯絡進行確認、回復處理結果。</span></p><p style="margin-left: 42px; margin-bottom: 0px; line-height: 150%; margin-top: 10px;"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2）其他跟進類</span></p><ul class="custom_dot list-paddingleft-1"><li class="list-dot list-dot-paddingleft"><p style="margin-left: 42px; margin-bottom: 0px; line-height: 150%; margin-top: 10px;"><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="text-indent: 29px; color: #333333; font-family: Wingdings;">Ø</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; text-indent: 29px; box-sizing: border-box; color: #333333;">&nbsp;</span>&nbsp;</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">對於需要取消套餐物件之問題，請優先核實套餐期限問題再進行處理&nbsp;</span></p></li></ul><p style="margin-left: 42px; margin-bottom: 0px; line-height: 150%; margin-top: 10px;"><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp;<span style="text-indent: 29px; color: #333333; font-family: Wingdings;">Ø</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; text-indent: 29px; box-sizing: border-box; color: #333333;">&nbsp;</span></span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">對繳費後明確表示需購買套餐的類型之問題，請優先協助購買並回復購買方法</span></p><p style="margin-left: 32px; margin-bottom: 0px; line-height: 150%; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.5 </span></strong><strong><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">糾紛類</span></strong></span></p><p style="margin-left: 42px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1）若可通過客服中心來幫忙聯絡從而幫會員解決糾紛，需幫忙聯絡處理，但若不在客服中心服務範圍，或無法幫會員處理的問題則可直接說明網站的立場從而拒絕，但是要盡力提供建議，安撫會員情緒</span></p><p style="margin-left: 32px; margin-bottom: 0px; line-height: 150%; margin-top: 10px;"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;&nbsp;<img src="/upload/question/20201027/1603769599906204.png" alt="image.png"></span></p><p style="text-indent: 44px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">回復要素：明確問題點、回復結果、表明立場、提供建議、表示歉意</span></p><p style="margin-left: 14px; margin-bottom: 0px; text-indent: 15px; line-height: 150%; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.6 </span></strong><strong><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">投訴類</span></strong></span></p><p style="margin-left: 42px; margin-bottom: 0px; line-height: 150%; margin-top: 10px;"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1）會員對網站使用，規則，客服服務不滿，已造成會員困擾或不便之申訴皆歸類為服務投訴</span></p><p style="margin-left: 74px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 14px; text-indent: 29px; color: #333333; font-family: Wingdings;">Ø</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: &quot;Times New Roman&quot;; text-indent: 29px; box-sizing: border-box; color: #333333;">&nbsp;</span></span>會員自行選擇了服務投訴，客服在回復時需判斷是否為投訴，若否將分類修正為正對應類型。</span></p><p style="margin-left: 74px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 14px; text-indent: 29px; color: #333333; font-family: Wingdings;">Ø</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: &quot;Times New Roman&quot;; text-indent: 29px; box-sizing: border-box; color: #333333;">&nbsp;</span></span>會員選擇為非投訴類別時，客服在回復時需將其內容選擇為是，將其分類選擇為投訴。</span></p><p style="text-indent: 15px; line-height: 150%; margin-top: 10px;"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<img src="/upload/question/20201027/1603769614335480.png" alt="image.png"></span></p><p style="text-indent: 15px; line-height: 150%; margin-top: 10px;"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 回復要素：電話聯絡確認、表示我們的重視及後續的處理方式、表示歉意</span></p><p style="margin-left: 14px; margin-bottom: 0px; text-indent: 15px; line-height: 150%; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.7 </span></strong><strong><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">特殊類：</span></strong></span></p><p style="margin-left: 42px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1）會員同一天多次線上留言或申訴，此種情況需電話聯絡說明處理，且不可隨意刪除會員相同的申訴內容，每條申訴均需回復。</span></p><p style=";text-indent:42px;line-height:150%">&nbsp; &nbsp;<img src="/upload/question/20201027/1603769635884351.png" alt="image.png"></p><p style="margin-left: 42px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;PS：會員在同一天多次線上申訴，但是對於客服已回覆的上條申訴內容並未查看，此種情況應電話聯絡會員確認說明。</span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">曹超雲</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-27 11:44:12</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、操作步驟</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3.1 一般申訴回覆步驟：客服後臺 - 客服相關- 線上回復-</span><a href="http://www.591.com.tw/admin.php?module=appeal&amp;action=list" style="text-decoration: underline; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">申訴列表</span></a><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">（請見圖示）</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><img src="/upload/question/20201027/1603770152276129.png" alt="image.png"></span></p><p><br></p><p><br></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3.2 跟進類申訴回覆步驟：客服後臺 - 客服相關- 線上回復-</span><a href="http://www.591.com.tw/admin.php?module=appeal&amp;action=list" style="text-decoration: underline; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">申訴列表</span></a><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">-狀態為跟進（請見圖示）</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><p><img src="/upload/question/20201027/1603770232293541.png" alt="image.png"></p><p><img src="/upload/question/20201027/1603770250385604.png" alt="image.png"></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:28px;line-height:150%"><br></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">曹超雲</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-27 11:47:03</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin-top: 10px;"><span style="font-size: 16px;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">（二）留言</span></strong></span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、 回復規範及要求</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1回覆規範</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">留言回復完整性、主動性、申請類、優先電話聯絡類，詳情可參考申訴處理規範</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2&nbsp; 回覆要求</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">可直接編輯或刪除類</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1)&nbsp;&nbsp;&nbsp;&nbsp; 留言提問中帶有網址，將網址部分編輯</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2)&nbsp;&nbsp;&nbsp;&nbsp; 留言內容若是宣傳打廣告等無關資訊，將整條留言刪除</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3)&nbsp;&nbsp;&nbsp;&nbsp; 內容已電話聯絡解決，將整條留言刪除（需跟進則記錄回撥）</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4)&nbsp;&nbsp;&nbsp;&nbsp; 同一用戶當天重複留言（內容全部一致），則可將重複留言內容做刪除只保留一筆記錄</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5)&nbsp;&nbsp;&nbsp;&nbsp; 留言中留有姓名、電話、帳號、密碼、詳細地址與郵箱等會員個人隱私資料時使用“*”號隱藏個人隱私資料</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">6)&nbsp;&nbsp;&nbsp;&nbsp; 內容已信箱回覆，將整條留言刪除</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、&nbsp;&nbsp; 回覆範例</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.1 網站回覆</span></p><p style="margin-top: 10px;"><img src="/upload/question/20201027/1603770376975309.png" alt="image.png"></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.2 郵箱回覆</span></p><p style="margin-top: 10px;"><img src="/upload/question/20201027/1603770390749019.png" alt="image.png"></p><p><br></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、&nbsp;&nbsp; 操作步驟</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 3.1 回覆步驟：客服後臺 - 客服相關-留言（請見圖示）</span></p><p><br></p><p style="margin-top: 10px;"><img src="/upload/question/20201027/1603770401935949.png" alt="image.png"></p><p style="margin-top: 10px;"><img src="/upload/question/20201027/1603770414182046.png" alt="image.png"></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">曹超雲</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-27 11:54:24</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin-top: 10px;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">（三）信箱</span></strong></p><p style="margin-top: 10px;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、 回復規範及要求</span></strong></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1 回復完整性、主動性、申請類、優先電話聯絡類，詳情可參考申訴處理規範</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2 注意事項</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1)&nbsp;&nbsp;&nbsp;&nbsp; 信箱回覆涉及個資時，請參照《信箱處理流程規範》進行核實回覆</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2)&nbsp;&nbsp;&nbsp;&nbsp; 電話中或線上回覆引導會員將截圖、照片發送至信箱時需說明備註行動電話和問題</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3)&nbsp;&nbsp;&nbsp;&nbsp; 由處理信箱的客服收到直接處理</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4)&nbsp;&nbsp;&nbsp;&nbsp; 客服電話中或線上直接處理了則需截圖回報給處理的信箱的人回覆</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5)&nbsp;&nbsp;&nbsp;&nbsp; 信箱的回覆格式需要Copy模板，可打開申訴回覆列表填寫，再至信箱複製即可</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;</span></p><p style="margin-top: 10px;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、 回覆範例</span></strong></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="box-sizing: border-box; color: #333333; font-size: 14px; text-indent: 29px; font-family: Wingdings;">Ø</span><span style="box-sizing: border-box; color: #333333; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: &quot;Times New Roman&quot;; text-indent: 29px;">&nbsp;</span>&nbsp; 信箱帳號：</span><a href="mailto:<EMAIL>" style="text-decoration: underline; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><EMAIL></span></a></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="box-sizing: border-box; color: #333333; font-size: 14px; text-indent: 29px; font-family: Wingdings;">Ø</span><span style="box-sizing: border-box; color: #333333; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: &quot;Times New Roman&quot;; text-indent: 29px;">&nbsp;</span>&nbsp; 密碼：591888tw</span></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="box-sizing: border-box; color: #333333; font-size: 14px; text-indent: 29px; font-family: Wingdings;">Ø</span><span style="box-sizing: border-box; color: #333333; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: &quot;Times New Roman&quot;; text-indent: 29px;">&nbsp;</span>&nbsp; 信箱列表（需處理昨日六點後及當天六點前的所有郵箱）</span></p><p style="margin-top: 10px;"><img src="/upload/question/20201027/1603770830483982.png" alt="image.png"></p><p style="margin-top: 15px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><span style="font-size: 14px;">3、 處理流程 &nbsp;&nbsp;</span>&nbsp;</strong></span></p><p style="margin-top: 15px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><br></strong></span></p><table width="570" cellspacing="0" cellpadding="0"><tbody><tr style=";height:35px" class="firstRow"><td style="border: 1px solid windowtext; background: rgb(242, 242, 242); padding: 0px 7px;" width="91" height="35"><p style="text-align:center"><strong><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">会员需求</span></strong></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(242, 242, 242); padding: 0px 7px;" width="102" height="35"><p style="text-align:center"><strong><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">具体需求</span></strong></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(242, 242, 242); padding: 0px 7px;" width="250" height="35"><p style="text-align:center"><strong><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">處理方式</span></strong></p></td></tr><tr><td rowspan="3" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="91"><p style="text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">物件管理</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="102"><p style="text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">修改物件資料</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="250"><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">註冊信箱來信，或信箱內主動提供</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">項以上 會員資料且已核實的可直接修改&nbsp;</span></p><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">非註冊信箱來信，需回撥註冊</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">/</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">刊登電話確認</span></p><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">3.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">電話聯絡不到，回信核實資料後再處理</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="102"><p style="text-align: center; margin-top: 10px;"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">上傳圖片</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="250"><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">註冊信箱來信，可直接上傳</span></p><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">有來電記錄，客服請會員寄信至信箱的，可直接上傳</span></p><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">3.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">會員信箱內有主動提供</span><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">2</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">項以上會員資料且已核實的，可直接上傳</span></p><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">4.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">以上都不符合，需回撥註冊</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">/</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">刊登電話確認</span></p><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">5.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">電話聯絡不到，回信核實資料後再處理</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="102"><p style="text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">刊登錯誤退點</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="250"><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; </span></span><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">回撥註冊/刊登電話確認</span></p><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">電話聯絡不到，回信確認電話方便接聽時間，或請會員來電處理</span></p><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">PS：聯絡無人接聽，若內容明確要取消，可直接取消告知資料會隨之刪除</span></p></td></tr><tr><td rowspan="3" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="91"><p style="text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">會員管理</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="102"><p style="text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">忘記密碼</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="250"><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">回復告知解決方案或電話聯絡會員處理</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="102"><p style="text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">修改身份</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="250"><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">回復告知解決方案或電話聯絡會員處理</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="102"><p style="text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">修改資料</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="250"><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">回復告知解決方案或電話聯絡會員處理</span></p></td></tr><tr><td rowspan="2" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="91"><p style="text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">儲值相關</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="102"><p style="text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">儲值未入賬</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="250"><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">查詢款項是否已入賬，已入賬則直接回復說明</span></p><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">查詢未入賬，確認系統無異常，請會員提供相關儲值證明</span></p><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">3.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">轉交專員處理，并將處理結果回復會員說明</span></p></td></tr><tr><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="102"><p style="text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">退款</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="250"><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; </span></span><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">回撥註冊/刊登電話確認</span></p><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">電話聯絡不到，回信確認電話方便接聽時間，或請會員來電處理</span></p></td></tr><tr><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="91"><p style="text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">發票問題</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="102"><p style="text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">修改發票資料</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="250"><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">註冊信箱來信，或信箱內主動提供</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">項以上會員資料且已核實的可直接修改</span></p><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">非註冊信箱來信，需回撥註冊</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">/</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">刊登電話確認</span></p><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">3.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">電話聯絡不到，回信核實資料後再處理</span></p></td></tr><tr><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="91"><p style="text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">咨詢問題</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="102"><p style="text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">其他咨詢問題</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="250"><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">若回復內容中不涉及會員個資，可直接進行回復</span></p><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">資訊問題涉及查詢、提供會員個人、物件資料等，需按上述處理方式處理</span></p></td></tr><tr style=";height:68px"><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="91" height="68"><p style="text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">提供資料</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="102" height="68"><p><span style="font-size:13px;font-family:'微软雅黑',sans-serif">通過信箱提供資料給客服</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="250" height="68"><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">會員提交的中獎資料，核實無誤，提交至媒體採購部留存後，即時刪除</span></p><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">客服寄送含有會員個資的檔案，需進行加密。密碼通過電話或事先約定的方式告知。</span></p></td></tr></tbody></table><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">曹超雲</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-27 12:00:03</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin-bottom: 5px; margin-top: 10px;"><span style="font-size: 14px;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">4、操作步驟</span></strong></span></p><p style="margin-bottom: 5px; margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4.1&nbsp; 一般問題回覆步驟</span></p><p><img src="/upload/question/20201027/1603771117128154.png" alt="image.png"></p><p><img src="/upload/question/20201027/1603771140647325.png" alt="image.png"></p><p><img src="/upload/question/20201027/1603771150542508.png" alt="image.png"></p><p style="margin-top: 10px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4.2&nbsp; 跟進類問題回覆步驟</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &nbsp;</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;<img src="/upload/question/20201027/1603771171711257.png" alt="image.png"></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><img src="/upload/question/20201027/1603771181200871.png" alt="image.png"></span></p><p><br></p><p><img src="/upload/question/20201027/1603771200593314.png" alt="image.png"></p></div></div> <div><span class="button">我要編輯</span></div></li></ul> <h3 class="more-answer">我要補充</h3> <div style=""><div id="editor_45805650802740104" class="edui-default" style=""><div id="edui1" class="edui-editor  edui-default" style="width: 100%; z-index: 999;"><div id="edui1_toolbarbox" class="edui-editor-toolbarbox edui-default" style=""><div id="edui1_toolbarboxouter" class="edui-editor-toolbarboxouter edui-default"><div class="edui-editor-toolbarboxinner edui-default"><div id="edui2" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui2&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui3" class="edui-box edui-button edui-for-fullscreen edui-default"><div id="edui3_state" onmousedown="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui3_body" unselectable="on" title="全屏" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui3&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui3&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui4" class="edui-box edui-button edui-for-source edui-default"><div id="edui4_state" onmousedown="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui4_body" unselectable="on" title="源代码" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui4&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui4&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui5" class="edui-box edui-button edui-for-undo edui-default"><div id="edui5_state" onmousedown="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui5_body" unselectable="on" title="撤销" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui5&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui5&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui6" class="edui-box edui-button edui-for-redo edui-default"><div id="edui6_state" onmousedown="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui6_body" unselectable="on" title="重做" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui6&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui6&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui7" class="edui-box edui-button edui-for-simpleupload edui-default"><div id="edui7_state" onmousedown="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui7_body" unselectable="on" title="单图上传" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui7&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui7&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"><iframe style="display: block; width: 20px; height: 20px; overflow: hidden; border: 0px; margin: 0px; padding: 0px; position: absolute; top: 0px; left: 0px; opacity: 0; cursor: pointer;"></iframe></div></div></div></div></div><div id="edui8" class="edui-box edui-button edui-for-imageleft edui-default"><div id="edui8_state" onmousedown="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui8_body" unselectable="on" title="左浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui8&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui8&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui9" class="edui-box edui-button edui-for-imageright edui-default"><div id="edui9_state" onmousedown="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui9_body" unselectable="on" title="右浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui9&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui9&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui10" class="edui-box edui-button edui-for-imagecenter edui-default"><div id="edui10_state" onmousedown="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui10_body" unselectable="on" title="居中" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui10&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui10&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui11" class="edui-box edui-separator  edui-default"></div><div id="edui12" class="edui-box edui-button edui-for-date edui-default"><div id="edui12_state" onmousedown="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui12_body" unselectable="on" title="日期" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui12&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui12&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui13" class="edui-box edui-button edui-for-time edui-default"><div id="edui13_state" onmousedown="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui13_body" unselectable="on" title="时间" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui13&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui13&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui14" class="edui-box edui-button edui-for-horizontal edui-default"><div id="edui14_state" onmousedown="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui14_body" unselectable="on" title="分隔线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui14&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui14&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui15" class="edui-box edui-separator  edui-default"></div><div id="edui16" class="edui-box edui-splitbutton edui-for-inserttable edui-default"><div title="插入表格" id="edui16_state" onmousedown="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui16_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onArrowClick();"></div></div></div></div><div id="edui19" class="edui-box edui-button edui-for-deletetable edui-default"><div id="edui19_state" onmousedown="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui19_body" unselectable="on" title="删除表格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui19&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui19&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui20" class="edui-box edui-button edui-for-insertrow edui-default"><div id="edui20_state" onmousedown="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui20_body" unselectable="on" title="前插入行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui20&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui20&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui21" class="edui-box edui-button edui-for-insertcol edui-default"><div id="edui21_state" onmousedown="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui21_body" unselectable="on" title="前插入列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui21&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui21&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui22" class="edui-box edui-button edui-for-mergeright edui-default"><div id="edui22_state" onmousedown="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui22_body" unselectable="on" title="右合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui22&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui22&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui23" class="edui-box edui-button edui-for-mergedown edui-default"><div id="edui23_state" onmousedown="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui23_body" unselectable="on" title="下合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui23&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui23&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui24" class="edui-box edui-button edui-for-deleterow edui-default"><div id="edui24_state" onmousedown="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui24_body" unselectable="on" title="删除行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui24&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui24&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui25" class="edui-box edui-button edui-for-deletecol edui-default"><div id="edui25_state" onmousedown="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui25_body" unselectable="on" title="删除列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui25&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui25&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui26" class="edui-box edui-button edui-for-splittorows edui-default"><div id="edui26_state" onmousedown="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui26_body" unselectable="on" title="拆分成行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui26&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui26&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui27" class="edui-box edui-button edui-for-splittocols edui-default"><div id="edui27_state" onmousedown="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui27_body" unselectable="on" title="拆分成列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui27&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui27&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui28" class="edui-box edui-button edui-for-splittocells edui-default"><div id="edui28_state" onmousedown="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui28_body" unselectable="on" title="完全拆分单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui28&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui28&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui29" class="edui-box edui-button edui-for-mergecells edui-default"><div id="edui29_state" onmousedown="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui29_body" unselectable="on" title="合并多个单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui29&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui29&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui36" class="edui-box edui-button edui-for-edittable edui-default"><div id="edui36_state" onmousedown="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui36_body" unselectable="on" title="表格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui36&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui36&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui41" class="edui-box edui-button edui-for-edittd edui-default"><div id="edui41_state" onmousedown="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui41_body" unselectable="on" title="单元格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui41&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui41&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui42" class="edui-box edui-separator  edui-default"></div><div id="edui43" class="edui-box edui-menubutton edui-for-lineheight edui-default"><div title="行间距" id="edui43_state" onmousedown="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui43_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onArrowClick();"></div></div></div></div><div id="edui52" class="edui-box edui-menubutton edui-for-rowspacingtop edui-default"><div title="段前距" id="edui52_state" onmousedown="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui52_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onArrowClick();"></div></div></div></div><div id="edui59" class="edui-box edui-menubutton edui-for-rowspacingbottom edui-default"><div title="段后距" id="edui59_state" onmousedown="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui59_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onArrowClick();"></div></div></div></div></div><div id="edui66" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui66&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui67" class="edui-box edui-combox edui-for-fontfamily edui-default"><div title="字体" id="edui67_state" onmousedown="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui67_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onButtonClick(event, this);">字体</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onArrowClick();"></div></div></div></div><div id="edui80" class="edui-box edui-combox edui-for-customstyle edui-default"><div title="自定义标题" id="edui80_state" onmousedown="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui80_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onButtonClick(event, this);">自定义标题</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onArrowClick();"></div></div></div></div><div id="edui86" class="edui-box edui-combox edui-for-paragraph edui-default"><div title="段落格式" id="edui86_state" onmousedown="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui86_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onButtonClick(event, this);">段落格式</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onArrowClick();"></div></div></div></div><div id="edui95" class="edui-box edui-combox edui-for-fontsize edui-default"><div title="字号" id="edui95_state" onmousedown="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui95_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onButtonClick(event, this);">字号</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onArrowClick();"></div></div></div></div><div id="edui110" class="edui-box edui-button edui-for-link edui-default"><div id="edui110_state" onmousedown="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui110_body" unselectable="on" title="超链接" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui110&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui110&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui111" class="edui-box edui-button edui-for-indent edui-default"><div id="edui111_state" onmousedown="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui111_body" unselectable="on" title="首行缩进" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui111&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui111&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui112" class="edui-box edui-button edui-for-bold edui-default"><div id="edui112_state" onmousedown="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui112_body" unselectable="on" title="加粗" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui112&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui112&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui113" class="edui-box edui-splitbutton edui-for-forecolor edui-default edui-colorbutton"><div title="字体颜色" id="edui113_state" onmousedown="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui113_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui113_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onArrowClick();"></div></div></div></div><div id="edui116" class="edui-box edui-splitbutton edui-for-backcolor edui-default edui-colorbutton"><div title="背景色" id="edui116_state" onmousedown="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui116_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui116_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onArrowClick();"></div></div></div></div><div id="edui119" class="edui-box edui-button edui-for-italic edui-default"><div id="edui119_state" onmousedown="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui119_body" unselectable="on" title="斜体" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui119&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui119&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui120" class="edui-box edui-button edui-for-underline edui-default"><div id="edui120_state" onmousedown="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui120_body" unselectable="on" title="下划线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui120&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui120&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui121" class="edui-box edui-button edui-for-strikethrough edui-default"><div id="edui121_state" onmousedown="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui121_body" unselectable="on" title="删除线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui121&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui121&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui122" class="edui-box edui-button edui-for-superscript edui-default"><div id="edui122_state" onmousedown="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui122_body" unselectable="on" title="上标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui122&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui122&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui123" class="edui-box edui-button edui-for-subscript edui-default"><div id="edui123_state" onmousedown="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui123_body" unselectable="on" title="下标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui123&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui123&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui124" class="edui-box edui-button edui-for-removeformat edui-default"><div id="edui124_state" onmousedown="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui124_body" unselectable="on" title="清除格式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui124&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui124&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui125" class="edui-box edui-button edui-for-blockquote edui-default"><div id="edui125_state" onmousedown="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui125_body" unselectable="on" title="引用" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui125&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui125&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui126" class="edui-box edui-button edui-for-pasteplain edui-default"><div id="edui126_state" onmousedown="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui126_body" unselectable="on" title="纯文本粘贴模式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui126&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui126&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui127" class="edui-box edui-menubutton edui-for-insertorderedlist edui-default"><div title="有序列表" id="edui127_state" onmousedown="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui127_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onArrowClick();"></div></div></div></div><div id="edui140" class="edui-box edui-menubutton edui-for-insertunorderedlist edui-default"><div title="无序列表" id="edui140_state" onmousedown="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui140_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onArrowClick();"></div></div></div></div><div id="edui147" class="edui-box edui-button edui-for-cleardoc edui-default"><div id="edui147_state" onmousedown="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui147_body" unselectable="on" title="清空文档" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui147&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui147&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div></div></div></div><div id="edui1_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;"><div id="edui1_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui1&quot;].showWordImageDialog();">点击上传</div><div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui1&quot;].hideToolbarMsg();">x</div><div id="edui1_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div><div style="height:0;overflow:hidden;clear:both;" class="edui-default"></div></div><div id="edui1_message_holder" class="edui-editor-messageholder edui-default"></div></div><div id="edui1_iframeholder" class="edui-editor-iframeholder edui-default" style="width: 100%; height: 300px; z-index: 999; overflow: hidden;"><iframe id="ueditor_0" width="100%" height="100%" frameborder="0" src="javascript:void(function(){document.open();document.write(&quot;<!DOCTYPE html><html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}
body{margin:8px;font-family:sans-serif;font-size:13px;}p{margin:5px 0;}</style><link rel='stylesheet' type='text/css' href='/static/ueditor/themes/iframe.css'/></head><body class='view' ></body><script type='text/javascript'  id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant0'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);</script></html>&quot;);document.close();}())"></iframe></div><div id="edui1_bottombar" class="edui-editor-bottomContainer edui-default"><table class="edui-default"><tbody class="edui-default"><tr class="edui-default"><td id="edui1_elementpath" class="edui-editor-bottombar edui-default"><div class="edui-editor-breadcrumb">元素路径:</div></td><td id="edui1_wordcount" class="edui-editor-wordcount edui-default">字数统计</td><td id="edui1_scale" class="edui-editor-scale edui-default" style="display: none;"><div class="edui-editor-icon edui-default"></div></td></tr></tbody></table></div><div id="edui1_scalelayer" class="edui-default"></div></div></div> <div class="edit-btn"><button type="button" class="button">提交回答</button> <label data-v-0fe26aab="" for="commonCheckbox" class="checkbox"><input data-v-0fe26aab="" type="checkbox" id="commonCheckbox" name="commonCheckbox"> <i data-v-0fe26aab=""></i> 需要彈窗，請勾選</label></div></div></div> <div class="index-right"><div class="right-bk"><h3 class="com-title"><a href="javascript:;">我的最爱</a></h3> <div class="item-box clearfix"><span>錯誤，請您登入後再操作！</span></div></div> <div class="hotTag right-bk"><h3 class="com-title">热门标签</h3> <div class="hotTag-box item-box clearfix"><p>無數據</p></div></div> <div class="right-bk rank"><h3 class="com-title"><a href="javascript:;">冠軍排行榜</a></h3> <ul><li><div><span class="number">1</span> <img src="/static/timg.jpeg"> <span>王菊芳</span></div> <span class="score">612分</span></li><li><div><span class="number">2</span> <img src="/upload/head/10177.gif?time=1556271256"> <span>黃雪靜</span></div> <span class="score">258分</span></li><li><div><span class="number">3</span> <img src="/static/timg.jpeg"> <span>田霞</span></div> <span class="score">240分</span></li><li><div><span class="number">4</span> <img src="/static/timg.jpeg"> <span>曹超雲</span></div> <span class="score">168分</span></li><li><div><span class="number">5</span> <img src="/static/timg.jpeg"> <span>文宇</span></div> <span class="score">96分</span></li><li><div><span class="number">6</span> <img src="/upload/head/10217.png?time=1548059679"> <span>黃興興</span></div> <span class="score">78分</span></li><li><div><span class="number">7</span> <img src="/static/timg.jpeg"> <span>黃佳麗</span></div> <span class="score">62分</span></li><li><div><span class="number">8</span> <img src="/static/timg.jpeg"> <span>马泽鹏</span></div> <span class="score">46分</span></li><li><div><span class="number">9</span> <img src="/static/timg.jpeg"> <span>陳敏南</span></div> <span class="score">40分</span></li><li><div><span class="number">10</span> <img src="/static/timg.jpeg"> <span>Cs-刘婷</span></div> <span class="score">10分</span></li><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></ul></div> <div class="activeUser right-bk"><h3 class="com-title">活跃用户</h3> <div class="activeUser-box"><a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a></div></div> <img src="data:image/png;base64,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" class="to-top"></div></div> <!----> <!----> <!----> <div class="page-cnt footer"><p>T5客服知识库<span>|</span>zsk.591.com.tw</p></div></div></div><script type="text/javascript" src="/js/manifest.f192b.js"></script><script type="text/javascript" src="/js/vendor.17570.js"></script><script type="text/javascript" src="/js/app.58254.js"></script><div id="edui_fixedlayer" class="edui-default" style="position: fixed; left: 0px; top: 0px; width: 0px; height: 0px;"><div id="edui148" class="edui-popup  edui-bubble edui-default" onmousedown="return false;" style="display: none;"> <div id="edui148_body" class="edui-popup-body edui-default"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank" class="edui-default"></iframe> <div class="edui-shadow edui-default"></div> <div id="edui148_content" class="edui-popup-content edui-default">  </div> </div></div></div></body></html>