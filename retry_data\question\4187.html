<html class=" "><head><meta charset="utf-8"><title>591客服知識庫</title><link rel="stylesheet" href="/static/font/font-awesome.min.css"><link href="/css/app.e5aa4.css" rel="stylesheet"><script type="text/javascript" charset="utf-8" async="" src="/js/questionDetail.d0a6b.js"></script><style type="text/css">.que-detail-page .index-left .button{width:74px;height:32px;line-height:32px;font-size:13px;margin-right:10px}.que-detail-page .index-left .button:hover{color:#fff}.que-detail-page .detail-box{padding:20px 30px;border:1px solid #e5e5e5;border-radius:4px}.que-detail-page .detail-box h3{font-size:18px;color:#333;margin-bottom:10px}.que-detail-page .detail-box h3 span{color:#f77d24}.que-detail-page .detail-box .add-fav{cursor:pointer}.que-detail-page .d-b-menu{margin:15px 0 25px;font-size:13px;color:#999}.que-detail-page .d-b-menu b{display:inline-block;font-weight:400;margin:0 10px}.que-detail-page .d-b-menu .r{float:right;color:#f77d24}.que-detail-page .d-b-menu .r .fa{margin-right:5px;font-size:16px}.que-detail-page .d-b-cnt{margin-bottom:25px;font-size:13px;color:#666}.que-detail-page .submit-ask{display:block;margin:32px auto 0}.que-detail-page .reply-list{margin:30px 0 10px;overflow:hidden}.que-detail-page .reply-list .reply-item{padding:15px 30px 25px;margin-bottom:30px;border-radius:4px;border:1px solid #e5e5e5}.que-detail-page .reply-list .reply-item h4{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;line-height:50px;font-size:13px}.que-detail-page .reply-list .reply-item h4 img{width:40px;height:40px;border-radius:50%}.que-detail-page .reply-list .reply-item h4 .time{color:#999}.que-detail-page .reply-list .reply-item h4 .button{-ms-flex-item-align:center;align-self:center}.que-detail-page .reply-list .reply-item h4 .delete{color:#666;cursor:pointer}.que-detail-page .reply-list .reply-item .reply-detail{padding:20px 0 30px}.que-detail-page .more-answer{font-size:18px;color:#666;margin-bottom:5px}.que-detail-page .editor-content table{width:100%;border:1px solid #ddd}.que-detail-page .editor-content table td,.que-detail-page .editor-content table th{border:1px solid #ddd;padding:2px 8px;line-height:2}.que-detail-page .editor-content img{max-width:100%;vertical-align:text-bottom}.que-detail-page .edit-btn{text-align:center;margin-top:20px}.que-detail-page .edit-btn .edit-cancle{background:#aaa}</style><style type="text/css">.head-info{line-height:26px;background:#f5f5f5;font-size:13px;color:#999}.head-info a{margin-left:20px;color:#333}.head-search{margin-top:22px}.head-search img{float:left}.head-search .srh-box{float:left;margin:20px 0 0 20px}.head-search .srh-box .srh-input{float:left;width:260px;height:38px;line-height:38px;border:2px solid #37a508;border-radius:4px 0 0 4px}.head-search .srh-box a,.head-search .srh-box button{float:left;width:80px;height:38px;line-height:36px;font-size:16px;color:#fff}.head-search .srh-box .srh-btn{border-radius:0 4px 4px 0}.head-search .srh-box .ask-btn{margin-left:10px}.head-nav{float:left;margin:20px 0 0 60px;color:#fff;font-size:16px;line-height:36px}.head-nav a{display:block;height:100%;color:#333}.head-nav li{position:relative;float:left;margin-right:35px}.head-nav .active a,.head-nav li:hover a{color:#30b940}.head-nav .active:after,.head-nav li:hover:after{display:block;content:"";height:3px;width:100%;background:#30b940}.head-pic{width:100%;height:90px;margin-top:20px;background:url(/static/banner.png) top no-repeat}</style><style type="text/css">.top-breadcrumb{height:20px;margin-top:10px;margin-bottom:40px;font-size:13px;color:#666}.top-breadcrumb .t-b-icon{color:#30b940}.top-breadcrumb span{margin-left:8px}.top-breadcrumb span .fa{font-size:16px;margin-left:8px}.top-breadcrumb span:first-child{margin-left:0}.top-breadcrumb .now{color:#30b940}</style><script type="text/javascript" src="/static/ueditor/ueditor.config.js?20210319" id="configScriptTag"></script><script type="text/javascript" src="/static/ueditor/ueditor.all.js?20210319" id="editorScriptTag"></script><style type="text/css">.checkbox[data-v-0fe26aab]{word-spacing:4px;vertical-align:bottom;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.checkbox i[data-v-0fe26aab]{display:inline-block;width:14px;height:14px;border-radius:2px;border:1px solid #ccc;background:#fff;position:relative;top:1px}.checkbox input[data-v-0fe26aab]{opacity:0;width:0;height:0;margin:0}.checkbox input:checked+i[data-v-0fe26aab]{background:#30b940;border-color:#30b940}.checkbox input:checked+i[data-v-0fe26aab]:before{content:"";position:absolute;left:2px;top:1px;width:11px;height:6px;border-bottom:2px solid #fff;border-left:2px solid #fff;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}</style><style type="text/css">.popbox{position:fixed;top:50%;left:50%;z-index:1011;background:#fff;border-radius:6px;overflow:hidden}.popbox h3{font-size:16px;line-height:40px;padding:0 20px}.popbox h3 span{float:right;color:#666;font-size:13px;cursor:pointer}.popbox .pb-cnt{padding:0 20px;font-size:13px}.popbox .pb-btn-area{position:absolute;bottom:0;left:0;z-index:1;width:100%;text-align:right;padding:0 20px 20px 0}.popbox .pb-btn-area button{height:32px;line-height:32px;border:0;background:#fff;cursor:pointer;border-radius:6px;margin-left:15px;padding:0 15px}.popbox .pb-btn-area button.pb-btn-ok{background:#daf3cf;color:#279005}.popbox .pb-btn-area button.pb-btn-ok:hover{background:#279005;color:#fff}</style><style type="text/css">.shadow{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1010;background:#000}</style><style type="text/css">.footer{margin-top:70px;margin-bottom:20px;padding-top:20px;text-align:center;color:#999;border-top:1px solid #f5f5f5}.footer span{margin:0 10px}</style><script src="/static/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript" defer="defer"></script><link href="/static/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet"><script src="/static/ueditor/third-party/codemirror/codemirror.js" type="text/javascript" defer="defer"></script><link rel="stylesheet" type="text/css" href="/static/ueditor/third-party/codemirror/codemirror.css"><script src="/static/ueditor/third-party/zeroclipboard/ZeroClipboard.js" type="text/javascript" defer="defer"></script><style type="text/css">.activeUser,.favorite,.hotTag,.rank{margin-bottom:40px}</style><style type="text/css">.rank .rank-page{height:60px;line-height:60px;font-size:18px;margin-bottom:20px;text-align:center;background:#fafafa;border-radius:5px}.rank ul{padding:0 20px}.rank li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;border-bottom:1px dotted #f5f5f5;line-height:50px}.rank li .flag{position:relative;display:inline-block;width:20px;height:30px;background:#476472;vertical-align:middle;margin-right:12px;color:#fff;text-align:center;line-height:20px;font-size:13px;font-style:normal}.rank li .flag:after{content:"";position:absolute;bottom:-4px;left:-4px;border:14px solid transparent;border-bottom-color:#fff}.rank li:first-child .flag{background:#fb6757}.rank li:nth-child(2) .flag{background:#fac525}.rank li:nth-child(3) .flag{background:#00a9fb}.rank li img{width:36px;height:36px;border-radius:50%;margin-right:12px}</style><style type="text/css">.activeUser .activeUser-box a{display:inline-block;width:64px;height:64px;border-radius:50%;overflow:hidden;margin:0 6px 12px}</style><style type="text/css">.to-top{display:none;position:fixed;bottom:150px;z-index:5;width:50px;height:50px;cursor:pointer}</style></head><body style=""><div id="app" style=""><div class="que-detail-page" style=""><div class="wiki-hd"><div class="head-info"><p class="page-cnt">
      您好，欢迎来到591客服知识库
      <a href="https://www.591.com.tw/admin.php?new_url=https://zsk.591.com.tw">[请登入]</a> <!----></p></div> <div class="page-cnt head-search clearfix"><a href="/" class="router-link-active"><img src="/static/logo.png"></a> <ul class="head-nav clearfix"><li class="router-link-active active"><a href="/">591房屋交易网</a></li> <li class=""><a href="/life">客服充电站</a></li> <li class=""><a href="/notification">通知事项</a></li></ul> <div class="srh-box"><input type="text" placeholder="请输入搜索内容" class="srh-input"> <button type="button" class="button srh-btn">搜索</button> <a href="/ask/591" class="button button-orange ask-btn">提問</a></div></div> <div class="head-pic"></div></div> <div class="top-breadcrumb com-width"><span><a href="/collection/591/1209" class="">客服督導相關<i class="fa fa-angle-double-right"></i></a></span><span><a href="/collection/591/1209/1655" class="">崗位作業指導<i class="fa fa-angle-double-right"></i></a></span><span><strong class="now">问题详情</strong></span></div> <div class="com-width clearfix" style=""><div class="index-left" style=""><div class="detail-box"><h3><!---->招聘作業指導書</h3> <p class="clearfix d-b-menu"><span class="l">
            文宇&nbsp;&nbsp;&nbsp;浏览48次&nbsp;&nbsp;&nbsp;2020-07-01 12:22:14
          </span> <span class="r"><span class="add-fav"><i class="fa fa-heart"></i>加入最爱</span></span></p> <div class="d-b-cnt editor-content big-img-parent"><div><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1、招聘流程與規範<br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2、招聘技巧</span></p><p><br></p></div></div> <div class="d-b-button"><a href="/edit/591/4187" class="button">编辑</a> <button class="button button-orange">关闭提问</button></div></div> <div class="hr-line"></div> <ul class="reply-list big-img-parent"><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-07-07 10:34:14</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1、招聘流程：</span><br></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><img src="/upload/question/20200707/1594089208884354.png" alt="image.png"></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><img src="/upload/question/20200707/1594089227370150.png" alt="image.png"></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><img src="/upload/question/20200707/1594089249290885.png" alt="image.png"></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-07-07 10:36:28</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p class="MsoListParagraph" style="margin-left:32px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 14px;">l<span style="font-style: normal; font-variant: normal; font-weight: normal; font-stretch: normal; font-size: 9px; line-height: normal;">&nbsp;&nbsp; </span></span><strong><span style="font-size: 14px;">流程管理规范</span></strong></span></p><p><span style="color: #666666; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span><br></p><p><span style="color: #666666; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">编号</span></strong></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">任务</span></strong></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">目的与程序</span></strong></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">1</span></strong></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:black">提出招聘需求，明确用人标准</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:black">提出招聘需求，明确用人标准</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">明确招聘的人数、明确需招哪一类型的人才</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">客服主管</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">每季度最后一个月底提出</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">依照人员异动及事务量分析人力需求及用人标准 </span></p></td></tr><tr style=";height:67px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="67"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">详细内容</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">1</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">、依照人员异动及事务评估，分析人力需求</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">2</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">、依照现场人员配对与团队讨论需招聘的用人标准</span></p></td></tr></tbody></table><p><strong><span style="font-size:13px;font-family: 'Microsoft YaHei',sans-serif">&nbsp;</span></strong></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">编号</span></strong></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">任务</span></strong></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">目的与程序</span></strong></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">2</span></strong></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:black">填写招聘申请表</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:black">填写招聘申请表单</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">签核招聘申请表，启动招聘计划</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">督导</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">每季度最后一个月底提出</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">依照招聘申请填写并签核</span></p></td></tr><tr style=";height:651px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="651"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">详细内容</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">1、 </span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">招聘需求下载路径：公司文档库</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">---</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">【</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">30</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">】表格</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">---</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">【</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">3005</span><span style="font-size:   13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">】招聘</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">—</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">下载【</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">3005-2</span><span style="font-size:13px;line-height:   150%;font-family:'Microsoft YaHei',sans-serif">】招聘申请表，走流程签名后提交至孙秀敏；并</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">RTX</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">提供给孙秀敏接受简历的邮箱</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">2、 </span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">招聘申请表填写注意事项：</span></p><p class="MsoListParagraph" style="margin-left:64px;text-align:justify;text-justify:inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">1）<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp; &nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">填表日期：填写当天时间</span></p><p style="margin-left:64px;text-align:justify;text-justify:   inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">2）<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp; &nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">招聘人数：填写主管申请人数</span></p><p style="margin-left:64px;text-align:justify;text-justify:   inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">3）<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp; &nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">申请原因：勾选符合的选项</span></p><p style="margin-left:64px;text-align:justify;text-justify:   inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">4）<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp; &nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">申请原因：离职补充（离职人员姓名），新增编制（业务类型）人员調動、人員儲備等，依照申請招聘原因填寫即可</span></p><p style="margin-left:64px;text-align:justify;text-justify:   inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">5）<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">招聘渠道：外部</span></p><p style="margin-left:64px;text-align:justify;text-justify:   inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">6）<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp; &nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">招聘时间：从填表当天起算三个月</span></p><p style="margin-left:64px;text-align:justify;text-justify:   inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">7）<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">学历：高中中专以上</span></p><p style="margin-left:64px;text-align:justify;text-justify:   inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">8）<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">年龄：20岁以上</span></p><p style="margin-left:64px;text-align:justify;text-justify:   inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">9）<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp; &nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">岗位职责、任职要求按网站发布的要求做填写</span></p><p style="margin-left:64px;text-align:justify;text-justify:   inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">10） </span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">职位等级：7级</span></p><p style="margin-left:64px;text-align:justify;text-justify:   inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">11） </span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">是否管理部筛选简历：不需要</span></p><p style="margin-left:64px;text-align:justify;text-justify:   inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">12） </span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">是否采购计算机：与现场主任确认</span></p><p style="margin-left:64px;text-align:justify;text-justify:   inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">13） </span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">备注：空白</span></p><p style="margin-left:64px;text-align:justify;text-justify:   inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">14） </span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">签核：部门经理、冯总或张总、余姐</span></p><p style="text-align:justify;text-justify:inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">如下图：</span></p><p style="text-align:justify;text-justify:inter-ideograph;line-height:150%"><img src="/upload/question/20200707/1594089426102832.png" alt="image.png"></p></td></tr></tbody></table><p><strong><span style="font-size:13px;font-family: 'Microsoft YaHei',sans-serif">&nbsp;</span></strong></p><p><strong><span style="font-size:13px;font-family: 'Microsoft YaHei',sans-serif">&nbsp;</span></strong></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">编号</span></strong></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">任务</span></strong></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">目的与程序</span></strong></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">3</span></strong></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:black">发布招聘信息</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">发布招聘信息</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">启动招聘发布招聘信，招聘途径网站（</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">58</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">同城、</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">BOSS</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">网、拉钩网等）</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">人力资源（孙秀敏）</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">提交招聘申请单后当天或隔天</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">由人资进行发布</span></p></td></tr><tr style=";height:34px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="34"><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:black">1、 </span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:black">发布成功后，人力资料会回复结果</span></p><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:black">2、 </span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:black">督导需查看网站发布的信息是否成功</span></p></td></tr></tbody></table><p><strong><span style="font-size:13px;font-family: 'Microsoft YaHei',sans-serif">&nbsp;</span></strong></p><table cellspacing="0" cellpadding="0" width="680"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">编号</span></strong></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">任务</span></strong></p></td><td width="350" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">目的与程序</span></strong></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">4</span></strong></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:black">筛选简历</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">What</span></p></td><td width="435" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">筛选简历</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">Why</span></p></td><td width="435" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">筛选符合团队的人才</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">Who</span></p></td><td width="435" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">督导</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">When</span></p></td><td width="435" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">面试者会主动投递简历至邮箱，有收到则及时筛选</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">How</span></p></td><td width="435" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">依照简历录入标准进行筛选</span></p></td></tr><tr style=";height:34px"><td width="350" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="34"><p class="MsoListParagraph" style="margin-left:21px;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">1、<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">频率：</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">因</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">3个部门一起筛选，故需要及时跟进，随时查看简历情况。若非3个部门一起筛选则可每日筛选三次（早上、中午、下班前）</span></p><p class="MsoListParagraph" style="margin-left:21px;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">2、<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">筛选简历标准</span></p><p style="line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">年龄：</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">18-25年</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">区间</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">学历：中专、高中、大专应届生</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">工作要求：客服、文职或未明确标识（其他符合客服特质均可）</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">地点要求：罗湖、福田等</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">期望薪资：5000—7000</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">工作经验：是否匹配或关联</span></p></td></tr></tbody></table><p><span style="color: #666666; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-07-21 13:56:32</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="text-align:center;line-height:250%"><strong><span style="font-size:27px;line-height:250%;font-family:'Microsoft YaHei',sans-serif">T5-</span></strong><strong><span style="font-size: 27px;line-height:250%;font-family:'Microsoft YaHei',sans-serif">客服面试技巧</span></strong></p><p style="line-height: 250%;"><strong><span style="font-size:17px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">&nbsp; &nbsp; &nbsp; 一、<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-weight: normal; font-stretch: normal; font-size: 9px; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></strong><strong><span style="font-size:17px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">面试的环境</span></strong></p><p style="text-indent:28px;line-height:150%"><span style="font-size:14px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#767171">在会议室等较安静的场所，关闭手机，避免干扰</span></p><p style="text-indent:28px;line-height:150%"><strong><span style="font-size:17px;line-height:150%;font-family:'Microsoft YaHei',sans-serif"><br></span></strong></p><p style="text-indent:28px;line-height:150%"><strong><span style="font-size:17px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">二、<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-weight: normal; font-stretch: normal; font-size: 9px; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></strong><strong><span style="font-size:17px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">面试的座椅摆设</span></strong></p><p style="text-indent:28px"><span style="font-size:14px;font-family:'Microsoft YaHei',sans-serif;color:#767171">用一样高度的椅子（圆桌、方桌等）</span></p><p style="text-indent:28px"><strong><span style="font-size:17px;line-height:150%;font-family:'Microsoft YaHei',sans-serif"><br></span></strong></p><p style="text-indent:28px"><strong><span style="font-size:17px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">三、<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-weight: normal; font-stretch: normal; font-size: 9px; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></strong><strong><span style="font-size:17px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">开场：</span></strong><strong> </strong><strong><span style="font-size:17px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">寒暄（建立融洽的氛围）</span></strong></p><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:14px;font-family:'Microsoft YaHei',sans-serif;color:#767171">目的</span><span style="font-size: 14px;font-family:'Microsoft YaHei',sans-serif;color:#767171">: </span><span style="font-size:14px;font-family:'Microsoft YaHei',sans-serif;color:#767171">初步相识</span><span style="font-size:14px;font-family:'Microsoft YaHei',sans-serif;color:#767171">, </span><span style="font-size:14px;font-family:'Microsoft YaHei',sans-serif;color:#767171">让应聘者感到自然</span><span style="font-size:14px;font-family: 'Microsoft YaHei',sans-serif;color:#767171">, </span><span style="font-size:14px;font-family:'Microsoft YaHei',sans-serif;color:#767171">友好与礼貌</span><span style="font-size:14px;font-family:'Microsoft YaHei',sans-serif;color:#767171">, </span><span style="font-size:14px;font-family:'Microsoft YaHei',sans-serif;color:#767171">同时为公司建立良好的形象。营造一种轻松的沟通氛围，达到开诚布公、知己知彼的沟通境界。</span></p><p style="text-align:justify;text-justify:inter-ideograph"><strong><span style="font-size:14px;font-family:'Microsoft YaHei',sans-serif">内容: </span></strong></p><table cellspacing="0" cellpadding="0" width="604"><tbody><tr class="firstRow"><td width="85" valign="top" style="border: 1px solid windowtext; background: rgb(247, 202, 172); padding: 0px 7px;"><p style="text-align:center"><strong><span style=";font-family:'Microsoft YaHei',sans-serif;color:black">类型</span></strong></p></td><td width="284" valign="top" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(247, 202, 172); padding: 0px 7px;"><p style="text-align:center"><strong><span style=";font-family:'Microsoft YaHei',sans-serif;color:black">问题</span></strong></p></td><td width="236" valign="top" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(247, 202, 172); padding: 0px 7px;"><p style="text-align:center"><strong><span style=";font-family:'Microsoft YaHei',sans-serif;color:black">观察点</span></strong></p></td></tr><tr><td width="85" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; background: white; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:black">欢迎面试者</span></strong></p></td><td width="284" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">欢迎王小姐对本公司认可，前来参加面试</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">观察面试者的表情、回复内容</span></p></td></tr><tr><td width="85" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; background: white; padding: 0px 7px;"><p style="text-align:center"><strong><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif;color:black">自我介绍</span></strong></p></td><td width="284" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">请王小姐先自我介绍下</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">观察应聘者的语言是否流畅、有条理、层次分明，讲话的风度如何</span></p></td></tr><tr><td width="85" rowspan="5" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; background: white; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:black">稳定性</span></strong></p></td><td width="284" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">你为什么离职？你认为原单位有什么不足？或者公司好的地方有哪些？</span></em><em> </em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">观察对方的眼睛，判断对方是否说实话。把原单位说得一文不值的人不宜录用</span></p></td></tr><tr style=";height:38px"><td width="284" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="38"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">对自己未来短，长期的规划</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="38"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者的想法及以后的发展空间</span></p></td></tr><tr style=";height:48px"><td width="284" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="48"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">从什么时间开始找工作的？最近都在找哪一类型的工作呢？有收到录入通知吗？不考虑的原因是？觉得自己相比较下来比较喜欢哪一种工作？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="48"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者的求职意向及比较在乎哪一个方面</span></p></td></tr><tr style=";height:48px"><td width="284" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="48"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">查看您的简历有频繁换工作，能谈谈为什么吗？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="48"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者的稳定性</span></p></td></tr><tr style=";height:48px"><td width="284" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="48"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">您会比较在意公司的哪方面？（薪资</span></em><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">/</span></em><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">福利、团队管理、同事相处、晋升等）</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="48"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者对公司的评估，结合面试者回复判断是否符合公司</span></p></td></tr><tr style=";height:48px"><td width="85" rowspan="3" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" height="48"><strong style="text-align: justify; white-space: normal; background-color: rgb(255, 255, 255);"><span style="font-size: 13px; font-family: &quot;Microsoft YaHei&quot;, sans-serif;">稳定性</span></strong></td><td width="284" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="48"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">有看到您的专业是</span></em><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">**</span></em><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">？请问为什么没有找对口的工作呢？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="48"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者定位</span></p></td></tr><tr style=";height:48px"><td width="284" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="48"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">请问您最近都在找哪些类型的工作？有面试过哪些工作？你觉得自己比较喜欢哪一种工作？请问有收到录入通知吗？那您打算入职吗？还在考虑是否入职的原因是什么？如果同时收到我们公司录入通知你会选择去哪一家？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="48"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者关注公司的哪一个方面，对比其他公司是否对她会更有优势</span></p></td></tr><tr style=";height:48px"><td width="284" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="48"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">您喜欢什么样的领导？你是怎么评价你之前的公司及领导的？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="48"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者是否客观评价</span></p></td></tr></tbody></table><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-07-21 13:57:16</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p class="MsoListParagraph" style="margin-left:34px"><span style="font-style: normal; font-variant: normal; font-stretch: normal; line-height: normal; font-size: 17px;"><font face="Microsoft YaHei, sans-serif"><b>四</b></font></span><span style="font: 9px &quot;Times New Roman&quot;;">&nbsp; &nbsp; &nbsp; &nbsp;</span><strong><span style="font-size:17px;font-family:'Microsoft YaHei',sans-serif">核心：提问与考核</span></strong></p><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:14px;font-family:'Microsoft YaHei',sans-serif;color:#767171">目的</span><span style="font-size: 14px;font-family:'Microsoft YaHei',sans-serif;color:#767171">: </span><span style="font-size:14px;font-family:'Microsoft YaHei',sans-serif;color:#767171">按照准备好的面试流程</span><span style="font-size:14px;font-family: 'Microsoft YaHei',sans-serif;color:#767171">, </span><span style="font-size:14px;font-family:'Microsoft YaHei',sans-serif;color:#767171">考核应聘者与工作相关的经验与能力</span></p><p style="text-align:justify;text-justify:inter-ideograph"><strong><span style="text-underline:wavetext-decoration:underline;"><span style="font-size:14px;font-family:'Microsoft YaHei',sans-serif">内容</span></span></strong><strong><span style="text-underline:wavetext-decoration:underline;"><span style="font-size:14px;font-family:'Microsoft YaHei',sans-serif">1</span></span></strong><strong><span style="text-underline:wavetext-decoration:underline;"><span style="font-size:14px;font-family:'Microsoft YaHei',sans-serif">：考核个性品质、能力与资质、价值观等</span></span></strong><strong><span style="text-underline:wavetext-decoration:underline;"></span></strong></p><table cellspacing="0" cellpadding="0" width="614"><tbody><tr class="firstRow"><td width="104" valign="top" style="border: 1px solid windowtext; background: rgb(247, 202, 172); padding: 0px 7px;"><p style="text-align:center"><strong><span style=";font-family:'Microsoft YaHei',sans-serif;color:black">类型</span></strong></p></td><td width="274" valign="top" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(247, 202, 172); padding: 0px 7px;"><p style="text-align:center"><strong><span style=";font-family:'Microsoft YaHei',sans-serif;color:black">问题</span></strong></p></td><td width="236" valign="top" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(247, 202, 172); padding: 0px 7px;"><p style="text-align:center"><strong><span style=";font-family:'Microsoft YaHei',sans-serif;color:black">观察点</span></strong></p></td></tr><tr><td width="104" rowspan="2" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">对客服的认识</span></strong></p></td><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">您是如何理解客服这份工作的？</span></em></p><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">客服相关工作会有很多种？</span></em></p><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">您比较喜欢哪一种类型的客服？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">观察面试者的表情、回复内容</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">您觉得从事电话接听，服务型的客服工作，需要具备哪些能力？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">观察应聘者的语言是否流畅、有条理、层次分明，讲话的风度如何</span></p></td></tr><tr><td width="104" rowspan="13" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p style="text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">能力测评</span></strong></p></td><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">请问您有哪些优势能胜任这份客服工作？哪些需要能力需要提升的？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><br></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">你觉得自己如果从事客服会遇到哪些困难跟挑战</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者对自己能力的认识，对客服的深入理解</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">你有何优缺点？有哪些案例可以体现您的优点吗？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">应聘者对自己的判断是否中肯，自信、自卑和自傲倾向如何</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">如果今天您接到一个投诉电话？你觉得应该如何处理好这通投诉？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解对问题的分析能力，思维能力</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">呼叫中心会有质量检查，你的质检成绩如何？属于那种层次的，质检有给您什么建议吗？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解服务质量水平，是否会接受别人建议并改正</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">你的学习能力怎么样？有什么案例能体现您的学习能力比较好的吗？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者的学习能力，记忆能力</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">打字一分钟</span></em><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">/</span></em><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">字有测过吗？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">打字需要达到标准</span><span style="font-size:   12px;font-family:'Microsoft YaHei',sans-serif">40</span><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">个最低</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">你觉得你的什么特质，可以竞选组长</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者对自己的客观评价</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">工作中出现过什么错误吗？举例说明</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者的细心度</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">公司是台湾企业，面向的客人是台湾人，需要学习台湾口音，未来入职可能每一个表达都需要让您去改变，会怎么看对这个问题？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者是否愿意改变自己，调整自己，愿意学习跟挑战</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">如果跟朋友出去玩，一般听谁的建议比较多，谁负责规划？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者是主导还是参与者，了解当跟朋友意见不同时的处理方式</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">在工作中的你觉得最大的成就是什么？是怎么做到的，有什么好的，或需要不断提升的？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解对方对“成就”的理解，了解对方能力的突出点，是否能客观的总结回顾自我</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">你的领导是怎么评价您的？同事是怎么评价您的？朋友是怎么评价您的？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解别人对面试者的看法及观察，面试者是否能客观对待</span></p></td></tr><tr><td width="104" rowspan="4" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p style="text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:black">能力测评</span></strong></p></td><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">你遇到的最大的挫折是什么？当时的情况是怎样的</span></em><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">?</span></em><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">你的感受是什么？你花了多长时间，使用了什么方法从挫折中恢复？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">对待问题的分析能力，对问题的处理态度，了解面试者价值观</span></p></td></tr><tr style=";height:28px"><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">你一般是通过什么方法缓解压力的？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者能否调整好心态</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">你觉得自己在工作效率方面有没有可以提高的地方，你准备如何提高？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者对自我的认识是否客观，是否正确看待问题</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">进到一个陌生的环境，您会如何让自己更快的融入团队？适应这里</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者适应能力</span></p></td></tr><tr><td width="104" rowspan="7" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p style="text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">性格</span></strong></p><p style="text-indent:13px"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">情绪管理</span></strong></p></td><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">你对自己的工作有什么要求</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">判断对方的工作特性</span><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">(</span><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">如追求完美、追求效率</span><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">)</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">平时喜欢做什么？你觉得自己是一个什么样性格的人（内敛、内向、开朗健谈、文静、急躁等）</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解兴趣爱好，了解性格</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">如果客户打上来就一直骂你，您会被会员的情绪影响吗？什么情况下会让您有情绪出现？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者对情绪的管理能力及对问的处理能力</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">周末或者休假的时候，你通常都会做什么呢？经常有朋友邀请你参加聚会或者集体游玩吗？为什么？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者的性格特征</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">你的朋友多吗？都是怎么认识的？你经常跟朋友们在一起吗？通常的情景是怎样的？为什么？ &nbsp; </span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者的性格特征</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">您有没有情绪失控的情况？当时的情境是怎样的？（如果没有）通常你会采用什么样的方式控制自己的情绪？你觉得自己可能在什么样的情况下情绪失控？ &nbsp;  </span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者的情绪管理能力</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">你有没有特别紧张的时候？能举个例子吗？当时的情况是怎样的？你是怎么克服的？自己有什么样的体会？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者的心理应变能力</span></p></td></tr><tr><td width="104" rowspan="4" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p><strong><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">规范制度</span></strong></p></td><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">客服是规范性比较强的一个行业，比如上班不能上外网、玩手机、会有规定的话，你怎么看待这个问题？会不会觉得没有比较约束？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者是否符合管理管理方式</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">术脚步要我们去说：比较开头语，结束语等必须要讲？</span></em></p><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">请问您会不会觉得约束力太高？您会按话术脚步严格执行去做吗？</span></em></p><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">&nbsp;</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者是否符合团队管理</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">您上一个公司有对考勤做要求吗？比如说不能迟到等？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者是否符合管理管理方式</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">您一般是准时还是提前到公司？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者是否符合管理管理方式</span></p></td></tr><tr><td width="104" rowspan="4" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p><strong><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">人际相处</span></strong></p><p><strong><span style="font-size:13px;font-family:   'Microsoft YaHei',sans-serif">与团队配合</span></strong></p></td><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">你的朋友多吗？都是怎么认识的？你经常跟朋友们在一起吗？通常的情景是怎样的？为什么？ &nbsp; </span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者的人际交际能力</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">你觉得自己的人缘怎么样？能举个例子吗？你觉得自己的人缘好</span></em><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">/</span></em><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">不好的原因是什么呢？ </span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者的人际交际能力</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">对于团队合作，你最喜欢和最不喜欢的地方各是什么？为什么？能举个例子详细说一说吗？ &nbsp;  </span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者的协调能力</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">对于团队中不易相处或不配合的同事，你会如何处理？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者的协调能力</span></p></td></tr></tbody></table><p style="line-height:200%"><strong><span style="text-underline:wavetext-decoration:underline;"><span style="font-size:14px;line-height:200%;font-family:'Microsoft YaHei',sans-serif;color:white">内容</span></span></strong><strong><span style="text-underline:wavetext-decoration:underline;"><span style="font-size:14px;line-height:200%;font-family:'Microsoft YaHei',sans-serif">2</span></span></strong><strong><span style="text-underline:wavetext-decoration:underline;"><span style="font-size:14px;line-height:200%;font-family:'Microsoft YaHei',sans-serif">：了解</span></span></strong><strong><span style="text-underline:wavetext-decoration:underline;"><span style="font-size:14px;line-height:200%;font-family:'Microsoft YaHei',sans-serif">/</span></span></strong><strong><span style="text-underline:wavetext-decoration:underline;"><span style="font-size:14px;line-height:200%;font-family:'Microsoft YaHei',sans-serif">核实背景，学历</span></span></strong><strong><span style="text-underline: wavetext-decoration:underline;"><span style="font-size:14px;line-height:200%;font-family: 'Microsoft YaHei',sans-serif">, </span></span></strong><strong><span style="text-underline:wavetext-decoration:underline;"><span style="font-size:14px;line-height:200%;font-family:'Microsoft YaHei',sans-serif">住址，婚否等</span></span></strong></p><table cellspacing="0" cellpadding="0" width="614"><tbody><tr style=";height:40px" class="firstRow"><td width="104" valign="top" style="border: 1px solid windowtext; background: rgb(247, 202, 172); padding: 0px 7px;" height="40"><p style="text-align:center;line-height:200%"><strong><span style=";line-height:200%;font-family:'Microsoft YaHei',sans-serif;color:black">类型</span></strong></p></td><td width="274" valign="top" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(247, 202, 172); padding: 0px 7px;" height="40"><p style="text-align:center;line-height:200%"><strong><span style=";line-height:200%;font-family:'Microsoft YaHei',sans-serif;color:black">问题</span></strong></p></td><td width="236" valign="top" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(247, 202, 172); padding: 0px 7px;" height="40"><p style="text-align:center;line-height:200%"><strong><span style=";line-height:200%;font-family:'Microsoft YaHei',sans-serif;color:black">观察点</span></strong></p></td></tr><tr><td width="104" rowspan="2" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph;text-indent:27px"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">学历</span></strong></p></td><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">有看到您现在是有在读本科，请问是什么专业呢，是否需要经常请假上课？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">观察定位</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">您是应届毕业生，今年才会毕业，毕业证是</span></em><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">6</span></em><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">月份才能拿到对吗？是否需要经常请假会学校办理事务呢？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">观察应聘者的语言是否流畅、有条理、层次分明，讲话的风度如何</span></p></td></tr><tr><td width="104" rowspan="2" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p style="text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">住址</span></strong></p></td><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">您现在是住在</span></em><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">*****</span></em><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">，请问是跟朋友住还是家人一起住呢？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解是否会有距离感影响稳定性</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">过来公司这边会很远吗？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者对自己能力的认识，对客服的深入理解</span></p></td></tr><tr><td width="104" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p style="text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">婚配</span></strong></p></td><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">方便了解您现在有男朋友了吗？准备什么时候结婚？ </span></em></p><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">您是</span></em><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">**</span></em><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">年的？这个年龄家里是否有催结婚相亲？有准备结婚的打算吗？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解是否有计划生孩子</span></p></td></tr><tr><td width="104" rowspan="3" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p style="text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">家庭情况了解</span></strong></p></td><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">您是深圳本地人？方便了解父母是做什么的吗？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解家境关注面试者是否有压力，对工作的态度如何</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">父母对您找工作是否有要求？或者帮您安排工作？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者是否听从父母的规划</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">家里有几个兄弟姐妹？你是最大的吗？是否需要寄钱给父母，负担些生活开销？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者是否有压力，</span></p></td></tr></tbody></table><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-07-21 13:57:53</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p class="MsoListParagraph" style="margin-left:48px"><strong><span style="font-size:17px;font-family:'Microsoft YaHei',sans-serif">五、<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span></strong><strong><span style="font-size:17px;font-family:'Microsoft YaHei',sans-serif">收尾：介绍公司与回答应聘者的问题</span></strong></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif;color:#767171">目的：</span><span style="font-size: 14px;font-family:'Microsoft YaHei',sans-serif;color:#767171">向面试者介绍公司福利待遇、工作内容等</span></p><p style="text-align:justify;text-justify:inter-ideograph"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">内容：</span></strong></p><table cellspacing="0" cellpadding="0" width="614"><tbody><tr class="firstRow"><td width="104" valign="top" style="border: 1px solid windowtext; background: rgb(247, 202, 172); padding: 0px 7px;"><p style="text-align:center"><strong><span style=";font-family:'Microsoft YaHei',sans-serif;color:black">类型</span></strong></p></td><td width="274" valign="top" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(247, 202, 172); padding: 0px 7px;"><p style="text-align:center"><strong><span style=";font-family:'Microsoft YaHei',sans-serif;color:black">问题</span></strong></p></td><td width="236" valign="top" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(247, 202, 172); padding: 0px 7px;"><p style="text-align:center"><strong><span style=";font-family:'Microsoft YaHei',sans-serif;color:black">观察点</span></strong></p></td></tr><tr><td width="104" rowspan="2" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph;text-indent:13px"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">介绍公司</span></strong></p><p style="text-align:justify;text-justify:inter-ideograph;text-indent:13px"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">工作内容</span></strong></p><p style="text-align:justify;text-justify:inter-ideograph;text-indent:13px"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">上班时间等</span></strong></p></td><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">请问您来公司面试前，是有网站上了解过我们公司？</span></em></p><p><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">对我们公司有什么想要提问的吗？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">观察</span><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">应聘者对公司的意向程度，对问题的思考能力</span></p></td></tr><tr><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">请问您对刚的介绍是否有疑问呢？ &nbsp; </span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">观察应聘者对介绍的理解能力</span></p></td></tr><tr><td width="104" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p style="text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">介绍工资</span></strong></p><p style="text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">福利待遇</span></strong></p></td><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">请问您填写的</span></em><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">***</span></em><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">元是之前实际拿到手的吗？您现在的薪资要求是</span></em><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">***</span></em><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">元？有要求实际拿到手吗？</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">观察应聘者与薪资待遇是否匹配</span></p></td></tr><tr><td width="104" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p style="text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">应聘者提问</span></strong></p></td><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">请问您对我们公司还有其他需要了解的吗</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">了解面试者对问题的理解能力</span></p></td></tr><tr><td width="104" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p style="text-align:center"><strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">感谢</span></strong></p></td><td width="274" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><em><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif;color:#767171">感谢应聘者的时间与对公司的关注</span></em></p></td><td width="236" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:12px;font-family:'Microsoft YaHei',sans-serif">/</span></p></td></tr></tbody></table><p><strong><span style="font-size:13px;font-family: 'Microsoft YaHei',sans-serif">&nbsp;</span></strong></p><p class="MsoListParagraph" style="margin-left:48px"><strong><span style=";font-family:'Microsoft YaHei',sans-serif"><br></span></strong></p><p class="MsoListParagraph" style="margin-left:48px"><strong><span style=";font-family:'Microsoft YaHei',sans-serif"><br></span></strong></p><p class="MsoListParagraph" style="margin-left:48px"><span style="font-size: 16px;"><strong><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif;">六、<span style="font-style: normal; font-variant: normal; font-weight: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></strong><strong><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif;">面试官技巧</span></strong></span></p><p class="MsoListParagraph" style="margin-left:48px"><strong><span style=";font-family:'Microsoft YaHei',sans-serif"><br></span></strong></p><p style="line-height:150%"><strong><span style="font-size:14px;line-height:150%;font-family: 'Microsoft YaHei',sans-serif;color:#0C0C0C">1</span></strong><strong><span style="font-size:14px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#0C0C0C">、提问的技巧（可与行为性问题相对照，解决没有弄清楚的问题）。</span></strong></p><p style="line-height:150%"><em><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#767171">自然、亲切、渐进、聊天式地导入正题；使用统一的指导语很关键。好的开头是成功的一半，目的在于缓解应试者的心理紧张。</span></em></p><p class="MsoListParagraph" style="margin-left:41px;text-align:justify;text-justify:inter-ideograph;line-height: 150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:#0C0C0C">Ø<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#0C0C0C">通俗、简明、有节奏感。提问时，考官应力求使用标准语言，避免使用有歧义的语言，不要用生僻字，尽量少用专业性太强的词汇。</span></p><p class="MsoListParagraph" style="margin-left:41px;text-align:justify;text-justify:inter-ideograph;line-height: 150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:#0C0C0C">Ø<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#0C0C0C">问题要有可评价性（与测评要素相对应）和延伸性（不是简单用“是”或者“否”就能回答）。</span></p><p class="MsoListParagraph" style="margin-left:41px;text-align:justify;text-justify:inter-ideograph;line-height: 150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:#0C0C0C">Ø<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#0C0C0C">坚持“问谁”、“问实”的原则（</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#0C0C0C">STAR</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#0C0C0C">追问法）。不允许应试者在这一问题上模棱两可、含混回答。追问、了解、弄清楚应试者的真实情况和意图。</span></p><table cellspacing="0" cellpadding="0"><tbody><tr style=";height:39px" class="firstRow"><td width="482" colspan="3" valign="top" style="border: 1px solid windowtext; background: rgb(247, 202, 172); padding: 0px 7px;" height="39"><p style="text-align:center"><strong><span style="font-size:14px;font-family:'Microsoft YaHei',sans-serif">STAR</span></strong><strong><span style="font-size:14px;font-family:'Microsoft YaHei',sans-serif;color:black">提问法</span></strong></p></td></tr><tr><td width="129" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph;line-height:150%"><strong><span style=";line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:red">S</span></strong><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">ituation</span></p></td><td width="123" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">情景</span></p></td><td width="231" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">“遇到的情景或当时的背景”</span></p></td></tr><tr><td width="129" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph;line-height:150%"><strong><span style=";line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:red">T</span></strong><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">ask</span></p></td><td width="123" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">任务</span></p></td><td width="231" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">“在上述情景下与需要完成的任务”</span></p></td></tr><tr><td width="129" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph;line-height:150%"><strong><span style=";line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:red">A</span></strong><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">ction</span></p></td><td width="123" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">行动&nbsp; </span></p></td><td width="231" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">“怎么说的？”，“怎么做的？”</span></p></td></tr><tr><td width="129" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph;line-height:150%"><strong><span style=";line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:red">R</span></strong><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">esult</span></p></td><td width="123" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">结果</span></p></td><td width="231" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">“上述行为带来的结果”</span></p></td></tr></tbody></table><p class="MsoListParagraph" style="margin-left:32px;text-align:justify;text-justify:inter-ideograph;line-height: 150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:#0C0C0C">Ø<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#0C0C0C">必要时可采取迂回的方式向应试者提问。如对于某些政治倾向和意愿，可问“你的同学和朋友是如何看待这个问题的？你认为如何？”即采用投射法来了解应试者自己的真实情况。</span></p><p class="MsoListParagraph" style="margin-left:32px;text-align:justify;text-justify:inter-ideograph;line-height: 150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:#0C0C0C">Ø<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#0C0C0C">追问和提问相结合，达到让应试者多说，考官多听的目的。</span></p><p class="MsoListParagraph" style="margin-left:32px;text-align:justify;text-justify:inter-ideograph;line-height: 150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:#0C0C0C">Ø<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#0C0C0C">给应试者提供弥补缺憾的机会。应试者可能因为被动地位或心情紧张而不能充分发挥自己的水平，所以要有补偿，如问“你还有什么要补充的吗？”</span></p><p style="line-height:150%"><strong><span style="font-size:14px;line-height:150%;font-family: 'Microsoft YaHei',sans-serif;color:#0C0C0C"><br></span></strong></p><p style="line-height:150%"><strong><span style="font-size:14px;line-height:150%;font-family: 'Microsoft YaHei',sans-serif;color:#0C0C0C"><br></span></strong></p><p style="line-height:150%"><strong><span style="font-size:14px;line-height:150%;font-family: 'Microsoft YaHei',sans-serif;color:#0C0C0C">2</span></strong><strong><span style="font-size:14px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#0C0C0C">、倾听的技巧</span></strong></p><p style="line-height:150%"><em><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#767171">有技巧的倾听才能很好地发现问题、找出问题，在倾听中有以下要点需注意：</span></em></p><p class="MsoListParagraph" style="margin-left:32px;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:#0C0C0C">Ø<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#0C0C0C">倾听时要仔细、认真，表情自然，不能不自然地俯视、斜视，或者盯着对方不动；防止造成应试者过多的心理压力，使其不能正常发挥。</span></p><p class="MsoListParagraph" style="margin-left:32px;text-align:justify;text-justify:inter-ideograph;line-height: 150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:#0C0C0C">Ø<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#0C0C0C">慎用一些带有倾向性的形体语言，如点头或摇头，以免给应试者造成误导。</span></p><p class="MsoListParagraph" style="margin-left:32px;text-align:justify;text-justify:inter-ideograph;line-height: 150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:#0C0C0C">Ø<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#0C0C0C">注意从应试者的语调、音高、言辞等方面区分应试者内在的素质水平。如讲话常用“嗯”“啊”等间歇语的人往往自我感觉良好，要求他人对他地位的重视；声音粗犷、音量较大者多为外向性格；讲话速度快而且平直，多为性格急躁、缺乏耐心；爱用流行、时髦词汇者大多虚荣心较强。</span></p><p class="MsoListParagraph" style="margin-left:32px;text-align:justify;text-justify:inter-ideograph;line-height: 150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:#0C0C0C">Ø<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#0C0C0C">客观倾听，避免夸大、低估、添加、省略、抢先、滞后、分析和重复错误倾向等。</span></p><p style="line-height:150%"><strong><span style="font-size:14px;line-height:150%;font-family: 'Microsoft YaHei',sans-serif;color:#0C0C0C"><br></span></strong></p><p style="line-height:150%"><strong><span style="font-size:14px;line-height:150%;font-family: 'Microsoft YaHei',sans-serif;color:#0C0C0C"><br></span></strong></p><p style="line-height:150%"><strong><span style="font-size:14px;line-height:150%;font-family: 'Microsoft YaHei',sans-serif;color:#0C0C0C">3</span></strong><strong><span style="font-size:14px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#0C0C0C">、</span></strong><strong><span style="font-size:14px;line-height: 150%;font-family:'Microsoft YaHei',sans-serif;color:#0C0C0C">观察的技巧</span></strong></p><p class="MsoListParagraph" style="margin-left:32px;text-align:justify;text-justify:inter-ideograph;line-height: 150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:#0C0C0C">Ø<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#0C0C0C">坚持观察的综合性、目的性和客观性原则。</span></p><p class="MsoListParagraph" style="margin-left:32px;text-align:justify;text-justify:inter-ideograph;line-height: 150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:#0C0C0C">Ø<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#0C0C0C">注意面部表情，通过对应试者面部表情的观察和分析，可推测其深层心理状况在不同程度上判断其情绪、态度、自信心、反应力、思维的敏捷性、性格特征、诚实性、人际交往能力等；如当考官提出一些难以回答或窘迫的问题时，应试者可以目光暗淡、双眉紧皱，带有明显的焦急或压抑的神色。</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif">语气其实就是心理活动的反映，在关注应聘者语气方面，需要留意应聘者讲述的语速，如是否有轻重缓急之处、是否有结巴之处、是否给人一种自信和铿锵有力的感觉。</span></p><p class="MsoListParagraph" style="margin-left:32px;text-align:justify;text-justify:inter-ideograph;line-height: 150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:#0C0C0C">Ø<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:#0C0C0C">注意身体姿态语言（手势、坐姿、表情变化、多余动作如捏衣角或攥手指等），这能提供有用的信息，了解应试者的内在心态。</span></p><p class="MsoListParagraph" style="margin-left:48px"><strong><span style=";font-family:'Microsoft YaHei',sans-serif"><br></span></strong></p><p class="MsoListParagraph" style="margin-left:48px"><strong><span style=";font-family:'Microsoft YaHei',sans-serif"><br></span></strong></p><p class="MsoListParagraph" style="margin-left:48px"><span style="font-size: 16px;"><strong><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif;">七、<span style="font-style: normal; font-variant: normal; font-weight: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></strong><strong><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif;">注意事项</span></strong></span></p><p class="MsoListParagraph" style="margin-left:48px;line-height:150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:black">l<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">保持目光接触并仔细聆听</span></p><p class="MsoListParagraph" style="margin-left:48px;text-align:justify;text-justify:inter-ideograph;line-height: 150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:black">l<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">多听少讲： 把</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">70%</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">的时间留给应聘者发言</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">, </span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">因如果面试者讲得越多</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">, </span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">得到的信息就越少</span></p><p class="MsoListParagraph" style="margin-left:48px;text-align:justify;text-justify:inter-ideograph;line-height: 150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:black">l<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">面试官依照面试记录表及时做记录</span></p><p class="MsoListParagraph" style="margin-left:48px;text-align:justify;text-justify:inter-ideograph;line-height: 150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:black">l<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">避免对应聘者的回答发表个人意见</span></p><p class="MsoListParagraph" style="margin-left:48px;text-align:justify;text-justify:inter-ideograph;line-height: 150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:black">l<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">当应聘者滔滔不绝时，适当打断，控制回答的方向</span></p><p class="MsoListParagraph" style="margin-left:48px;text-align:justify;text-justify:inter-ideograph;line-height: 150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:black">l<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">时间控制（大概在</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">15-30</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">分钟内）</span></p><p class="MsoListParagraph" style="margin-left:48px;text-align:justify;text-justify:inter-ideograph;line-height: 150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:black">l<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">面试结束时</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">, </span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">面试者应起身相送</span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">, </span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">再次感谢应试者应试</span></p><p class="MsoListParagraph" style="margin-left:48px;text-align:justify;text-justify:inter-ideograph;line-height: 150%"><span style="font-size:13px;line-height:150%;font-family:Wingdings;color:black">l<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;line-height:150%;font-family:'Microsoft YaHei',sans-serif;color:black">面试后做好面试评估并与主管达成一致</span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li></ul> <h3 class="more-answer">我要補充</h3> <div style=""><div id="editor_414395934900635.44" class="edui-default" style=""><div id="edui1" class="edui-editor  edui-default" style="width: 100%; z-index: 999;"><div id="edui1_toolbarbox" class="edui-editor-toolbarbox edui-default"><div id="edui1_toolbarboxouter" class="edui-editor-toolbarboxouter edui-default"><div class="edui-editor-toolbarboxinner edui-default"><div id="edui2" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui2&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui3" class="edui-box edui-button edui-for-fullscreen edui-default"><div id="edui3_state" onmousedown="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui3_body" unselectable="on" title="全屏" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui3&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui3&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui4" class="edui-box edui-button edui-for-source edui-default"><div id="edui4_state" onmousedown="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui4_body" unselectable="on" title="源代码" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui4&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui4&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui5" class="edui-box edui-button edui-for-undo edui-default"><div id="edui5_state" onmousedown="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui5_body" unselectable="on" title="撤销" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui5&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui5&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui6" class="edui-box edui-button edui-for-redo edui-default"><div id="edui6_state" onmousedown="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui6_body" unselectable="on" title="重做" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui6&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui6&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui7" class="edui-box edui-button edui-for-simpleupload edui-default"><div id="edui7_state" onmousedown="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui7_body" unselectable="on" title="单图上传" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui7&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui7&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui8" class="edui-box edui-button edui-for-imageleft edui-default"><div id="edui8_state" onmousedown="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui8_body" unselectable="on" title="左浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui8&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui8&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui9" class="edui-box edui-button edui-for-imageright edui-default"><div id="edui9_state" onmousedown="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui9_body" unselectable="on" title="右浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui9&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui9&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui10" class="edui-box edui-button edui-for-imagecenter edui-default"><div id="edui10_state" onmousedown="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui10_body" unselectable="on" title="居中" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui10&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui10&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui11" class="edui-box edui-separator  edui-default"></div><div id="edui12" class="edui-box edui-button edui-for-date edui-default"><div id="edui12_state" onmousedown="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui12_body" unselectable="on" title="日期" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui12&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui12&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui13" class="edui-box edui-button edui-for-time edui-default"><div id="edui13_state" onmousedown="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui13_body" unselectable="on" title="时间" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui13&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui13&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui14" class="edui-box edui-button edui-for-horizontal edui-default"><div id="edui14_state" onmousedown="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui14_body" unselectable="on" title="分隔线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui14&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui14&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui15" class="edui-box edui-separator  edui-default"></div><div id="edui16" class="edui-box edui-splitbutton edui-for-inserttable edui-default"><div title="插入表格" id="edui16_state" onmousedown="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui16_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onArrowClick();"></div></div></div></div><div id="edui19" class="edui-box edui-button edui-for-deletetable edui-default"><div id="edui19_state" onmousedown="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui19_body" unselectable="on" title="删除表格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui19&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui19&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui20" class="edui-box edui-button edui-for-insertrow edui-default"><div id="edui20_state" onmousedown="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui20_body" unselectable="on" title="前插入行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui20&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui20&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui21" class="edui-box edui-button edui-for-insertcol edui-default"><div id="edui21_state" onmousedown="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui21_body" unselectable="on" title="前插入列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui21&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui21&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui22" class="edui-box edui-button edui-for-mergeright edui-default"><div id="edui22_state" onmousedown="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui22_body" unselectable="on" title="右合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui22&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui22&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui23" class="edui-box edui-button edui-for-mergedown edui-default"><div id="edui23_state" onmousedown="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui23_body" unselectable="on" title="下合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui23&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui23&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui24" class="edui-box edui-button edui-for-deleterow edui-default"><div id="edui24_state" onmousedown="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui24_body" unselectable="on" title="删除行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui24&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui24&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui25" class="edui-box edui-button edui-for-deletecol edui-default"><div id="edui25_state" onmousedown="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui25_body" unselectable="on" title="删除列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui25&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui25&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui26" class="edui-box edui-button edui-for-splittorows edui-default"><div id="edui26_state" onmousedown="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui26_body" unselectable="on" title="拆分成行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui26&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui26&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui27" class="edui-box edui-button edui-for-splittocols edui-default"><div id="edui27_state" onmousedown="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui27_body" unselectable="on" title="拆分成列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui27&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui27&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui28" class="edui-box edui-button edui-for-splittocells edui-default"><div id="edui28_state" onmousedown="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui28_body" unselectable="on" title="完全拆分单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui28&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui28&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui29" class="edui-box edui-button edui-for-mergecells edui-default"><div id="edui29_state" onmousedown="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui29_body" unselectable="on" title="合并多个单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui29&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui29&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui36" class="edui-box edui-button edui-for-edittable edui-default"><div id="edui36_state" onmousedown="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui36_body" unselectable="on" title="表格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui36&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui36&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui41" class="edui-box edui-button edui-for-edittd edui-default"><div id="edui41_state" onmousedown="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui41_body" unselectable="on" title="单元格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui41&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui41&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui42" class="edui-box edui-separator  edui-default"></div><div id="edui43" class="edui-box edui-menubutton edui-for-lineheight edui-default"><div title="行间距" id="edui43_state" onmousedown="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui43_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onArrowClick();"></div></div></div></div><div id="edui52" class="edui-box edui-menubutton edui-for-rowspacingtop edui-default"><div title="段前距" id="edui52_state" onmousedown="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui52_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onArrowClick();"></div></div></div></div><div id="edui59" class="edui-box edui-menubutton edui-for-rowspacingbottom edui-default"><div title="段后距" id="edui59_state" onmousedown="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui59_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onArrowClick();"></div></div></div></div></div><div id="edui66" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui66&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui67" class="edui-box edui-combox edui-for-fontfamily edui-default"><div title="字体" id="edui67_state" onmousedown="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui67_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onButtonClick(event, this);">字体</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onArrowClick();"></div></div></div></div><div id="edui80" class="edui-box edui-combox edui-for-customstyle edui-default"><div title="自定义标题" id="edui80_state" onmousedown="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui80_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onButtonClick(event, this);">自定义标题</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onArrowClick();"></div></div></div></div><div id="edui86" class="edui-box edui-combox edui-for-paragraph edui-default"><div title="段落格式" id="edui86_state" onmousedown="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui86_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onButtonClick(event, this);">段落格式</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onArrowClick();"></div></div></div></div><div id="edui95" class="edui-box edui-combox edui-for-fontsize edui-default"><div title="字号" id="edui95_state" onmousedown="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui95_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onButtonClick(event, this);">字号</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onArrowClick();"></div></div></div></div><div id="edui110" class="edui-box edui-button edui-for-link edui-default"><div id="edui110_state" onmousedown="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui110_body" unselectable="on" title="超链接" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui110&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui110&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui111" class="edui-box edui-button edui-for-indent edui-default"><div id="edui111_state" onmousedown="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui111_body" unselectable="on" title="首行缩进" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui111&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui111&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui112" class="edui-box edui-button edui-for-bold edui-default"><div id="edui112_state" onmousedown="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui112_body" unselectable="on" title="加粗" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui112&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui112&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui113" class="edui-box edui-splitbutton edui-for-forecolor edui-default edui-colorbutton"><div title="字体颜色" id="edui113_state" onmousedown="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui113_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui113_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onArrowClick();"></div></div></div></div><div id="edui116" class="edui-box edui-splitbutton edui-for-backcolor edui-default edui-colorbutton"><div title="背景色" id="edui116_state" onmousedown="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui116_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui116_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onArrowClick();"></div></div></div></div><div id="edui119" class="edui-box edui-button edui-for-italic edui-default"><div id="edui119_state" onmousedown="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui119_body" unselectable="on" title="斜体" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui119&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui119&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui120" class="edui-box edui-button edui-for-underline edui-default"><div id="edui120_state" onmousedown="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui120_body" unselectable="on" title="下划线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui120&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui120&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui121" class="edui-box edui-button edui-for-strikethrough edui-default"><div id="edui121_state" onmousedown="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui121_body" unselectable="on" title="删除线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui121&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui121&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui122" class="edui-box edui-button edui-for-superscript edui-default"><div id="edui122_state" onmousedown="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui122_body" unselectable="on" title="上标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui122&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui122&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui123" class="edui-box edui-button edui-for-subscript edui-default"><div id="edui123_state" onmousedown="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui123_body" unselectable="on" title="下标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui123&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui123&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui124" class="edui-box edui-button edui-for-removeformat edui-default"><div id="edui124_state" onmousedown="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui124_body" unselectable="on" title="清除格式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui124&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui124&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui125" class="edui-box edui-button edui-for-blockquote edui-default"><div id="edui125_state" onmousedown="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui125_body" unselectable="on" title="引用" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui125&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui125&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui126" class="edui-box edui-button edui-for-pasteplain edui-default"><div id="edui126_state" onmousedown="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui126_body" unselectable="on" title="纯文本粘贴模式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui126&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui126&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui127" class="edui-box edui-menubutton edui-for-insertorderedlist edui-default"><div title="有序列表" id="edui127_state" onmousedown="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui127_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onArrowClick();"></div></div></div></div><div id="edui140" class="edui-box edui-menubutton edui-for-insertunorderedlist edui-default"><div title="无序列表" id="edui140_state" onmousedown="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui140_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onArrowClick();"></div></div></div></div><div id="edui147" class="edui-box edui-button edui-for-cleardoc edui-default"><div id="edui147_state" onmousedown="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui147_body" unselectable="on" title="清空文档" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui147&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui147&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div></div></div></div><div id="edui1_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;"><div id="edui1_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui1&quot;].showWordImageDialog();">点击上传</div><div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui1&quot;].hideToolbarMsg();">x</div><div id="edui1_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div><div style="height:0;overflow:hidden;clear:both;" class="edui-default"></div></div><div id="edui1_message_holder" class="edui-editor-messageholder edui-default"></div></div><div id="edui1_iframeholder" class="edui-editor-iframeholder edui-default" style="width: 100%; height: 300px; z-index: 999; overflow: hidden;"><iframe id="ueditor_0" width="100%" height="100%" frameborder="0" src="javascript:void(function(){document.open();document.write(&quot;<!DOCTYPE html><html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}
body{margin:8px;font-family:sans-serif;font-size:13px;}p{margin:5px 0;}</style><link rel='stylesheet' type='text/css' href='/static/ueditor/themes/iframe.css'/></head><body class='view' ></body><script type='text/javascript'  id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant0'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);</script></html>&quot;);document.close();}())"></iframe></div><div id="edui1_bottombar" class="edui-editor-bottomContainer edui-default"><table class="edui-default"><tbody class="edui-default"><tr class="edui-default"><td id="edui1_elementpath" class="edui-editor-bottombar edui-default"><div class="edui-editor-breadcrumb">元素路径:</div></td><td id="edui1_wordcount" class="edui-editor-wordcount edui-default">字数统计</td><td id="edui1_scale" class="edui-editor-scale edui-default" style="display: none;"><div class="edui-editor-icon edui-default"></div></td></tr></tbody></table></div><div id="edui1_scalelayer" class="edui-default"></div></div></div> <div class="edit-btn"><button type="button" class="button">提交回答</button> <label data-v-0fe26aab="" for="commonCheckbox" class="checkbox"><input data-v-0fe26aab="" type="checkbox" id="commonCheckbox" name="commonCheckbox"> <i data-v-0fe26aab=""></i> 需要彈窗，請勾選</label></div></div></div> <div class="index-right"><div class="right-bk"><h3 class="com-title"><a href="javascript:;">我的最爱</a></h3> <div class="item-box clearfix"><span>錯誤，請您登入後再操作！</span></div></div> <div class="hotTag right-bk"><h3 class="com-title">热门标签</h3> <div class="hotTag-box item-box clearfix"><p>無數據</p></div></div> <div class="right-bk rank"><h3 class="com-title"><a href="javascript:;">冠軍排行榜</a></h3> <ul><li><div><span class="number">1</span> <img src="/static/timg.jpeg"> <span>王菊芳</span></div> <span class="score">612分</span></li><li><div><span class="number">2</span> <img src="/upload/head/10177.gif?time=1556271256"> <span>黃雪靜</span></div> <span class="score">258分</span></li><li><div><span class="number">3</span> <img src="/static/timg.jpeg"> <span>田霞</span></div> <span class="score">240分</span></li><li><div><span class="number">4</span> <img src="/static/timg.jpeg"> <span>曹超雲</span></div> <span class="score">168分</span></li><li><div><span class="number">5</span> <img src="/static/timg.jpeg"> <span>文宇</span></div> <span class="score">96分</span></li><li><div><span class="number">6</span> <img src="/upload/head/10217.png?time=1548059679"> <span>黃興興</span></div> <span class="score">78分</span></li><li><div><span class="number">7</span> <img src="/static/timg.jpeg"> <span>黃佳麗</span></div> <span class="score">62分</span></li><li><div><span class="number">8</span> <img src="/static/timg.jpeg"> <span>马泽鹏</span></div> <span class="score">46分</span></li><li><div><span class="number">9</span> <img src="/static/timg.jpeg"> <span>陳敏南</span></div> <span class="score">40分</span></li><li><div><span class="number">10</span> <img src="/static/timg.jpeg"> <span>Cs-刘婷</span></div> <span class="score">10分</span></li><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></ul></div> <div class="activeUser right-bk"><h3 class="com-title">活跃用户</h3> <div class="activeUser-box"><a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a></div></div> <img src="data:image/png;base64,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" class="to-top"></div></div> <!----> <!----> <!----> <div class="page-cnt footer"><p>T5客服知识库<span>|</span>zsk.591.com.tw</p></div></div></div><script type="text/javascript" src="/js/manifest.f192b.js"></script><script type="text/javascript" src="/js/vendor.17570.js"></script><script type="text/javascript" src="/js/app.58254.js"></script><div id="edui_fixedlayer" class="edui-default" style="position: fixed; left: 0px; top: 0px; width: 0px; height: 0px;"><div id="edui148" class="edui-popup  edui-bubble edui-default" onmousedown="return false;" style="display: none;"> <div id="edui148_body" class="edui-popup-body edui-default"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank" class="edui-default"></iframe> <div class="edui-shadow edui-default"></div> <div id="edui148_content" class="edui-popup-content edui-default">  </div> </div></div></div></body></html>