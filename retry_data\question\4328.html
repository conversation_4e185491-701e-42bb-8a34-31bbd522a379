<html class="nprogress-busy"><head><meta charset="utf-8"><title>591客服知識庫</title><link rel="stylesheet" href="/static/font/font-awesome.min.css"><link href="/css/app.e5aa4.css" rel="stylesheet"><script type="text/javascript" charset="utf-8" async="" src="/js/questionDetail.d0a6b.js"></script><style type="text/css">.que-detail-page .index-left .button{width:74px;height:32px;line-height:32px;font-size:13px;margin-right:10px}.que-detail-page .index-left .button:hover{color:#fff}.que-detail-page .detail-box{padding:20px 30px;border:1px solid #e5e5e5;border-radius:4px}.que-detail-page .detail-box h3{font-size:18px;color:#333;margin-bottom:10px}.que-detail-page .detail-box h3 span{color:#f77d24}.que-detail-page .detail-box .add-fav{cursor:pointer}.que-detail-page .d-b-menu{margin:15px 0 25px;font-size:13px;color:#999}.que-detail-page .d-b-menu b{display:inline-block;font-weight:400;margin:0 10px}.que-detail-page .d-b-menu .r{float:right;color:#f77d24}.que-detail-page .d-b-menu .r .fa{margin-right:5px;font-size:16px}.que-detail-page .d-b-cnt{margin-bottom:25px;font-size:13px;color:#666}.que-detail-page .submit-ask{display:block;margin:32px auto 0}.que-detail-page .reply-list{margin:30px 0 10px;overflow:hidden}.que-detail-page .reply-list .reply-item{padding:15px 30px 25px;margin-bottom:30px;border-radius:4px;border:1px solid #e5e5e5}.que-detail-page .reply-list .reply-item h4{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;line-height:50px;font-size:13px}.que-detail-page .reply-list .reply-item h4 img{width:40px;height:40px;border-radius:50%}.que-detail-page .reply-list .reply-item h4 .time{color:#999}.que-detail-page .reply-list .reply-item h4 .button{-ms-flex-item-align:center;align-self:center}.que-detail-page .reply-list .reply-item h4 .delete{color:#666;cursor:pointer}.que-detail-page .reply-list .reply-item .reply-detail{padding:20px 0 30px}.que-detail-page .more-answer{font-size:18px;color:#666;margin-bottom:5px}.que-detail-page .editor-content table{width:100%;border:1px solid #ddd}.que-detail-page .editor-content table td,.que-detail-page .editor-content table th{border:1px solid #ddd;padding:2px 8px;line-height:2}.que-detail-page .editor-content img{max-width:100%;vertical-align:text-bottom}.que-detail-page .edit-btn{text-align:center;margin-top:20px}.que-detail-page .edit-btn .edit-cancle{background:#aaa}</style><style type="text/css">.head-info{line-height:26px;background:#f5f5f5;font-size:13px;color:#999}.head-info a{margin-left:20px;color:#333}.head-search{margin-top:22px}.head-search img{float:left}.head-search .srh-box{float:left;margin:20px 0 0 20px}.head-search .srh-box .srh-input{float:left;width:260px;height:38px;line-height:38px;border:2px solid #37a508;border-radius:4px 0 0 4px}.head-search .srh-box a,.head-search .srh-box button{float:left;width:80px;height:38px;line-height:36px;font-size:16px;color:#fff}.head-search .srh-box .srh-btn{border-radius:0 4px 4px 0}.head-search .srh-box .ask-btn{margin-left:10px}.head-nav{float:left;margin:20px 0 0 60px;color:#fff;font-size:16px;line-height:36px}.head-nav a{display:block;height:100%;color:#333}.head-nav li{position:relative;float:left;margin-right:35px}.head-nav .active a,.head-nav li:hover a{color:#30b940}.head-nav .active:after,.head-nav li:hover:after{display:block;content:"";height:3px;width:100%;background:#30b940}.head-pic{width:100%;height:90px;margin-top:20px;background:url(/static/banner.png) top no-repeat}</style><style type="text/css">.top-breadcrumb{height:20px;margin-top:10px;margin-bottom:40px;font-size:13px;color:#666}.top-breadcrumb .t-b-icon{color:#30b940}.top-breadcrumb span{margin-left:8px}.top-breadcrumb span .fa{font-size:16px;margin-left:8px}.top-breadcrumb span:first-child{margin-left:0}.top-breadcrumb .now{color:#30b940}</style><script type="text/javascript" src="/static/ueditor/ueditor.config.js?20210319" id="configScriptTag"></script><script type="text/javascript" src="/static/ueditor/ueditor.all.js?20210319" id="editorScriptTag"></script><style type="text/css">.checkbox[data-v-0fe26aab]{word-spacing:4px;vertical-align:bottom;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.checkbox i[data-v-0fe26aab]{display:inline-block;width:14px;height:14px;border-radius:2px;border:1px solid #ccc;background:#fff;position:relative;top:1px}.checkbox input[data-v-0fe26aab]{opacity:0;width:0;height:0;margin:0}.checkbox input:checked+i[data-v-0fe26aab]{background:#30b940;border-color:#30b940}.checkbox input:checked+i[data-v-0fe26aab]:before{content:"";position:absolute;left:2px;top:1px;width:11px;height:6px;border-bottom:2px solid #fff;border-left:2px solid #fff;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}</style><style type="text/css">.popbox{position:fixed;top:50%;left:50%;z-index:1011;background:#fff;border-radius:6px;overflow:hidden}.popbox h3{font-size:16px;line-height:40px;padding:0 20px}.popbox h3 span{float:right;color:#666;font-size:13px;cursor:pointer}.popbox .pb-cnt{padding:0 20px;font-size:13px}.popbox .pb-btn-area{position:absolute;bottom:0;left:0;z-index:1;width:100%;text-align:right;padding:0 20px 20px 0}.popbox .pb-btn-area button{height:32px;line-height:32px;border:0;background:#fff;cursor:pointer;border-radius:6px;margin-left:15px;padding:0 15px}.popbox .pb-btn-area button.pb-btn-ok{background:#daf3cf;color:#279005}.popbox .pb-btn-area button.pb-btn-ok:hover{background:#279005;color:#fff}</style><style type="text/css">.shadow{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1010;background:#000}</style><style type="text/css">.footer{margin-top:70px;margin-bottom:20px;padding-top:20px;text-align:center;color:#999;border-top:1px solid #f5f5f5}.footer span{margin:0 10px}</style><script src="/static/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript" defer="defer"></script><link href="/static/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet"><script src="/static/ueditor/third-party/codemirror/codemirror.js" type="text/javascript" defer="defer"></script><link rel="stylesheet" type="text/css" href="/static/ueditor/third-party/codemirror/codemirror.css"><script src="/static/ueditor/third-party/zeroclipboard/ZeroClipboard.js" type="text/javascript" defer="defer"></script><style type="text/css">.activeUser,.favorite,.hotTag,.rank{margin-bottom:40px}</style><style type="text/css">.rank .rank-page{height:60px;line-height:60px;font-size:18px;margin-bottom:20px;text-align:center;background:#fafafa;border-radius:5px}.rank ul{padding:0 20px}.rank li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;border-bottom:1px dotted #f5f5f5;line-height:50px}.rank li .flag{position:relative;display:inline-block;width:20px;height:30px;background:#476472;vertical-align:middle;margin-right:12px;color:#fff;text-align:center;line-height:20px;font-size:13px;font-style:normal}.rank li .flag:after{content:"";position:absolute;bottom:-4px;left:-4px;border:14px solid transparent;border-bottom-color:#fff}.rank li:first-child .flag{background:#fb6757}.rank li:nth-child(2) .flag{background:#fac525}.rank li:nth-child(3) .flag{background:#00a9fb}.rank li img{width:36px;height:36px;border-radius:50%;margin-right:12px}</style><style type="text/css">.activeUser .activeUser-box a{display:inline-block;width:64px;height:64px;border-radius:50%;overflow:hidden;margin:0 6px 12px}</style><style type="text/css">.to-top{display:none;position:fixed;bottom:150px;z-index:5;width:50px;height:50px;cursor:pointer}</style></head><body style=""><div id="app" style=""><div class="que-detail-page" style=""><div class="wiki-hd"><div class="head-info"><p class="page-cnt">
      您好，欢迎来到591客服知识库
      <a href="https://www.591.com.tw/admin.php?new_url=https://zsk.591.com.tw">[请登入]</a> <!----></p></div> <div class="page-cnt head-search clearfix"><a href="/" class="router-link-active"><img src="/static/logo.png"></a> <ul class="head-nav clearfix"><li class="router-link-active active"><a href="/">591房屋交易网</a></li> <li class=""><a href="/life">客服充电站</a></li> <li class=""><a href="/notification">通知事项</a></li></ul> <div class="srh-box"><input type="text" placeholder="请输入搜索内容" class="srh-input"> <button type="button" class="button srh-btn">搜索</button> <a href="/ask/591" class="button button-orange ask-btn">提問</a></div></div> <div class="head-pic"></div></div> <div class="top-breadcrumb com-width"><span><a href="/collection/591/1209" class="">客服督導相關<i class="fa fa-angle-double-right"></i></a></span><span><a href="/collection/591/1209/1655" class="">崗位作業指導<i class="fa fa-angle-double-right"></i></a></span><span><strong class="now">问题详情</strong></span></div> <div class="com-width clearfix" style=""><div class="index-left" style=""><div class="detail-box"><h3><!---->新人培訓流程及規範</h3> <p class="clearfix d-b-menu"><span class="l">
            曹超雲&nbsp;&nbsp;&nbsp;浏览49次&nbsp;&nbsp;&nbsp;2020-11-04 09:50:38
          </span> <span class="r"><span class="add-fav"><i class="fa fa-heart"></i>加入最爱</span></span></p> <div class="d-b-cnt editor-content big-img-parent"><div><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">新人培訓流程及規範</span></p></div></div> <div class="d-b-button"><a href="/edit/591/4328" class="button">编辑</a> <button class="button button-orange">关闭提问</button></div></div> <div class="hr-line"></div> <ul class="reply-list big-img-parent"><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">曹超雲</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-04 09:53:10</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1、培訓流程及規範</span></strong></p><p><br></p><p><img src="/upload/question/20201112/1605147444649338.png" alt="image.png"></p><p>&nbsp;<img src="/upload/question/20201112/1605147479745667.png" alt="image.png" width="625" height="519" style="width: 625px; height: 519px;"></p><p><br></p><p><br></p><p><br></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">曹超雲</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-18 10:08:58</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="line-height: 1.75em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>2、流程管理規範</strong></span></p><p><br></p><p><br></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>1</strong></span></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">確認電腦、座位、導師</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">確認新人的電腦、座位及導師人選</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">明確新人的電腦、座位、導師，確保新人入職辦公用品正常使用</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">督導</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">收到人資反饋新人會按約定時間來報到時</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">與團隊討論達成一致，確認座位、電腦、導師</span></p></td></tr><tr style=";height:245px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="245"><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1確認新人的電腦及座位</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1.1目的：確保新人入職後可正常使用</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1.2督導與主任（陳敏南）確認電腦/座位，並確認電腦是否刷機，若未刷機則需通過技術部（阿升）處理刷機。</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1.3頻率/時間：收到人資反饋新人會按約定時間來報到時</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1.4 初步確認後，與主管達成一致</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1.5相關崗位：客服主任、客服主管</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2確認新人導師</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.1目的：確保新人入職後由導師負責整個試用期能快速熟悉公司環境、制度</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.2與人資（秀敏）確認：優先確認客服團隊導師、若均沒有空缺，則考慮同部門其他職能導師人選；確認人選後與客服主管、當班主任、達成一致，在與導師溝通，確認導師並確認新人入職時間及上班時間是否會衝突，若衝突（如導師晚班或休息）則需與導師、主任協調好換班。</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.3收到人資反饋新人會按約定時間來報到時</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.4相關崗位：客服主任、客服主管、導師、人資（秀敏）</span></p></td></tr></tbody></table><p><br></p><p><br></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>2</strong></span></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">達成一致</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">確認新人的電腦、座位及導師人選後需與團隊達成一致</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">確保新人的電腦、座位、導師，確保新人入職辦公用品正常使用</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">骨幹團隊</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">收到人資反饋新人會按約定時間來報到時</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">與團隊討論達成一致，確認座位、電腦、導師</span></p></td></tr><tr style=";height:122px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="122"><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1確認新人的電腦、座位及導師人選後需與團隊達成一致</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1.1目的：確保新人入職後可正常使用</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1.2督導與人資、主任初步確認好：導師、座位、電腦後需與主管/骨幹團隊達成一致</span></p></td></tr></tbody></table><p><br></p><p><br></p><p><br></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>3</strong></span></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">反饋人資</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">確認新人的電腦、座位及導師人選後需反饋人資（秀敏）</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">確保人資辦理新人入職後由導師帶領到座位並介紹公司環境制度</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">督導</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">與團隊確認好電腦、座位及導師後及時反饋人資（秀敏）</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">與人資（秀敏）反饋確認的導師人選</span></p></td></tr><tr style=";height:103px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="103"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細內容</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1確認新人的電腦、座位及導師人選後需反饋人資</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1.1目的：確保新人辦理入職後由導師輔導介紹公司環境制度</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1.2與團隊達成一致後及時反饋人資登記</span></p></td></tr></tbody></table><p><br></p><p><br></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>4</strong></span></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">入職</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新人入職</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">確保新人按時到崗培訓</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新人</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">按人資通知的入職時間報到</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新人準時到公司，由人資辦理入職手續</span></p></td></tr><tr style=";height:28px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">無</span></p></td></tr></tbody></table><p><br></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>5</strong></span></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">介紹公司環境領導</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">介紹新人熟悉公司環境及相關領導</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">確保新人能快速熟悉公司環境、相關制定、直屬領導</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">導師</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新人辦理入職手續後，由人資交接人員到導師時</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">導師依照導師作業指導書內容完成輔導事務</span></p></td></tr><tr style=";height:28px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">無</span></p></td></tr></tbody></table><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">曹超雲</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-18 12:02:17</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><br></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>6</strong></span></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">發放培訓教材及介紹培訓規劃</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">發放培訓教材及介紹培訓規劃</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">確保新人對試用期的學習內容及培訓規劃有全面的瞭解</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">督導</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">導師在介紹完第一天需要講解的內容後</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">列印培訓教材、發送培訓規劃並講解</span></p></td></tr><tr style=";height:28px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1培訓教材</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1.1目的：可幫助新人對學習內容有全面的瞭解，確保新人依照教材進行全面系統的學習網站業務知識</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1.2教材的編寫與更新</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1.2.1新人教材編寫：</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第一步：先梳理所有試用期需要學習的內容，依照學習內容列框架（大綱）、依照學習內容類別進行分類，收集網站業務知識點並按大綱及分類的邏輯順序編寫，並與團隊達成一致</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第二步：確認教材展示的方式（載體），字體：繁體，字號，格式與團隊達成一致</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第三步：依照規劃編寫教材，完成後與團隊討論並定稿</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1.2.2 新人教材更新</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第一步：收集所有需新增、刪除、更新的知識點，並記錄</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第二步：按照收集的知識點分別更新至教材，確認字體：繁體，字號，格式與團隊達成一致</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第三步：完成更新並定稿</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1.3時間：</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1.3.1 更新時間：新人入職前、上次培訓後；若沒有新人，每季度的第一個月15日更新一次</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1.3.2 列印時間：新人入職前一天</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2 培訓規劃（僅針對學習能力適中的新人）</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.1 目的：幫助新人對培訓規劃有全面的瞭解，確保新人培訓順利、高效進行，保證培訓質量</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.2培訓規劃的編寫與更新</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.2.1 培訓規劃的編寫</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.2.1.1規劃每天學習的業務知識</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第一步：梳理所有業務知識點，並根據知識點進行劃分每天學習的內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第二步：需按照每日的學習時間，評估每日的內容是否安排合理</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第三步：根據業務知識重要程度劃分新人需要掌握的程度（類別有：瞭解、記住、熟練）</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.2.1.2 培訓及評估方式</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">教學前：發放學習內容的輔助試題，讓新人根據預習業務完成</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">教學時：通過講解及提問的方式，保證學員掌握所學內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">教學後：通過筆試的方式，驗收是否掌握</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.2.2 培訓規劃的更新</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.2.2.1培訓時間及學習內容規劃是否合理：可依照教學經驗或新人能力評估，若學習時間安排過快或推遲，則需調整培訓的內容劃分及培訓時間的安排</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.2.2.2培訓方式是否合理：需關注培訓中及培訓後新人的掌握程度，及時調整培訓方式（僅考慮學習能力適中人員）</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.3 更新時間：新人培訓時、新人培訓後；</span></p></td></tr></tbody></table><p><br></p><p><br></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>7</strong></span></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">按照規劃教材教學</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">依照第一階段的學習內容教學&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">確保新人按照學習規劃學習</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">督導</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新人第一階段學習期間</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">根據學習規劃對新人進行教學</span></p></td></tr><tr style=";height:28px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、新人規劃表，請參考：</span><a href="file:///C:/Users/<USER>/Desktop/%E7%9D%A3%E5%B0%8E%E5%AD%B8%E7%BF%92%E4%BA%8B%E5%8B%99/%E6%96%B0%E4%BA%BA%E5%9F%B9%E8%A8%93%E5%AD%B8%E7%BF%92/T5-%E6%96%B0%E5%AE%A2%E6%9C%8D%E5%AD%A6%E4%B9%A0%E8%A7%84%E5%88%92--2020.9%E6%A2%B3%E7%90%86(2).xlsx" style="text-decoration: underline; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新人培訓規劃</span></a></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、注意事項：教學過程中可根據新人掌握程度調整規劃</span></p></td></tr></tbody></table><p><br></p><p><br></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>8</strong></span></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">按照規劃學習</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">依照第一階段的學習內容定量學習&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">學習站台業務知識確保達成考核標準</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新人</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新人第一階段學習期間</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">根據學習規劃進行學習</span></p></td></tr><tr style=";height:28px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;&nbsp; &nbsp; </span>目的：新人按照規劃學習進行學習，確保按時按量掌握所有學習內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;&nbsp; &nbsp; </span>學習方式：</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">■&nbsp;</span>學習前：督導發放崗前學習輔助試題，新人根據預習內容完成</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">需關注：自學能力、動手操作能力、理解能力、記憶力</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">■&nbsp;</span></span>學習中：根據預習內容向督導進行提問，並由督導解答</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">需關注：理解能力、反應能力、思考力</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">■ 學習後：根據每日所學內容進行複習，自行查缺補漏疑難問題並提問，由督導解答</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">需關注：接受能力、理解能力、操作能力、記憶力</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.1時間：</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">崗前試題完成時間：1個小時</span></p></td></tr></tbody></table><p><br></p><p><br></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>9</strong></span></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">考核</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">依照學習內容完成考試&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">驗收學習內容的掌握程度</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新人</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">每日學習業務知識後</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">根據每日學習內容完成筆試</span></p></td></tr><tr style=";height:28px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; &nbsp; </span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">目的：驗收每日學習內容的掌握程度</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; &nbsp; </span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">考核方式：根據每日所學內容進行筆試</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; &nbsp; </span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">頻率：每日1次，每次時間1~1.5小時</span></p></td></tr></tbody></table><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">曹超雲</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-18 13:46:34</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>10</strong></span></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">評估</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">依照新人第一階段學習進行驗收評估&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">驗收新人學習內容的掌握程度，確認是否達到標準</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">督導</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">根據每日考核的結果進行評估是否達標</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">根據評估標準進行評估</span></p></td></tr><tr style=";height:28px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; &nbsp; </span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">目的：驗收新人對學習內容的掌握程度</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; &nbsp; </span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">評估方式及標準：筆試達到90分或以上</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">評估結果</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3.1達標：按正常培訓規劃進行教學。</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3.2 &nbsp; 不達標</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3.2.1&nbsp; 可改善的情況</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第一步，重新梳理未掌握的學習內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第二步，針對未掌握的業務知識進行再次講解提升</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第三步，根據情況給予新人時間複習，在規定時間內再次考核驗收（驗收方式：補考）</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第四步，驗收合格後進入正常的培訓規劃進行教學</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3.2.2&nbsp; 不可改善的情況</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第一步，按照可改善的情況的步驟進行2次提升</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第二步，若提升後考核仍無法達到標準，且無法在規定時間內完成學習進度或出現行為態度不符合團隊規範時，需及時與團隊反饋新人情況</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第三步，與團隊討論達成一致，並與新人溝通達成一致，進行最後的提升學習並驗收，若仍無法改善則會提前結束試用期</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第四步，第3次提升，仍無法達到標準則辦理離職手續，並與人資思柳同步，告知新人學習情況。</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.4</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; &nbsp; </span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">時間：依照培訓規劃學習完業務知識後</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.5</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; &nbsp; </span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">頻率：按天/次驗收，按周/次驗收</span></p></td></tr></tbody></table><p><br></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>11</strong></span></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">結束</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">當新人提升後仍無法達到考核標準時需溝通提前結束試用期&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">驗收新人學習內容的掌握程度，無法達到標準</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">團隊/督導</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">經過3次提升仍無法達到目標時</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">根據評估標準進行評估、與團隊反饋結果並達成一致</span></p></td></tr><tr style=";height:28px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1目的：當新人提升後仍無法達到考核標準時需溝通提前結束試用期</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2步驟：</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第一步，提升3次後考核仍無法達標標準時，將結果同步給團隊，並與團隊達成一致。</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第二步，與人資思柳同步新人學習情況，及驗收的標準，告知需提前結束試用期</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第三步，與新人溝通，告知考核結果，溝通離職事宜。</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第四步，將結果反饋人資，由人資提供離職表</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第五步，按照流程協助新人辦理離職手續（<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">由督導代為走簽核流程</span>）</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第六步，請技術部阿升將電話重新刷機</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3時間：新人經3次提升後仍無法達到考核標準後</span></p></td></tr></tbody></table><p><br></p><p><br></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>12</strong></span></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">按照規劃實操</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">依照第二階段的學習內容教學&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">確保新人按照學習規劃實操</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">督導</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新人第二階段學習期間</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">根據學習規劃對新人進行教學</span></p></td></tr><tr style=";height:28px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、新人規劃表，請參考：</span><a href="file:///C:/Users/<USER>/Desktop/%E7%9D%A3%E5%B0%8E%E5%AD%B8%E7%BF%92%E4%BA%8B%E5%8B%99/%E6%96%B0%E4%BA%BA%E5%9F%B9%E8%A8%93%E5%AD%B8%E7%BF%92/T5-%E6%96%B0%E5%AE%A2%E6%9C%8D%E5%AD%A6%E4%B9%A0%E8%A7%84%E5%88%92--2020.9%E6%A2%B3%E7%90%86(2).xlsx" style="text-decoration: underline; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新人培訓規劃</span></a></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、注意事項：教學過程中可根據新人掌握程度調整學習規劃</span></p></td></tr></tbody></table><p><br></p><p><br></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>13</strong></span></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">學習電話接聽</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">依照第二階段的學習內容上線接聽電話&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">按規範實操確保達到考核標準</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新人</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新人第二階段學習期間</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="margin-left: 0px; text-align: center; line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">根據學習規劃進行實操</span></p></td></tr><tr style=";height:28px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.目的：按照規劃進行實操，確保掌握學習內容及接聽規範</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.學習方式：</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.1業務學習：</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">參考第一階段的業務學習步驟</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.2電話接聽</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.2.1電話撥測：</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.2.1.1 &nbsp; 撥測方式：由督導充當會員角色，進行電話撥測；依照話術文檔進行角色對練</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">撥測內容：依照業務學習內容或來電記錄分類問題進行撥測</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.2.1.2 &nbsp; 驗收標準：按照接聽規範進行接聽，撥測獨立解決率需達到70%或以上 </span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.2.1.3&nbsp; 實操時間：5-7天</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.2.2電話監聽輔導上線</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.2.2.1實操方式：上線前期（1-2周內）由督導輔導；獨立解決率達到60%或以上可由在職客服輔導上線接聽電話</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.2.2.2注意事項：</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">選人：挑選電話接聽比較規範及優秀，且業務能力熟練人員並與主任達成一致（需在工作安排許可的情況下）</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.2.2.3 &nbsp; 驗收標準：按周制定解決率目標，最終獨立解決率達70%或以上方可獨立接聽</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.2.2.4 &nbsp; 輔導時間：2周-3周內</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.2.3 &nbsp; 獨立接聽</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.2.3.1 &nbsp; 實操內容：獨立上線接聽電話、需記錄每日詢問的問題量</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #000000;">2.2.3.2&nbsp; &nbsp;實操方式：需將每日問題及正確處理方式記錄在電話接聽問題<a href="https://docs.google.com/spreadsheets/d/1CWvFVS2PSmcQS1-yXgb_Nn_UyQJtSrbnKRXfOXKdAGk/edit#gid=2075508187" target="_self">匯總表格<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">(點擊可進入)</span></a>中，計算每日的獨立解決率，確保每日問題均能得到改善</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.2.3.3 &nbsp; 驗收標準：按周制定解決率目標，最終解決率達90%或以上</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.2.3.4&nbsp; &nbsp;實操時間：4周</span></p></td></tr></tbody></table><p><br></p><p><br></p><p><br></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>14</strong></span></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">考核</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">按照電話電話規範進行接聽&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">驗收電話接聽的掌握程度</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新人</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">實操完電話接聽的各個階段後</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">依照規範進行電話接聽，解決會員問題</span></p></td></tr><tr style=";height:28px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1目的：驗收電話接聽的掌握程度</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2考核方式：抽檢每日接聽的電話進行評估</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3頻率：按日/次、按周/次</span></p></td></tr></tbody></table><p><br></p><p><br></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>15</strong></span></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">評估</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">依照新人第二階段學習進行驗收評估&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">驗收新人電話接聽的掌握程度，確認是否達到標準</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">督導</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">根據每日/周電話情況進行評估是否達標</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">根據評估標準進行評估</span></p></td></tr><tr style=";height:28px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1 &nbsp; 目的：驗收新人對學習內容的掌握程度</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2 &nbsp; 評估方式及標準：</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.1 &nbsp; 撥測：督導評估，按日制定目標，獨立解決率達70%或以上</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.2 &nbsp; 輔助上線：輔助人評估，按周制定目標，獨立解決率達70%或以上</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.3 &nbsp; 正式上線：督導評估，按周制定目標，獨立解決率達90%或以上</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">需關注：溝通表達能力、反應能力、理解能力並記錄在檔</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.4 &nbsp; 注意事项：需記錄每日撥測的成績</span></p><p style="margin-left: 26px; line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3評估結果</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3.1達標：按正常規劃，進入轉正評估階段</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3.2不達標</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">可改善的情況</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第一步，梳理考核不達標的原因（是業務知識還是溝通技巧）</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第二步，針對未掌握的業務知識或溝通技巧進行再次講解提升</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第三步，根據情況給予新人時間撥測及輔助接聽，在規定時間內再次評估</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第四步，驗收合格後進入轉正評估階段</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3.2.2&nbsp; 不可改善的情況</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第一步，按照可改善的情況的步驟進行二次提升</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第二步，若提升後考核仍無法達到標準，且無法在規定時間內完成學習進度或出現行為態度不符合團隊規範時，需及時與團隊反饋新人情況</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第三步，與團隊討論達成一致，並與新人溝通達成一致，進行最後的提升學習並驗收，若仍無法改善則會提前結束試用期</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第四步，第3次提升，仍無法達到標準則辦理離職手續，並與人資思柳同步，告知新人學習情況。</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3提升方式：</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3.1跟進新人每日記錄的電話接聽問題匯總表格，及時提煉未掌握的業務知識進行再次培訓及考試驗收</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3.2抽檢電話錄音：抽檢時長比較久（5分鐘或以上）的電話錄音，提煉可改善的溝通技巧，並按時培訓提升電話溝通技巧分享，增加電話撥測進行驗收</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.4</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp;</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">時間：依照培訓規劃學習完業務知識後</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">頻率：按天/次驗收，按周/次驗收</span></p></td></tr></tbody></table><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">曹超雲</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-18 13:47:13</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>16</strong></span></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">結束</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">當新人提升後仍無法達到考核標準時需溝通提前結束試用期&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">驗收新人學習內容的掌握程度，無法達到標準</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">團隊/督導</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">經過3次提升仍無法達到目標時</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">根據評估標準進行評估、與團隊反饋結果並達成一致</span></p></td></tr><tr style=";height:28px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1目的：當新人提升後仍無法達到考核標準時需溝通提前結束試用期</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2步驟：</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第一步，提升3次後考核仍無法達標標準時，將結果同步給團隊，並與團隊達成一致。</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第二步，與人資思柳同步新人學習情況，及驗收的標準，告知需提前結束試用期</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第三步，與新人溝通，告知評估結果，溝通離職事宜。</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第四步，將結果反饋人資，由人資提供</span><a href="file:///C:/Users/<USER>/Desktop/%E7%9D%A3%E5%B0%8E%E5%AD%B8%E7%BF%92%E4%BA%8B%E5%8B%99/%E5%93%A1%E5%B7%A5%E9%9B%A2%E8%81%B7%E8%BD%89%E6%AD%A3%E8%A1%A8/%E3%80%903008-1%E3%80%91%E8%81%8C%E5%91%98%E7%A6%BB%E8%81%8C%E7%94%B3%E8%AF%B7%E8%A1%A8-20140930(2).doc" style="text-decoration: underline; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">離職表</span></a></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第五步，按照流程協助新人辦理離職手續（由督導代為走簽核流程）</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第六步，請技術部阿升將電話重新刷機</span></p><p style="line-height: 1.5em;"><br></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3時間：新人經3次提升後仍無法達到考核標準後</span></p></td></tr></tbody></table><p><br></p><p><br></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>17</strong></span></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">轉正評估</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新人學習完所有業務知識並通過考核後，進入轉正評估&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">確認新人是否可以達到轉正標準</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">督導</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">學習完業務知識並通過考核後</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">根據轉正標準進行評估</span></p></td></tr><tr style=";height:28px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; &nbsp; </span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">目的：評估新人試用期是否達到轉正標準</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; &nbsp; </span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">驗收內容及標準：</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.1筆試</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">內容：業務知識轉正筆試考</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">驗收標準：筆試達90分或以上</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.2實操</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">內容：抽檢電話錄音</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">驗收標準：參照<a href="http://192.168.8.29PublicFile591房屋交易网（台湾）【12】客服相关【0103】客服督导日常工作内容2019.8其他盤資料-重要勿刪工作事務2019督導20172020年督导岗位目标2020年星级学习教材更新一星教材试题最最新确认版-文宇已查阅的" target="_self">電話評估標準</a>，分數達80分或以上</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2.3審核事項</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">內容：仲介認證審核</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">驗收標準：審核資料正確率100%</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3操作時間：轉正前2周進行評估</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.4評估結果：</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.4.1 &nbsp; 通過</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">反饋團隊達成一致，進入下一步：簽核轉正申請表</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.4.2 &nbsp; 不通過</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">反饋團隊達成一致，辦理離職手續（操作步驟請見：編號16）</span></p></td></tr></tbody></table><p><br></p><p><br></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>18</strong></span></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">簽核轉正申請表</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">轉正評估通過後，簽核轉正申請表&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新人已達轉正標準，正式轉正</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">督導</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">轉正評估通過後</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">根據轉正申請表格式填寫</span></p></td></tr><tr style=";height:28px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; &nbsp; </span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">目的：為新人辦理轉正手續</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; &nbsp; </span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">步驟：</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第一步：人資思柳提供</span><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">轉正申請表</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">（一般是轉正前1天）</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第二步：讓新人按照格式填寫【職員自評】的內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第三步：督導填寫【直屬主管評估】及【轉正薪資】</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第三步：主管、部門主管簽字</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">注意事項</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3.1表格內容不允許塗改</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3.2表格內容需手寫，不可機印</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.4 &nbsp; 直屬主管評估內容需包含:</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">■ 筆試分數</span></p><p style="line-height: 1.5em;"><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">■&nbsp;</span></span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">電話分數</span></p><p style="line-height: 1.5em;"><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">■&nbsp;</span></span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">審核分數</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">■&nbsp;</span>優點</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">■&nbsp;</span>需提升</span></p></td></tr></tbody></table><p><br></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>19</strong></span></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">轉正溝通</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">與新人同步轉正評估情況&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">確保新人了解進入現場情況</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">督導</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">正式進入現場前</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">溝通試用期評估情況，進入現場前的事項</span></p></td></tr><tr style=";height:28px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細內容</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.1目的：確保新人了解試用期評估情況及進入現場注意事項</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.2 &nbsp; 步驟：</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第一步，與主管/主管討論班級分配</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第二步，定會議室，與新人同步時間</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第三步，正式溝通，內容包含評估結果、班級、位置、入現場的注意事項、後續的規劃</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第四步，溝通結束後，需將新人試用期學習規劃評估表，提交給主任</span></p><p style="line-height: 1.5em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.3</span><span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 14px; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp;</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">時間：新人簽核轉正申請表後</span><br></p></td></tr></tbody></table><p><br></p><table cellspacing="0" cellpadding="0" width="652"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>編號</strong></span></p></td><td width="95" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>任務</strong></span></p></td><td width="501" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>目的與程式</strong></span></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>20</strong></span></p></td><td width="95" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">進入現場</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">What</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">正式轉正</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Why</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">可獨立處理客服相關事務</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">Who</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新人</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">When</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">簽核轉正申請表完成溝通後</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">How</span></p></td><td width="406" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">搬位置，進入班級</span></p></td></tr><tr style=";height:28px"><td width="501" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">無</span></p></td></tr></tbody></table><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">曹超雲</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-18 15:19:37</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><table cellpadding="0" cellspacing="0" width="1088" style="width: 800px;"><colgroup><col width="73" style="width:73px"><col width="78" style=";width:79px"><col width="83" style=";width:83px"><col width="71" style=";width:71px"><col width="696" style=";width:696px"><col width="87" style=";width:87px"></colgroup><tbody><tr height="30" style=";height:30px" class="firstRow"><td colspan="6" height="30" width="1088" style="" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">电话评定标准</span></td></tr><tr height="36" style=";height:36px"><td height="36" width="73" style="" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">项目</span></td><td width="79" style="" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">分类</span></td><td width="83" style="" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">不达标次数</span></td><td width="71" style="" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">分值</span></td><td width="696" style="" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">扣分说明</span></td><td width="87" style="" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">规则</span></td></tr><tr height="303" style=";height:303px"><td rowspan="2" height="555" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">服务意识</span></td><td width="79" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">服务规范</span></td><td width="83" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">同一扣分项累计出现10次，则本次考核不达标</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">10</span></td><td width="696" style=""><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">标准：需完全符合电话客服接听规范要求，并使用规范性话术，若出现下列问题（包含但不仅限于）将被扣分</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">：</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1. 开头语不规范</span></p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2. 结束语不规范<br>3. 个资未核实完整<br>4. 出现明显的大陆用词“1=幺”<br>5. 基本未使用“您”、“请”、“是”“好的”或请会员等待时，未使用“请稍等，客服与专员确认”、“不好意思”，让您久等了”等礼貌用语<br>6. 出现服务禁语：“喂”、“不知道”等<br>7. 频繁使用语气助词：吧、嘛、呀等<br>8. 语言组织不够专业、规范，过多口语：“嗯、那个、这个、这样的话、那这边” 等<br>9.当日来记录未做<br>10.出现口误不及时纠正</span></td><td width="87" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1条2分</span></td></tr><tr height="252" style=";height:252px"><td height="252" width="79" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">服务态度</span></td><td width="83" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">同一扣分项累计出现3次，则本次考核不达标</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">20</span></td><td width="696" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">标准：以良好的服务意识、态度，亲切、礼貌、耐心的为会员提供主动、贴心的服务体验，沟通过程中若出现下列问题（包含但不仅限于）将被扣分：<br>1. 抢话、插话，无故粗暴打断用户<br>2. 明显的情绪波动，突然提高或降低音量，不耐烦<br>3. 讲话随意、有气无力，懒散、敷衍、不耐煩<br>4. 冷场、未及时回应会员<br>5. 反问、指责、质问、挑衅会员<br>6. 未经申请主动挂机<br>7. 在通话中摔键盘、敲桌子发泄情绪<br>8.电话接通仍与旁人聊天<br>9.用户抱怨及不满意时未安抚或致歉（若用户重复抱怨时未再次安抚或致歉）</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1条5分</span></td></tr><tr height="267" style=";height:267px"><td height="267" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">沟通表达</span></td><td width="79" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">沟通技巧</span></td><td width="83" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">同一扣分项累计出现5次，则本次考核不达标</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">20</span></td><td width="696" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">标准：耐心倾听、正确理解用户需求，适时互动、引导用户，清楚表达，有效安抚。若出现下列问题（包含但不仅限于）将被扣分：<br>1. 用户已表达清楚，但客服无法快速理解用户诉求（一通电话大于2次重复询问）<br>2. 注意力不集中，没有认真倾听用户的问题，导致理解错误或反复询问<br>3. 对于用户的问题，未进行有效的引导<br>4. 表达不清晰、啰嗦、语无伦次、没有条理<br>5. 语速过快或过慢，未能与用户节奏同步，导致用户听不清楚或不耐烦<br>6. 讲话方式直接、沟通强势，冒犯用户<br>7. 当用户质疑与不满时，未安抚与引导用户，如：用户表示要去投诉或提告，未想办法缓和用户情绪，而直接请用户去告或者去投诉<br>8. 无沟通技巧，机械式回复（如一直重复：很抱歉，我们的规则就是这样的）<br></span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1条5分</span></td></tr><tr height="121" style=";height:121px"><td height="121" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">业务解答</span></td><td width="79" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">解答、处理差错</span></td><td width="83" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">同一扣分项累计出现2次，则本次考核不达标</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">50</span></td><td width="696" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">标准：按业务流程、规范，完整的、正确的解答/解决用户的问题。若出现下列问题（包含但不仅限于）将被扣分：<br>1. 未按规则、流程处理（如：该停权未停权）<br>2. 给与用户无法实现的承诺<br>3. 未针对用户提出的问题进行完整回复<br></span><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4. 提供错误解决方案且未及时纠正</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5. 未解决到用户的问题（如：忘记操作）</span></p></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1条10分</span></td></tr><tr height="120" style=";height:120px"><td height="120" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">加分项</span></td><td width="79" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">可加分类型<br> &nbsp; &nbsp; （加至10分封顶）<br> &nbsp; &nbsp; </span></td><td width="83" style=""><br></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">10</span></td><td width="696" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1. 主动的(区别于他人)、超出用户预期的服务<br>2. 面对理解能力很差、情绪暴躁等会员，仍然能保持耐心、贴心的优质服务<br>3. 超凡的沟通技巧，在会员极为不满的情况下，能通过沟通技巧，得到会员的理解或认同<br>4. 通话中非常热情、情绪饱满，快速有效解决用户的问题，整个通话过程给用户带来非常愉快的感受<br>5. 用户在通话中有明确表示认可（如：你的服务很好，可参考好评录音判定标准）</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1条2分</span></td></tr><tr height="145" style=";height:145px"><td height="145" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">致命错误</span></td><td width="79" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">致命错误类型（考核中出现其中一项则中断考核）</span></td><td width="83" style=""><br></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">100</span></td><td width="696" style=""><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1. 在电话中辱骂用</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">户</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2. 与用户讨论政治问题</span></p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3. 恶意外泄其他用户的个人资料<br>4. 恶意销毁相关数据<br>5. 未按标准提供服务，给公司或用户造成严重损失的（如：导致用户被罚款，公司见报、引发公司高层关注等严重问题）<br></span></td><td width="87" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">该通电话0分</span></td></tr><tr height="42" style=";height:42px"><td colspan="6" height="42" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">抽50通，试用期80分通过</span></td></tr></tbody></table><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">曹超雲</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-18 15:21:50</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">轉正申請表填寫格式如下：<br></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><img src="https://zsk.591.com.tw/upload/question/20201118/1605682764215726.png" alt="image.png"></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li></ul> <h3 class="more-answer">我要補充</h3> <div style=""><div id="editor_79342062172978100" class="edui-default" style=""><div id="edui1" class="edui-editor  edui-default" style="width: 100%; z-index: 999;"><div id="edui1_toolbarbox" class="edui-editor-toolbarbox edui-default"><div id="edui1_toolbarboxouter" class="edui-editor-toolbarboxouter edui-default"><div class="edui-editor-toolbarboxinner edui-default"><div id="edui2" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui2&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui3" class="edui-box edui-button edui-for-fullscreen edui-default"><div id="edui3_state" onmousedown="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui3_body" unselectable="on" title="全屏" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui3&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui3&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui4" class="edui-box edui-button edui-for-source edui-default"><div id="edui4_state" onmousedown="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui4_body" unselectable="on" title="源代码" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui4&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui4&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui5" class="edui-box edui-button edui-for-undo edui-default"><div id="edui5_state" onmousedown="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui5_body" unselectable="on" title="撤销" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui5&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui5&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui6" class="edui-box edui-button edui-for-redo edui-default"><div id="edui6_state" onmousedown="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui6_body" unselectable="on" title="重做" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui6&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui6&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui7" class="edui-box edui-button edui-for-simpleupload edui-default"><div id="edui7_state" onmousedown="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui7_body" unselectable="on" title="单图上传" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui7&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui7&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui8" class="edui-box edui-button edui-for-imageleft edui-default"><div id="edui8_state" onmousedown="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui8_body" unselectable="on" title="左浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui8&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui8&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui9" class="edui-box edui-button edui-for-imageright edui-default"><div id="edui9_state" onmousedown="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui9_body" unselectable="on" title="右浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui9&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui9&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui10" class="edui-box edui-button edui-for-imagecenter edui-default"><div id="edui10_state" onmousedown="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui10_body" unselectable="on" title="居中" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui10&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui10&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui11" class="edui-box edui-separator  edui-default"></div><div id="edui12" class="edui-box edui-button edui-for-date edui-default"><div id="edui12_state" onmousedown="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui12_body" unselectable="on" title="日期" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui12&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui12&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui13" class="edui-box edui-button edui-for-time edui-default"><div id="edui13_state" onmousedown="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui13_body" unselectable="on" title="时间" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui13&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui13&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui14" class="edui-box edui-button edui-for-horizontal edui-default"><div id="edui14_state" onmousedown="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui14_body" unselectable="on" title="分隔线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui14&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui14&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui15" class="edui-box edui-separator  edui-default"></div><div id="edui16" class="edui-box edui-splitbutton edui-for-inserttable edui-default"><div title="插入表格" id="edui16_state" onmousedown="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui16_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onArrowClick();"></div></div></div></div><div id="edui19" class="edui-box edui-button edui-for-deletetable edui-default"><div id="edui19_state" onmousedown="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui19_body" unselectable="on" title="删除表格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui19&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui19&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui20" class="edui-box edui-button edui-for-insertrow edui-default"><div id="edui20_state" onmousedown="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui20_body" unselectable="on" title="前插入行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui20&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui20&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui21" class="edui-box edui-button edui-for-insertcol edui-default"><div id="edui21_state" onmousedown="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui21_body" unselectable="on" title="前插入列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui21&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui21&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui22" class="edui-box edui-button edui-for-mergeright edui-default"><div id="edui22_state" onmousedown="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui22_body" unselectable="on" title="右合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui22&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui22&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui23" class="edui-box edui-button edui-for-mergedown edui-default"><div id="edui23_state" onmousedown="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui23_body" unselectable="on" title="下合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui23&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui23&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui24" class="edui-box edui-button edui-for-deleterow edui-default"><div id="edui24_state" onmousedown="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui24_body" unselectable="on" title="删除行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui24&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui24&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui25" class="edui-box edui-button edui-for-deletecol edui-default"><div id="edui25_state" onmousedown="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui25_body" unselectable="on" title="删除列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui25&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui25&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui26" class="edui-box edui-button edui-for-splittorows edui-default"><div id="edui26_state" onmousedown="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui26_body" unselectable="on" title="拆分成行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui26&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui26&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui27" class="edui-box edui-button edui-for-splittocols edui-default"><div id="edui27_state" onmousedown="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui27_body" unselectable="on" title="拆分成列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui27&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui27&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui28" class="edui-box edui-button edui-for-splittocells edui-default"><div id="edui28_state" onmousedown="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui28_body" unselectable="on" title="完全拆分单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui28&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui28&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui29" class="edui-box edui-button edui-for-mergecells edui-default"><div id="edui29_state" onmousedown="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui29_body" unselectable="on" title="合并多个单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui29&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui29&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui36" class="edui-box edui-button edui-for-edittable edui-default"><div id="edui36_state" onmousedown="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui36_body" unselectable="on" title="表格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui36&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui36&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui41" class="edui-box edui-button edui-for-edittd edui-default"><div id="edui41_state" onmousedown="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui41_body" unselectable="on" title="单元格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui41&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui41&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui42" class="edui-box edui-separator  edui-default"></div><div id="edui43" class="edui-box edui-menubutton edui-for-lineheight edui-default"><div title="行间距" id="edui43_state" onmousedown="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui43_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onArrowClick();"></div></div></div></div><div id="edui52" class="edui-box edui-menubutton edui-for-rowspacingtop edui-default"><div title="段前距" id="edui52_state" onmousedown="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui52_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onArrowClick();"></div></div></div></div><div id="edui59" class="edui-box edui-menubutton edui-for-rowspacingbottom edui-default"><div title="段后距" id="edui59_state" onmousedown="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui59_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onArrowClick();"></div></div></div></div></div><div id="edui66" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui66&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui67" class="edui-box edui-combox edui-for-fontfamily edui-default"><div title="字体" id="edui67_state" onmousedown="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui67_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onButtonClick(event, this);">字体</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onArrowClick();"></div></div></div></div><div id="edui80" class="edui-box edui-combox edui-for-customstyle edui-default"><div title="自定义标题" id="edui80_state" onmousedown="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui80_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onButtonClick(event, this);">自定义标题</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onArrowClick();"></div></div></div></div><div id="edui86" class="edui-box edui-combox edui-for-paragraph edui-default"><div title="段落格式" id="edui86_state" onmousedown="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui86_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onButtonClick(event, this);">段落格式</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onArrowClick();"></div></div></div></div><div id="edui95" class="edui-box edui-combox edui-for-fontsize edui-default"><div title="字号" id="edui95_state" onmousedown="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui95_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onButtonClick(event, this);">字号</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onArrowClick();"></div></div></div></div><div id="edui110" class="edui-box edui-button edui-for-link edui-default"><div id="edui110_state" onmousedown="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui110_body" unselectable="on" title="超链接" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui110&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui110&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui111" class="edui-box edui-button edui-for-indent edui-default"><div id="edui111_state" onmousedown="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui111_body" unselectable="on" title="首行缩进" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui111&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui111&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui112" class="edui-box edui-button edui-for-bold edui-default"><div id="edui112_state" onmousedown="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui112_body" unselectable="on" title="加粗" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui112&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui112&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui113" class="edui-box edui-splitbutton edui-for-forecolor edui-default edui-colorbutton"><div title="字体颜色" id="edui113_state" onmousedown="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui113_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui113_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onArrowClick();"></div></div></div></div><div id="edui116" class="edui-box edui-splitbutton edui-for-backcolor edui-default edui-colorbutton"><div title="背景色" id="edui116_state" onmousedown="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui116_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui116_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onArrowClick();"></div></div></div></div><div id="edui119" class="edui-box edui-button edui-for-italic edui-default"><div id="edui119_state" onmousedown="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui119_body" unselectable="on" title="斜体" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui119&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui119&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui120" class="edui-box edui-button edui-for-underline edui-default"><div id="edui120_state" onmousedown="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui120_body" unselectable="on" title="下划线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui120&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui120&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui121" class="edui-box edui-button edui-for-strikethrough edui-default"><div id="edui121_state" onmousedown="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui121_body" unselectable="on" title="删除线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui121&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui121&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui122" class="edui-box edui-button edui-for-superscript edui-default"><div id="edui122_state" onmousedown="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui122_body" unselectable="on" title="上标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui122&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui122&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui123" class="edui-box edui-button edui-for-subscript edui-default"><div id="edui123_state" onmousedown="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui123_body" unselectable="on" title="下标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui123&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui123&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui124" class="edui-box edui-button edui-for-removeformat edui-default"><div id="edui124_state" onmousedown="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui124_body" unselectable="on" title="清除格式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui124&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui124&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui125" class="edui-box edui-button edui-for-blockquote edui-default"><div id="edui125_state" onmousedown="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui125_body" unselectable="on" title="引用" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui125&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui125&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui126" class="edui-box edui-button edui-for-pasteplain edui-default"><div id="edui126_state" onmousedown="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui126_body" unselectable="on" title="纯文本粘贴模式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui126&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui126&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui127" class="edui-box edui-menubutton edui-for-insertorderedlist edui-default"><div title="有序列表" id="edui127_state" onmousedown="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui127_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onArrowClick();"></div></div></div></div><div id="edui140" class="edui-box edui-menubutton edui-for-insertunorderedlist edui-default"><div title="无序列表" id="edui140_state" onmousedown="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui140_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onArrowClick();"></div></div></div></div><div id="edui147" class="edui-box edui-button edui-for-cleardoc edui-default"><div id="edui147_state" onmousedown="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui147_body" unselectable="on" title="清空文档" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui147&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui147&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div></div></div></div><div id="edui1_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;"><div id="edui1_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui1&quot;].showWordImageDialog();">点击上传</div><div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui1&quot;].hideToolbarMsg();">x</div><div id="edui1_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div><div style="height:0;overflow:hidden;clear:both;" class="edui-default"></div></div><div id="edui1_message_holder" class="edui-editor-messageholder edui-default"></div></div><div id="edui1_iframeholder" class="edui-editor-iframeholder edui-default" style="width: 100%; height: 300px; z-index: 999; overflow: hidden;"><iframe id="ueditor_0" width="100%" height="100%" frameborder="0" src="javascript:void(function(){document.open();document.write(&quot;<!DOCTYPE html><html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}
body{margin:8px;font-family:sans-serif;font-size:13px;}p{margin:5px 0;}</style><link rel='stylesheet' type='text/css' href='/static/ueditor/themes/iframe.css'/></head><body class='view' ></body><script type='text/javascript'  id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant0'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);</script></html>&quot;);document.close();}())"></iframe></div><div id="edui1_bottombar" class="edui-editor-bottomContainer edui-default"><table class="edui-default"><tbody class="edui-default"><tr class="edui-default"><td id="edui1_elementpath" class="edui-editor-bottombar edui-default"><div class="edui-editor-breadcrumb">元素路径:</div></td><td id="edui1_wordcount" class="edui-editor-wordcount edui-default">字数统计</td><td id="edui1_scale" class="edui-editor-scale edui-default" style="display: none;"><div class="edui-editor-icon edui-default"></div></td></tr></tbody></table></div><div id="edui1_scalelayer" class="edui-default"></div></div></div> <div class="edit-btn"><button type="button" class="button">提交回答</button> <label data-v-0fe26aab="" for="commonCheckbox" class="checkbox"><input data-v-0fe26aab="" type="checkbox" id="commonCheckbox" name="commonCheckbox"> <i data-v-0fe26aab=""></i> 需要彈窗，請勾選</label></div></div></div> <div class="index-right"><div class="right-bk"><h3 class="com-title"><a href="javascript:;">我的最爱</a></h3> <div class="item-box clearfix"><span>暫無數據</span></div></div> <div class="hotTag right-bk"><h3 class="com-title">热门标签</h3> <div class="hotTag-box item-box clearfix"><p>無數據</p></div></div> <div class="right-bk rank"><h3 class="com-title"><a href="javascript:;">冠軍排行榜</a></h3> <ul><li>暫無數據</li></ul></div> <div class="activeUser right-bk"><h3 class="com-title">活跃用户</h3> <div class="activeUser-box"><a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a></div></div> <img src="data:image/png;base64,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" class="to-top"></div></div> <!----> <!----> <!----> <div class="page-cnt footer"><p>T5客服知识库<span>|</span>zsk.591.com.tw</p></div></div></div><script type="text/javascript" src="/js/manifest.f192b.js"></script><script type="text/javascript" src="/js/vendor.17570.js"></script><script type="text/javascript" src="/js/app.58254.js"></script><div id="edui_fixedlayer" class="edui-default" style="position: fixed; left: 0px; top: 0px; width: 0px; height: 0px;"><div id="edui148" class="edui-popup  edui-bubble edui-default" onmousedown="return false;" style="display: none;"> <div id="edui148_body" class="edui-popup-body edui-default"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank" class="edui-default"></iframe> <div class="edui-shadow edui-default"></div> <div id="edui148_content" class="edui-popup-content edui-default">  </div> </div></div></div><div id="nprogress"><div class="bar" role="bar" style="transform: translate3d(-38.7714%, 0px, 0px); transition: 500ms;"><div class="peg"></div></div></div></body></html>