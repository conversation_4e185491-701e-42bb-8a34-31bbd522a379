<html class=" "><head><meta charset="utf-8"><title>591客服知識庫</title><link rel="stylesheet" href="/static/font/font-awesome.min.css"><link href="/css/app.e5aa4.css" rel="stylesheet"><script type="text/javascript" charset="utf-8" async="" src="/js/questionDetail.d0a6b.js"></script><style type="text/css">.que-detail-page .index-left .button{width:74px;height:32px;line-height:32px;font-size:13px;margin-right:10px}.que-detail-page .index-left .button:hover{color:#fff}.que-detail-page .detail-box{padding:20px 30px;border:1px solid #e5e5e5;border-radius:4px}.que-detail-page .detail-box h3{font-size:18px;color:#333;margin-bottom:10px}.que-detail-page .detail-box h3 span{color:#f77d24}.que-detail-page .detail-box .add-fav{cursor:pointer}.que-detail-page .d-b-menu{margin:15px 0 25px;font-size:13px;color:#999}.que-detail-page .d-b-menu b{display:inline-block;font-weight:400;margin:0 10px}.que-detail-page .d-b-menu .r{float:right;color:#f77d24}.que-detail-page .d-b-menu .r .fa{margin-right:5px;font-size:16px}.que-detail-page .d-b-cnt{margin-bottom:25px;font-size:13px;color:#666}.que-detail-page .submit-ask{display:block;margin:32px auto 0}.que-detail-page .reply-list{margin:30px 0 10px;overflow:hidden}.que-detail-page .reply-list .reply-item{padding:15px 30px 25px;margin-bottom:30px;border-radius:4px;border:1px solid #e5e5e5}.que-detail-page .reply-list .reply-item h4{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;line-height:50px;font-size:13px}.que-detail-page .reply-list .reply-item h4 img{width:40px;height:40px;border-radius:50%}.que-detail-page .reply-list .reply-item h4 .time{color:#999}.que-detail-page .reply-list .reply-item h4 .button{-ms-flex-item-align:center;align-self:center}.que-detail-page .reply-list .reply-item h4 .delete{color:#666;cursor:pointer}.que-detail-page .reply-list .reply-item .reply-detail{padding:20px 0 30px}.que-detail-page .more-answer{font-size:18px;color:#666;margin-bottom:5px}.que-detail-page .editor-content table{width:100%;border:1px solid #ddd}.que-detail-page .editor-content table td,.que-detail-page .editor-content table th{border:1px solid #ddd;padding:2px 8px;line-height:2}.que-detail-page .editor-content img{max-width:100%;vertical-align:text-bottom}.que-detail-page .edit-btn{text-align:center;margin-top:20px}.que-detail-page .edit-btn .edit-cancle{background:#aaa}</style><style type="text/css">.head-info{line-height:26px;background:#f5f5f5;font-size:13px;color:#999}.head-info a{margin-left:20px;color:#333}.head-search{margin-top:22px}.head-search img{float:left}.head-search .srh-box{float:left;margin:20px 0 0 20px}.head-search .srh-box .srh-input{float:left;width:260px;height:38px;line-height:38px;border:2px solid #37a508;border-radius:4px 0 0 4px}.head-search .srh-box a,.head-search .srh-box button{float:left;width:80px;height:38px;line-height:36px;font-size:16px;color:#fff}.head-search .srh-box .srh-btn{border-radius:0 4px 4px 0}.head-search .srh-box .ask-btn{margin-left:10px}.head-nav{float:left;margin:20px 0 0 60px;color:#fff;font-size:16px;line-height:36px}.head-nav a{display:block;height:100%;color:#333}.head-nav li{position:relative;float:left;margin-right:35px}.head-nav .active a,.head-nav li:hover a{color:#30b940}.head-nav .active:after,.head-nav li:hover:after{display:block;content:"";height:3px;width:100%;background:#30b940}.head-pic{width:100%;height:90px;margin-top:20px;background:url(/static/banner.png) top no-repeat}</style><style type="text/css">.top-breadcrumb{height:20px;margin-top:10px;margin-bottom:40px;font-size:13px;color:#666}.top-breadcrumb .t-b-icon{color:#30b940}.top-breadcrumb span{margin-left:8px}.top-breadcrumb span .fa{font-size:16px;margin-left:8px}.top-breadcrumb span:first-child{margin-left:0}.top-breadcrumb .now{color:#30b940}</style><script type="text/javascript" src="/static/ueditor/ueditor.config.js?20210319" id="configScriptTag"></script><script type="text/javascript" src="/static/ueditor/ueditor.all.js?20210319" id="editorScriptTag"></script><style type="text/css">.checkbox[data-v-0fe26aab]{word-spacing:4px;vertical-align:bottom;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.checkbox i[data-v-0fe26aab]{display:inline-block;width:14px;height:14px;border-radius:2px;border:1px solid #ccc;background:#fff;position:relative;top:1px}.checkbox input[data-v-0fe26aab]{opacity:0;width:0;height:0;margin:0}.checkbox input:checked+i[data-v-0fe26aab]{background:#30b940;border-color:#30b940}.checkbox input:checked+i[data-v-0fe26aab]:before{content:"";position:absolute;left:2px;top:1px;width:11px;height:6px;border-bottom:2px solid #fff;border-left:2px solid #fff;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}</style><style type="text/css">.popbox{position:fixed;top:50%;left:50%;z-index:1011;background:#fff;border-radius:6px;overflow:hidden}.popbox h3{font-size:16px;line-height:40px;padding:0 20px}.popbox h3 span{float:right;color:#666;font-size:13px;cursor:pointer}.popbox .pb-cnt{padding:0 20px;font-size:13px}.popbox .pb-btn-area{position:absolute;bottom:0;left:0;z-index:1;width:100%;text-align:right;padding:0 20px 20px 0}.popbox .pb-btn-area button{height:32px;line-height:32px;border:0;background:#fff;cursor:pointer;border-radius:6px;margin-left:15px;padding:0 15px}.popbox .pb-btn-area button.pb-btn-ok{background:#daf3cf;color:#279005}.popbox .pb-btn-area button.pb-btn-ok:hover{background:#279005;color:#fff}</style><style type="text/css">.shadow{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1010;background:#000}</style><style type="text/css">.footer{margin-top:70px;margin-bottom:20px;padding-top:20px;text-align:center;color:#999;border-top:1px solid #f5f5f5}.footer span{margin:0 10px}</style><script src="/static/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript" defer="defer"></script><link href="/static/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet"><script src="/static/ueditor/third-party/codemirror/codemirror.js" type="text/javascript" defer="defer"></script><link rel="stylesheet" type="text/css" href="/static/ueditor/third-party/codemirror/codemirror.css"><script src="/static/ueditor/third-party/zeroclipboard/ZeroClipboard.js" type="text/javascript" defer="defer"></script><style type="text/css">.activeUser,.favorite,.hotTag,.rank{margin-bottom:40px}</style><style type="text/css">.rank .rank-page{height:60px;line-height:60px;font-size:18px;margin-bottom:20px;text-align:center;background:#fafafa;border-radius:5px}.rank ul{padding:0 20px}.rank li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;border-bottom:1px dotted #f5f5f5;line-height:50px}.rank li .flag{position:relative;display:inline-block;width:20px;height:30px;background:#476472;vertical-align:middle;margin-right:12px;color:#fff;text-align:center;line-height:20px;font-size:13px;font-style:normal}.rank li .flag:after{content:"";position:absolute;bottom:-4px;left:-4px;border:14px solid transparent;border-bottom-color:#fff}.rank li:first-child .flag{background:#fb6757}.rank li:nth-child(2) .flag{background:#fac525}.rank li:nth-child(3) .flag{background:#00a9fb}.rank li img{width:36px;height:36px;border-radius:50%;margin-right:12px}</style><style type="text/css">.activeUser .activeUser-box a{display:inline-block;width:64px;height:64px;border-radius:50%;overflow:hidden;margin:0 6px 12px}</style><style type="text/css">.to-top{display:none;position:fixed;bottom:150px;z-index:5;width:50px;height:50px;cursor:pointer}</style></head><body style=""><div id="app" style=""><div class="que-detail-page" style=""><div class="wiki-hd"><div class="head-info"><p class="page-cnt">
      您好，欢迎来到591客服知识库
      <a href="https://www.591.com.tw/admin.php?new_url=https://zsk.591.com.tw">[请登入]</a> <!----></p></div> <div class="page-cnt head-search clearfix"><a href="/" class="router-link-active"><img src="/static/logo.png"></a> <ul class="head-nav clearfix"><li class="router-link-active active"><a href="/">591房屋交易网</a></li> <li class=""><a href="/life">客服充电站</a></li> <li class=""><a href="/notification">通知事项</a></li></ul> <div class="srh-box"><input type="text" placeholder="请输入搜索内容" class="srh-input"> <button type="button" class="button srh-btn">搜索</button> <a href="/ask/591" class="button button-orange ask-btn">提問</a></div></div> <div class="head-pic"></div></div> <div class="top-breadcrumb com-width"><span><a href="/collection/591/1302" class="">客服質檢相關<i class="fa fa-angle-double-right"></i></a></span><span><a href="/collection/591/1302/1652" class="">作業指導書<i class="fa fa-angle-double-right"></i></a></span><span><strong class="now">问题详情</strong></span></div> <div class="com-width clearfix" style=""><div class="index-left" style=""><div class="detail-box"><h3><!---->T5-星級考核制度</h3> <p class="clearfix d-b-menu"><span class="l">
            黃雪靜&nbsp;&nbsp;&nbsp;浏览90次&nbsp;&nbsp;&nbsp;2020-04-23 17:25:23
          </span> <span class="r"><span class="add-fav"><i class="fa fa-heart"></i>加入最爱</span></span></p> <div class="d-b-cnt editor-content big-img-parent"><div><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">如題</span></p></div></div> <div class="d-b-button"><a href="/edit/591/4058" class="button">编辑</a> <button class="button button-orange">关闭提问</button></div></div> <div class="hr-line"></div> <ul class="reply-list big-img-parent"><li class="reply-item"><h4><div><img src="/upload/head/10177.gif?time=1556271256">&nbsp;&nbsp;&nbsp;
              <span class="user">黃雪靜</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-04-23 17:30:37</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><span style="font-size: 16px;"><strong><span style="font-family: 微软雅黑, sans-serif;">什么是星级考核</span></strong></span></p><p style="text-indent: 0px; "><span style="font-family: 微软雅黑, sans-serif; text-indent: 2em; font-size: 14px;">1、星级考核定义</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 13px; text-indent: 2em;">通过培训、考核、评定的过程，培养客服工作技能，确定星级客服和薪资等级</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 13px; text-indent: 2em;"><br></span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 13px; text-indent: 2em;">2、星级划分标准</span></p><p><span style="font-family: 微软雅黑, sans-serif; font-size: 13px; text-indent: 2em;"></span></p><table><tbody><tr class="firstRow"><td width="251" valign="middle" style="word-break: break-all;" align="center"><span style="font-family:微软雅黑, Microsoft YaHei"><span style="font-size: 14px;">星级</span></span></td><td width="251" valign="middle" style="word-break: break-all;" align="center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">考核资格</span></td><td width="251" valign="middle" style="word-break: break-all;" align="center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">薪资</span></td></tr><tr><td width="251" valign="middle" style="word-break: break-all;" align="center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">一星</span></td><td width="251" valign="middle" style="word-break: break-all;" align="center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">试用期满并通过转正考核</span></td><td width="251" valign="middle" style="word-break: break-all;" align="center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">6200</span></td></tr><tr><td width="251" valign="middle" style="word-break: break-all;" align="center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">二星</span></td><td width="251" valign="middle" style="word-break: break-all;" align="center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">通过一星客服评定满半年及以上</span></td><td width="251" valign="middle" style="word-break: break-all;" align="center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">6500</span></td></tr><tr><td width="251" valign="middle" style="word-break: break-all;" align="center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">三星</span></td><td width="251" valign="middle" style="word-break: break-all;" align="center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">通过二星客服评定满半年及以上</span></td><td width="251" valign="middle" style="word-break: break-all;" align="center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">6900</span></td></tr><tr><td width="251" valign="middle" style="word-break: break-all;" align="center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">四星</span></td><td width="251" valign="middle" style="word-break: break-all;" align="center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">通过三星客服评定满半年及以上</span></td><td width="251" valign="middle" style="word-break: break-all;" align="center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">7400</span></td></tr></tbody></table><p><br></p><p><strong><span style="font-size:19px;font-family:'微软雅黑',sans-serif">流程管理标准</span></strong><strong><span style="font-size:19px;font-family:'微软雅黑',sans-serif">&nbsp;</span></strong></p><table cellspacing="0" cellpadding="0" width="699"><tbody><tr class="firstRow"><td width="47" style="border: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">编号</span></strong></p></td><td width="57" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">任务</span></strong></p></td><td width="595" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">目的与程序</span></strong></p></td></tr><tr><td width="47" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1</span></strong></p></td><td width="57" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:12px;font-family:'微软雅黑',sans-serif;color:black">制定各星级学习内容、要求及考核方式</span></p></td><td width="50" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">What</span></p></td><td width="520" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">每个星级需要学习的事务类型与内容</span></p></td></tr><tr><td width="50" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">Why</span></p></td><td width="520" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">星级辅导与考核的依据</span></p></td></tr><tr><td width="50" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">Who</span></p></td><td width="520" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">客服质检</span></p></td></tr><tr><td width="50" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">When</span></p></td><td width="520" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">每年例行评估一次</span></p><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">当业务有重大调整（增/减）时</span></p></td></tr><tr><td width="50" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">How</span></p></td><td width="520" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-size:13px;font-family:'微软雅黑',sans-serif">根据事务的难易、重要程度以及学习所需的时间进行综合考量</span></p></td></tr><tr style=";height:165px"><td width="595" colspan="2" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="165"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">详细内容：</span></p><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif"><img src="/upload/question/20201225/1608882850395220.png" alt="image.png" width="583" height="152" style="width: 583px; height: 152px;"></span></p><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif"><br></span></p><img src="/upload/question/20201225/1608882927294355.png" alt="image.png"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:red">&nbsp;<img src="/upload/question/20201225/1608882965970390.png" alt="image.png"></span></p><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">&nbsp;</span></p><img src="/upload/question/20201225/1608883016268968.png" alt="image.png"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">&nbsp;</span></p><img src="/upload/question/20201225/1608883068746519.png" alt="image.png"><p class="MsoListParagraph" style="margin-left:0"><br></p></td></tr></tbody></table><p class="MsoListParagraph" style="margin-left:32px"><strong><span style="font-size:19px;font-family:'微软雅黑',sans-serif">&nbsp;</span></strong></p><table cellspacing="0" cellpadding="0" width="699"><tbody><tr class="firstRow"><td width="47" style="border: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">编号</span></strong></p></td><td width="57" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">任务</span></strong></p></td><td width="595" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">目的与程序</span></strong></p></td></tr><tr><td width="47" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2</span></strong></p></td><td width="57" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:12px;font-family:'微软雅黑',sans-serif;color:black">明确星级考核内容及评分标准</span></p></td><td width="66" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">What</span></p></td><td width="350" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">明确每项事务需要掌握的程度</span></p></td></tr><tr><td width="66" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">Why</span></p></td><td width="350" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">为学习提供最低标准，统一团队服务水平</span></p></td></tr><tr><td width="66" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">Who</span></p></td><td width="350" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">客服质检</span></p></td></tr><tr><td width="66" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">When</span></p></td><td width="350" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">学习内容有新增或现有标准不满足服务需求时</span></p></td></tr><tr><td width="66" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">How</span></p></td><td width="350" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-size:13px;font-family:'微软雅黑',sans-serif">根据事务的难易、重要程度进行综合考量</span></p></td></tr><tr style=";height:216px"><td width="595" colspan="2" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="216"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">详细内容：</span></p><img src="/upload/question/20201225/1608883112644357.png" alt="image.png"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">&nbsp;</span></p><img src="/upload/question/20201225/1608883144831127.png" alt="image.png"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">&nbsp;</span></p><img src="/upload/question/20201225/1608883173561608.png" alt="image.png"><p class="MsoListParagraph" style="margin-left:0"><br></p></td></tr></tbody></table><p><strong><span style="font-size:19px;font-family:'微软雅黑',sans-serif">&nbsp;&nbsp; <span style="color:red">&nbsp;&nbsp;&nbsp;&nbsp;</span></span></strong></p><table cellspacing="0" cellpadding="0" width="699"><tbody><tr style=";height:45px" class="firstRow"><td width="47" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">编号</span></strong></p></td><td width="57" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">任务</span></strong></p></td><td width="595" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">目的与程序</span></strong></p></td></tr><tr><td width="47" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">3</span></strong></p></td><td width="57" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">收集考核需求</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">What</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">星级考核报名通知</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">Why</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">收集考核人员名单</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">Who</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">客服质检</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">When</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">月底之前</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">How</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-size:13px;font-family:'微软雅黑',sans-serif">以有度方式在新功能群内通知</span></p></td></tr><tr style=";height:245px"><td width="595" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="245"><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1、 </span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">与主任确认需求：每月26日之前于骨干团队群内与AB班主任确认是否有考核需求；</span></p><p class="MsoListParagraph" style="margin-left:50px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.1<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">若无，则结束</span></p><p class="MsoListParagraph" style="margin-left:50px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.2<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">若有，则进入下一流程</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2、 </span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">通知客服报名考核：每月25日之前于新通知群内发布考核报名通知。</span></p><p class="MsoListParagraph" style="margin-left:0;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:gray">详细内容：</span></p><p><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:gray">*</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:gray">年*月份星级考核报名通知</span></p><p class="MsoListParagraph" style="margin-left:28px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:gray">1.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; </span></span><span style="font-size:13px;font-family:   '微软雅黑',sans-serif;color:gray">报名范围：T5-全体客服人员（星级学习已达到考核标准者）</span></p><p class="MsoListParagraph" style="margin-left:28px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:gray">2.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; </span></span><span style="font-size:13px;font-family:   '微软雅黑',sans-serif;color:gray">报名方式：至各班主任进行报名，由主任将名单提交给质检</span></p><p class="MsoListParagraph" style="margin-left:28px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:gray">3.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; </span></span><span style="font-size:13px;font-family:   '微软雅黑',sans-serif;color:gray">考核方式：参见星级考核工作流程</span></p><p class="MsoListParagraph" style="margin-left:28px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:gray">4.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; </span></span><span style="font-size:13px;font-family:   '微软雅黑',sans-serif;color:gray">报名时间：截止至当月底</span></p></td></tr></tbody></table><p><strong><span style="font-size:19px;font-family:'微软雅黑',sans-serif">&nbsp;&nbsp;&nbsp;&nbsp; </span></strong></p><table cellspacing="0" cellpadding="0" width="699"><tbody><tr style=";height:45px" class="firstRow"><td width="47" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">编号</span></strong></p></td><td width="57" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">任务</span></strong></p></td><td width="595" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">目的与程序</span></strong></p></td></tr><tr><td width="47" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">4</span></strong></p></td><td width="57" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">客服与主任报名</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">What</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">星级考核报名</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">Why</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">获取考核资格</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">Who</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">准备参加考核的客服</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">When</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">每月30日之前</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">How</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">以有度方式进行报名</span></p></td></tr><tr style=";height:178px"><td width="595" colspan="2" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="178"><p class="MsoListParagraph" style="margin-left:0;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">详细内容：</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">报名方式：</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:   13px;font-family:'微软雅黑',sans-serif">有度进行报名</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">报名时间：</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:   13px;font-family:'微软雅黑',sans-serif">截止当月月底之前</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">3.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">跟谁报名：</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:   13px;font-family:'微软雅黑',sans-serif">与各班主任进行报名，并说明需考核几星</span></p></td></tr></tbody></table><p><strong><span style="font-size:19px;font-family:'微软雅黑',sans-serif">&nbsp;&nbsp;&nbsp;&nbsp; </span></strong></p><table cellspacing="0" cellpadding="0" width="699"><tbody><tr style=";height:45px" class="firstRow"><td width="47" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">编号</span></strong></p></td><td width="57" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">任务</span></strong></p></td><td width="595" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">目的与程序</span></strong></p></td></tr><tr><td width="47" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">5</span></strong></p></td><td width="57" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">提交考核名单</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">What</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">主任向质检提交次月参加星级考核的名单</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">Why</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">明确各星级参与考核人员</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">Who</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">客服主任</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">When</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">每月30日之前</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">How</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-size:13px;font-family:'微软雅黑',sans-serif">以有度方式进行提交</span></p></td></tr><tr style=";height:94px"><td width="595" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="94"><p class="MsoListParagraph" style="margin-left:0;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">提交内容：</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">考核人员与考核的星级</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">提交考核人员考核当月的工作安排</span></p><p style="text-align:justify;text-justify:inter-ideograph"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:red">PS</span></strong><strong><span style="font-size:13px;font-family:   '微软雅黑',sans-serif;color:red">：名单需在月底之前提交给质检，逾期无法参与下月考核</span></strong></p></td></tr></tbody></table><p><strong><span style="font-size:19px;font-family:'微软雅黑',sans-serif">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></strong></p><table cellspacing="0" cellpadding="0" width="699"><tbody><tr style=";height:45px" class="firstRow"><td width="47" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">编号</span></strong></p></td><td width="57" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">任务</span></strong></p></td><td width="595" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">目的与程序</span></strong></p></td></tr><tr style=";height:28px"><td width="47" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" height="28"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">6</span></strong></p></td><td width="57" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">准备考核内容/抽检范围</span></p></td><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">What</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="28"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">准备试题与明确实操抽检范围</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">Why</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">星级考核准备</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">Who</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">客服质检</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">When</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">收到客服人员报名星级考核後的15天内</span></p></td></tr><tr><td width="94" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">How</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-size:13px;font-family:'微软雅黑',sans-serif">-</span></p></td></tr><tr style=";height:110px"><td width="595" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="110"><p class="MsoListParagraph" style="margin-left:0;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">详细内容：</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">笔试题</span></p><p class="MsoListParagraph" style="margin-left:48px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.1<span style="font:9px 'Times New Roman'">&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">出题原则：依据流程图【2.明确各星级考核内容】出题</span></p><p class="MsoListParagraph" style="margin-left:48px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.2<span style="font:9px 'Times New Roman'">&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">题型设置：填空题、判断题、简答题、案例分析题、论述题</span></p><p class="MsoListParagraph" style="margin-left:48px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.3<span style="font:9px 'Times New Roman'">&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">同步整理出试卷答案与评分要点</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">实操抽检</span></p><p class="MsoListParagraph" style="margin-left:50px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.1<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">客服质检核对现场工作安排是否与考核计划一致，若不一致，需与客服主任进行沟通、协调</span></p></td></tr></tbody></table><p><strong><span style="font-size:19px;font-family:'微软雅黑',sans-serif">&nbsp;&nbsp;&nbsp;&nbsp; </span></strong></p><table cellspacing="0" cellpadding="0" width="699"><tbody><tr style=";height:44px" class="firstRow"><td width="47" style="border: 1px solid windowtext; padding: 0px 7px;" height="44"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">编号</span></strong></p></td><td width="57" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="44"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">任务</span></strong></p></td><td width="595" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="44"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">目的与程序</span></strong></p></td></tr><tr style=";height:20px"><td width="47" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" height="20"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">7</span></strong></p></td><td width="57" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="20"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">讨论、确认</span></p></td><td width="95" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="20"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">What</span></p></td><td width="500" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="20"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">与主任确认星级笔试考核内容</span></p></td></tr><tr style=";height:18px"><td width="95" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="18"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">Why</span></p></td><td width="500" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="18"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">避免考题太偏、太难或其他不妥之处</span></p></td></tr><tr style=";height:18px"><td width="95" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="18"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">Who</span></p></td><td width="500" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="18"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">客服主任</span></p></td></tr><tr style=";height:17px"><td width="95" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="17"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">When</span></p></td><td width="500" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="17"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">收到星级考核报名的7天内</span></p></td></tr><tr style=";height:18px"><td width="95" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="18"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">How</span></p></td><td width="500" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="18"><p><span style="font-size:13px;font-family:'微软雅黑',sans-serif">有度或会议方式进行通知、反馈</span></p></td></tr><tr style=";height:88px"><td width="595" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="88"><p class="MsoListParagraph" style="margin-left:0;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">详细内容：</span></p><p class="MsoListParagraph" style="margin-left:24px"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">主任对试题有疑问，应在2个工作日内提出，客服质检需在收到反馈的当天内与骨干团队成员达成共识，并于收到星级考核报名的15天内完成最终版试题。</span></p></td></tr></tbody></table><p><strong><span style="font-size:19px;font-family:'微软雅黑',sans-serif">&nbsp;&nbsp;&nbsp;&nbsp; </span></strong></p><table cellspacing="0" cellpadding="0" width="699"><tbody><tr style=";height:22px" class="firstRow"><td width="47" style="border: 1px solid windowtext; padding: 0px 7px;" height="22"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">编号</span></strong></p></td><td width="57" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="22"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">任务</span></strong></p></td><td width="595" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="22"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">目的与程序</span></strong></p></td></tr><tr style=";height:11px"><td width="47" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" height="11"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">8</span></strong></p></td><td width="57" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="11"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">发布笔试通知</span></p></td><td width="87" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="11"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">What</span></p></td><td width="509" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="11"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">通知笔试考核时间</span></p></td></tr><tr style=";height:12px"><td width="87" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="12"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">Why</span></p></td><td width="509" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="12"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">与待考客服同步考核日期、考核时长</span></p></td></tr><tr style=";height:12px"><td width="87" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="12"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">Who</span></p></td><td width="509" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="12"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">客服质检</span></p></td></tr><tr style=";height:12px"><td width="87" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="12"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">When</span></p></td><td width="509" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="12"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">笔试考核开始前三天</span></p></td></tr><tr style=";height:12px"><td width="87" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="12"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:   '微软雅黑',sans-serif">How</span></p></td><td width="509" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 7px;" height="12"><p><span style="font-size:13px;font-family:'微软雅黑',sans-serif;background:white">有度</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">方式进行通知</span></p></td></tr><tr style=";height:52px"><td width="595" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="52"><p class="MsoListParagraph" style="margin-left:0;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">详细内容：</span></p><p class="MsoListParagraph" style="margin-left:0;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">笔试考核时长：<span style="background:white">二星：90m，三星、四星：100m</span></span></p><p class="MsoListParagraph" style="margin-left:0;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">考试时间安排：工作日下午</span></p></td></tr></tbody></table><p><strong><span style="font-size:19px;font-family:'微软雅黑',sans-serif">&nbsp;&nbsp; &nbsp;&nbsp;</span></strong></p><table cellspacing="0" cellpadding="0" width="699"><tbody><tr style=";height:45px" class="firstRow"><td width="47" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">编号</span></strong></p></td><td width="57" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">任务</span></strong></p></td><td width="595" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">目的与程序</span></strong></p></td></tr><tr><td width="47" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">9</span></strong></p></td><td width="57" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">实施考核</span></p></td><td width="83" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">What</span></p></td><td width="513" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">实施星级考核</span></p></td></tr><tr><td width="83" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">Why</span></p></td><td width="513" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">-</span></p></td></tr><tr><td width="83" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">Who</span></p></td><td width="513" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">客服质检</span></p></td></tr><tr><td width="83" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">When</span></p></td><td width="513" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">客服已报名参与考核时</span></p></td></tr><tr><td width="83" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">How</span></p></td><td width="513" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-size:13px;font-family:'微软雅黑',sans-serif">实操+笔试</span></p></td></tr><tr style=";height:123px"><td width="595" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="123"><p class="MsoListParagraph" style="margin-left:0;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">详细内容：</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">笔试考核</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.1</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">客服质检将考核试题有度发送给待考人</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.2</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">待考人仅能开打考试文档，并确保有度、电脑网路处于离线状态</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.3</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">考试过程中，参考人员不得以任何形式（如：电脑、手机、纸本资料）查询试题答案</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.4</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">客服质检应不定期巡视考试现场纪律，若发现违规作弊行为，中断考试并反馈至客服主任、客服主管</span></p><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2. &nbsp;</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">实操考核</span></p><p class="MsoListParagraph" style="margin-left:48px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.1<span style="font:9px 'Times New Roman'">&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">录音抽样方式：</span></p><p class="MsoListParagraph" style="margin-left:96px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.1.1<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">两周内抽5天，每天抽10通</span></p><p class="MsoListParagraph" style="margin-left:96px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.1.2<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">所抽取的录音样本，需涵盖以下情况</span></p><p class="MsoListParagraph" style="margin-left:96px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:Wingdings"><span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp; &nbsp; </span></span><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">高峰时段之录音</span></strong></p><p class="MsoListParagraph" style="margin-left:96px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:Wingdings"><span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp; &nbsp; </span></span><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">同一时段之不同录音</span></strong></p><p class="MsoListParagraph" style="margin-left:96px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:Wingdings"><span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp; &nbsp; </span></span><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">同一天的不同时段之录音</span></strong></p><p class="MsoListParagraph" style="margin-left:48px;text-align:justify;text-justify:inter-ideograph;background:white"><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">2.2<span style="font:9px 'Times New Roman'">&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">广告审核抽样方式：</span></p><p><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; </span></strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">&nbsp;2.2.1</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">新刊登固定在1000笔内抽检500笔，错误5笔合格；修改固定在500笔内抽检200笔，错误3笔合格；在2-3周内抽检完</span></p><p><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 2.2.2</span><span style="font-size:   13px;font-family:'微软雅黑',sans-serif;color:black">整层住家20%（100笔）、独立套房30%（150笔）、住宅50%（250笔）</span></p><p><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span style="background:white">&nbsp;</span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:#A6A6A6;background:white">注：新刊登按照各类型的总量来进行计算比例</span></p><p><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 2.2.3</span><span style="font-size:   13px;font-family:'微软雅黑',sans-serif;color:black">先详细统计各类型每天的审核量，然后按照比例大概抽检x笔即可，</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:#A6A6A6">（例如：整层住家审核两天，第一天总量为135笔，第二天审核量为100笔，那我就直接在这两内各抽检50笔）</span></p><p><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 2.2.4</span><span style="font-size:   13px;font-family:'微软雅黑',sans-serif;color:black">按审核顺序（日期）抽检；抽检时间段为第一天抽检x笔是上午时间段，第二天抽检x笔是下午时间段，以此类推</span></p><p style="text-align:justify;text-justify:inter-ideograph;background:white"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">&nbsp;</span></strong></p><p class="MsoListParagraph" style="margin-left:48px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">2.3<span style="font:9px 'Times New Roman'">&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">在线回复抽样方式：</span></p><p class="MsoListParagraph" style="margin-left:96px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.3.1<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">一周内抽50条</span></p><p class="MsoListParagraph" style="margin-left:96px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.3.2<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">所抽检的在线回复样本，需涵盖以下情况</span></p><p class="MsoListParagraph" style="margin-left:96px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:Wingdings"><span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp; &nbsp; </span></span><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">同时段不同时间之回复内容</span></strong></p><p class="MsoListParagraph" style="margin-left:48px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">2.4<span style="font:9px 'Times New Roman'">&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">高级事务抽样方式：</span></p><p class="MsoListParagraph" style="margin-left:96px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.4.1<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">两周内抽5天，每天抽10条</span></p><p class="MsoListParagraph" style="margin-left:96px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.4.2<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">所抽检的高级事务样本，需涵盖以下两种情况</span></p><p class="MsoListParagraph" style="margin-left:96px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:Wingdings"><span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp; &nbsp; </span></span><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">不同类型之处理内容</span></strong></p><p class="MsoListParagraph" style="margin-left:96px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:Wingdings"><span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp; &nbsp; </span></span><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">同一天不同时段之处理内容</span></strong></p></td></tr></tbody></table><p><strong><span style="font-size:19px;font-family:'微软雅黑',sans-serif">&nbsp;&nbsp;&nbsp; </span></strong></p><table cellspacing="0" cellpadding="0" width="0"><tbody><tr style=";height:45px" class="firstRow"><td width="47" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">编号</span></strong></p></td><td width="57" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">任务</span></strong></p></td><td width="595" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">目的与程序</span></strong></p></td></tr><tr><td width="47" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">10</span></strong></p></td><td width="57" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px; word-break: break-all;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">评分</span></p></td><td width="87" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">What</span></p></td><td width="509" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">针对考核结果进行评分</span></p></td></tr><tr><td width="87" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">Why</span></p></td><td width="509" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">评估考核是否通过</span></p></td></tr><tr><td width="87" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">Who</span></p></td><td width="509" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">客服质检</span></p></td></tr><tr style=";height:31px"><td width="87" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="31"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">When</span></p></td><td width="509" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="31"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">考核后的2个工作日内</span></p></td></tr><tr><td width="87" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">How</span></p></td><td width="509" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-size:13px;font-family:'微软雅黑',sans-serif">批改试卷与实操抽检</span></p></td></tr><tr style=";height:146px"><td width="595" colspan="2" valign="middle" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" height="146" align="left"><p class="MsoListParagraph" style="margin-left:0;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">详细内容：</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">笔试考核</span></p><p class="MsoListParagraph" style="margin-left:50px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.1<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">将客服填写的试卷打印出来</span></p><p class="MsoListParagraph" style="margin-left:50px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.2<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">参照试卷答案与评分要点进行评分作业</span></p><p class="MsoListParagraph" style="margin-left:50px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.3<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">笔试成绩于考核结束后的2个工作日内</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">实操考核</span></p><p class="MsoListParagraph" style="margin-left:50px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">2.1<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; &nbsp; </span></span><span style="font-size:13px;font-family:   '微软雅黑',sans-serif;color:black">电话录音评分标准</span></p><p><br></p><p><img src="/upload/question/20201225/1608883829955028.png" alt="image.png"><img src="/upload/question/20201225/1608884090889656.png" alt="image.png" style="font-family: 微软雅黑, sans-serif; font-size: 13px; text-align: justify;"><span style="font-family: 微软雅黑, sans-serif; font-size: 13px; text-align: justify;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-left:50px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">2.2<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; &nbsp; </span></span><span style="font-size:13px;font-family:   '微软雅黑',sans-serif;color:black">申诉、信箱、留言评分标准</span></p><p><img src="/upload/question/20201225/1608883558130608.png" alt="image.png"></p><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">&nbsp;</span></p><p class="MsoListParagraph" style="margin-left:50px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black"></span></p><p class="MsoListParagraph" style="margin-top: 0px;margin-bottom: 0px;margin-left: 50px;padding: 0px;white-space: normal;text-align: justify"><span style="font-size: 13px;font-family: 微软雅黑, sans-serif">2.3<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;font-size: 9px;line-height: normal;font-family: 'Times New Roman'">&nbsp;</span></span><span style="font-size: 13px; font-family: 微软雅黑, sans-serif;">在线客服回复评分标准</span></p><p class="MsoListParagraph" style="margin-top: 0px;margin-bottom: 0px;margin-left: 50px;padding: 0px;white-space: normal;text-align: justify"><span style="font-size: 13px;font-family: 微软雅黑, sans-serif"></span><br></p><p><br></p><p><img src="/upload/question/20201225/1608883702477491.png" alt="image.png"><img src="/upload/question/20201225/1608883752165525.png" alt="image.png" style="text-align: justify;"></p><p class="MsoListParagraph" style="margin-left:50px;text-align:justify;text-justify:inter-ideograph"><br></p><p class="MsoListParagraph" style="margin-left:50px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">2.4<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; &nbsp; </span></span><span style="font-size:13px;font-family:   '微软雅黑',sans-serif;color:black">高级事务评分标准</span></p><img src="/upload/question/20201225/1608883363987395.png" alt="image.png"><p style="text-align:justify;text-justify:inter-ideograph"><br></p></td></tr></tbody></table><p><strong><span style="font-size:19px;font-family:'微软雅黑',sans-serif">&nbsp;&nbsp;&nbsp; </span></strong></p><table cellspacing="0" cellpadding="0" width="699"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">编号</span></strong></p></td><td width="85" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">任务</span></strong></p></td><td width="558" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">目的与程序</span></strong></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">11</span></strong></p></td><td width="85" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">填写星级评定表与加薪单</span></p></td><td width="57" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">What</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">填写星级评定表、调薪单与权限异动表</span></p></td></tr><tr><td width="57" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">Why</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">走流程</span></p></td></tr><tr><td width="57" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">Who</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">客服质检</span></p></td></tr><tr style=";height:31px"><td width="57" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="31"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">When</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="31"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">星级考核通过，月底之前填写加薪单</span></p></td></tr><tr><td width="57" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">How</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-size:13px;font-family:'微软雅黑',sans-serif">填写-打印-签核</span></p></td></tr><tr style=";height:84px"><td width="558" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="84"><p class="MsoListParagraph" style="margin-left:0;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">详细内容：</span></p><p class="MsoListParagraph" style="margin-left:0;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1. </span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">填写</span><a href="file:///C:/Users/<USER>/AppData/Roaming/Microsoft/Word/%E6%9D%83%E9%99%90%E7%94%B3%E8%AF%B7%E8%A1%A8%E6%A0%BC%E3%80%81%E5%8A%A0%E8%96%AA%E8%A1%A8/%E6%98%9F%E7%BA%A7%E5%AE%A2%E6%9C%8D%E8%AF%84%E5%AE%9A%E8%A1%A8.doc"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">星级评定表</span></a><span style="font-size:   13px;font-family:'微软雅黑',sans-serif">、</span><a href="file:///C:/Users/<USER>/AppData/Roaming/Microsoft/Word/%E6%9D%83%E9%99%90%E7%94%B3%E8%AF%B7%E8%A1%A8%E6%A0%BC%E3%80%81%E5%8A%A0%E8%96%AA%E8%A1%A8/%E6%98%9F%E7%BA%A7%E5%AE%A2%E6%9C%8D%E8%B0%83%E8%96%AA%E8%A1%A8.doc"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">调薪单</span></a><span style="font-size:   13px;font-family:'微软雅黑',sans-serif">与</span><a href="file:///C:/Users/<USER>/AppData/Roaming/Microsoft/Word/%E6%9D%83%E9%99%90%E7%94%B3%E8%AF%B7%E8%A1%A8%E6%A0%BC%E3%80%81%E5%8A%A0%E8%96%AA%E8%A1%A8/%E8%8C%83%E6%9C%AC%E3%80%903018-3%E3%80%91%E8%AE%A1%E7%AE%97%E6%9C%BA%E6%9D%83%E9%99%90%E7%94%B3%E8%AF%B7%E5%BC%82%E5%8A%A8%E8%A1%A8.doc"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">权限异动表</span></a></p><p class="MsoListParagraph" style="margin-left:0;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">公司文档路径：192.168.8.29PublicFile591房屋交易网（台湾）【12】客服相关【0104】客服质检客服績效考核权限申请表格、加薪表（</span><a href="file:///C:/Users/<USER>/AppData/Roaming/Microsoft/Word/%E6%9D%83%E9%99%90%E7%94%B3%E8%AF%B7%E8%A1%A8%E6%A0%BC%E3%80%81%E5%8A%A0%E8%96%AA%E8%A1%A8/%E6%9D%83%E9%99%90%E7%94%B3%E8%AF%B7%E3%80%81%E5%8A%A0%E8%96%AA%E8%A1%A8%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9.docx"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">注意事项</span></a><span style="font-size:   13px;font-family:'微软雅黑',sans-serif">）</span></p><p style="text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2. </span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">星级评定表部门内部保留、调薪单给管理部-陈思柳、权限异动表给唐永红</span></p></td></tr></tbody></table><p><strong><span style="font-size:19px;font-family:'微软雅黑',sans-serif">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></strong></p><table cellspacing="0" cellpadding="0" width="699"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">编号</span></strong></p></td><td width="85" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">任务</span></strong></p></td><td width="558" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">目的与程序</span></strong></p></td></tr><tr style=";height:29px"><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" height="29"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">12</span></strong></p></td><td width="85" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="29"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">复查考核结果</span></p></td><td width="57" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="29"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">What</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="29"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">未通过考核人员的处理流程</span></p></td></tr><tr><td width="57" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">Why</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">确认评分是否有误，了解失分的原因</span></p></td></tr><tr style=";height:25px"><td width="57" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="25"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">Who</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="25"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">客服主任</span></p></td></tr><tr><td width="57" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">When</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">第一次考核未通过</span></p></td></tr><tr><td width="57" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">How</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-size:13px;font-family:'微软雅黑',sans-serif">查阅试卷、与质检确认评分细节</span></p></td></tr><tr style=";height:85px"><td width="558" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="85"><p class="MsoListParagraph" style="margin-left:0;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">详细内容：</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">1.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">主任与客服质检调阅考核评分记录</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">针对有疑问的地方，与客服质检或骨干成员进行讨论</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">3.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">若最终结果确定为不通过，则需分析客服考核不通过的原因，评估是否补考</span></p></td></tr></tbody></table><p><strong><span style="font-size:19px;font-family:'微软雅黑',sans-serif">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></strong></p><table cellspacing="0" cellpadding="0" width="699"><tbody><tr style=";height:45px" class="firstRow"><td width="57" style="border: 1px solid windowtext; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">编号</span></strong></p></td><td width="85" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">任务</span></strong></p></td><td width="558" colspan="2" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" height="45"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">目的与程序</span></strong></p></td></tr><tr><td width="57" rowspan="6" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><strong><span style="font-size:13px;font-family:'微软雅黑',sans-serif">13</span></strong></p></td><td width="85" rowspan="6" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0;text-align:center"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">补考流程</span></p></td><td width="57" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">What</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">星级考核补考规范</span></p></td></tr><tr><td width="57" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">Why</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">明确补考的规则与流程</span></p></td></tr><tr style=";height:30px"><td width="57" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="30"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">Who</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="30"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">客服主任、客服质检、客服共同协商</span></p></td></tr><tr><td width="57" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">When</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">确定星级考核成绩後的两个工作日内</span></p></td></tr><tr><td width="57" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p class="MsoListParagraph" style="margin-left:0"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">How</span></p></td><td width="501" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"><p><span style="font-size:13px;font-family:'微软雅黑',sans-serif">沟通协商</span></p></td></tr><tr style=";height:160px"><td width="558" colspan="2" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="160"><p class="MsoListParagraph" style="margin-left:0;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">详细内容：</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">1.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; </span></span><span style="font-size:13px;font-family:   '微软雅黑',sans-serif;color:black">当月考试笔试与实操中若有一项未通过，可选择补考一次，若两项均未通过，则需要重新报名考试</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">1.</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">1.1</span><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">若需选择补考，可在公布考核成绩后的两个工作日内，与客服主任申请补考</span></p><p class="MsoListParagraph" style="margin-left:24px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">2.<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; </span></span><span style="font-size:13px;font-family:   '微软雅黑',sans-serif;color:black">客服主任根据本次考核结果分析是否立即补考</span></p><p class="MsoListParagraph" style="margin-left:48px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">2.1<span style="font:9px 'Times New Roman'">&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif">不补考则可评估重新申请考核（笔试+实操）的时间</span></p><p class="MsoListParagraph" style="margin-left:48px;text-align:justify;text-justify:inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">2.2<span style="font:9px 'Times New Roman'">&nbsp; </span></span><span style="font-size:13px;font-family:'微软雅黑',sans-serif;color:black">需要补考，则由客服质检安排於次月底之前完成补考（未考核通过那项）</span></p><p style="margin-left:24px;text-align:justify;text-justify:   inter-ideograph"><span style="font-size:13px;font-family:'微软雅黑',sans-serif">注：若补考仍未通过，至少需间隔<span style="color:   black">2</span><span style="color:black">个月才</span>能重新报考星级考核</span></p></td></tr></tbody></table><p><strong><span style="font-size:19px;font-family:'微软雅黑',sans-serif">&nbsp;</span></strong></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li></ul> <h3 class="more-answer">我要補充</h3> <div style=""><div id="editor_37672326431897150" class="edui-default" style=""><div id="edui1" class="edui-editor  edui-default" style="width: 100%; z-index: 999;"><div id="edui1_toolbarbox" class="edui-editor-toolbarbox edui-default"><div id="edui1_toolbarboxouter" class="edui-editor-toolbarboxouter edui-default"><div class="edui-editor-toolbarboxinner edui-default"><div id="edui2" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui2&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui3" class="edui-box edui-button edui-for-fullscreen edui-default"><div id="edui3_state" onmousedown="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui3_body" unselectable="on" title="全屏" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui3&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui3&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui4" class="edui-box edui-button edui-for-source edui-default"><div id="edui4_state" onmousedown="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui4_body" unselectable="on" title="源代码" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui4&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui4&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui5" class="edui-box edui-button edui-for-undo edui-default"><div id="edui5_state" onmousedown="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui5_body" unselectable="on" title="撤销" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui5&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui5&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui6" class="edui-box edui-button edui-for-redo edui-default"><div id="edui6_state" onmousedown="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui6_body" unselectable="on" title="重做" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui6&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui6&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui7" class="edui-box edui-button edui-for-simpleupload edui-default"><div id="edui7_state" onmousedown="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui7_body" unselectable="on" title="单图上传" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui7&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui7&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui8" class="edui-box edui-button edui-for-imageleft edui-default"><div id="edui8_state" onmousedown="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui8_body" unselectable="on" title="左浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui8&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui8&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui9" class="edui-box edui-button edui-for-imageright edui-default"><div id="edui9_state" onmousedown="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui9_body" unselectable="on" title="右浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui9&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui9&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui10" class="edui-box edui-button edui-for-imagecenter edui-default"><div id="edui10_state" onmousedown="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui10_body" unselectable="on" title="居中" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui10&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui10&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui11" class="edui-box edui-separator  edui-default"></div><div id="edui12" class="edui-box edui-button edui-for-date edui-default"><div id="edui12_state" onmousedown="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui12_body" unselectable="on" title="日期" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui12&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui12&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui13" class="edui-box edui-button edui-for-time edui-default"><div id="edui13_state" onmousedown="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui13_body" unselectable="on" title="时间" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui13&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui13&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui14" class="edui-box edui-button edui-for-horizontal edui-default"><div id="edui14_state" onmousedown="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui14_body" unselectable="on" title="分隔线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui14&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui14&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui15" class="edui-box edui-separator  edui-default"></div><div id="edui16" class="edui-box edui-splitbutton edui-for-inserttable edui-default"><div title="插入表格" id="edui16_state" onmousedown="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui16_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onArrowClick();"></div></div></div></div><div id="edui19" class="edui-box edui-button edui-for-deletetable edui-default"><div id="edui19_state" onmousedown="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui19_body" unselectable="on" title="删除表格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui19&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui19&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui20" class="edui-box edui-button edui-for-insertrow edui-default"><div id="edui20_state" onmousedown="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui20_body" unselectable="on" title="前插入行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui20&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui20&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui21" class="edui-box edui-button edui-for-insertcol edui-default"><div id="edui21_state" onmousedown="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui21_body" unselectable="on" title="前插入列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui21&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui21&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui22" class="edui-box edui-button edui-for-mergeright edui-default"><div id="edui22_state" onmousedown="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui22_body" unselectable="on" title="右合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui22&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui22&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui23" class="edui-box edui-button edui-for-mergedown edui-default"><div id="edui23_state" onmousedown="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui23_body" unselectable="on" title="下合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui23&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui23&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui24" class="edui-box edui-button edui-for-deleterow edui-default"><div id="edui24_state" onmousedown="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui24_body" unselectable="on" title="删除行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui24&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui24&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui25" class="edui-box edui-button edui-for-deletecol edui-default"><div id="edui25_state" onmousedown="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui25_body" unselectable="on" title="删除列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui25&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui25&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui26" class="edui-box edui-button edui-for-splittorows edui-default"><div id="edui26_state" onmousedown="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui26_body" unselectable="on" title="拆分成行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui26&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui26&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui27" class="edui-box edui-button edui-for-splittocols edui-default"><div id="edui27_state" onmousedown="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui27_body" unselectable="on" title="拆分成列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui27&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui27&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui28" class="edui-box edui-button edui-for-splittocells edui-default"><div id="edui28_state" onmousedown="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui28_body" unselectable="on" title="完全拆分单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui28&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui28&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui29" class="edui-box edui-button edui-for-mergecells edui-default"><div id="edui29_state" onmousedown="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui29_body" unselectable="on" title="合并多个单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui29&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui29&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui36" class="edui-box edui-button edui-for-edittable edui-default"><div id="edui36_state" onmousedown="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui36_body" unselectable="on" title="表格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui36&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui36&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui41" class="edui-box edui-button edui-for-edittd edui-default"><div id="edui41_state" onmousedown="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui41_body" unselectable="on" title="单元格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui41&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui41&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui42" class="edui-box edui-separator  edui-default"></div><div id="edui43" class="edui-box edui-menubutton edui-for-lineheight edui-default"><div title="行间距" id="edui43_state" onmousedown="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui43_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onArrowClick();"></div></div></div></div><div id="edui52" class="edui-box edui-menubutton edui-for-rowspacingtop edui-default"><div title="段前距" id="edui52_state" onmousedown="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui52_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onArrowClick();"></div></div></div></div><div id="edui59" class="edui-box edui-menubutton edui-for-rowspacingbottom edui-default"><div title="段后距" id="edui59_state" onmousedown="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui59_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onArrowClick();"></div></div></div></div></div><div id="edui66" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui66&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui67" class="edui-box edui-combox edui-for-fontfamily edui-default"><div title="字体" id="edui67_state" onmousedown="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui67_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onButtonClick(event, this);">字体</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onArrowClick();"></div></div></div></div><div id="edui80" class="edui-box edui-combox edui-for-customstyle edui-default"><div title="自定义标题" id="edui80_state" onmousedown="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui80_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onButtonClick(event, this);">自定义标题</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onArrowClick();"></div></div></div></div><div id="edui86" class="edui-box edui-combox edui-for-paragraph edui-default"><div title="段落格式" id="edui86_state" onmousedown="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui86_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onButtonClick(event, this);">段落格式</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onArrowClick();"></div></div></div></div><div id="edui95" class="edui-box edui-combox edui-for-fontsize edui-default"><div title="字号" id="edui95_state" onmousedown="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui95_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onButtonClick(event, this);">字号</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onArrowClick();"></div></div></div></div><div id="edui110" class="edui-box edui-button edui-for-link edui-default"><div id="edui110_state" onmousedown="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui110_body" unselectable="on" title="超链接" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui110&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui110&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui111" class="edui-box edui-button edui-for-indent edui-default"><div id="edui111_state" onmousedown="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui111_body" unselectable="on" title="首行缩进" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui111&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui111&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui112" class="edui-box edui-button edui-for-bold edui-default"><div id="edui112_state" onmousedown="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui112_body" unselectable="on" title="加粗" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui112&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui112&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui113" class="edui-box edui-splitbutton edui-for-forecolor edui-default edui-colorbutton"><div title="字体颜色" id="edui113_state" onmousedown="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui113_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui113_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onArrowClick();"></div></div></div></div><div id="edui116" class="edui-box edui-splitbutton edui-for-backcolor edui-default edui-colorbutton"><div title="背景色" id="edui116_state" onmousedown="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui116_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui116_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onArrowClick();"></div></div></div></div><div id="edui119" class="edui-box edui-button edui-for-italic edui-default"><div id="edui119_state" onmousedown="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui119_body" unselectable="on" title="斜体" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui119&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui119&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui120" class="edui-box edui-button edui-for-underline edui-default"><div id="edui120_state" onmousedown="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui120_body" unselectable="on" title="下划线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui120&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui120&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui121" class="edui-box edui-button edui-for-strikethrough edui-default"><div id="edui121_state" onmousedown="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui121_body" unselectable="on" title="删除线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui121&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui121&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui122" class="edui-box edui-button edui-for-superscript edui-default"><div id="edui122_state" onmousedown="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui122_body" unselectable="on" title="上标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui122&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui122&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui123" class="edui-box edui-button edui-for-subscript edui-default"><div id="edui123_state" onmousedown="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui123_body" unselectable="on" title="下标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui123&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui123&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui124" class="edui-box edui-button edui-for-removeformat edui-default"><div id="edui124_state" onmousedown="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui124_body" unselectable="on" title="清除格式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui124&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui124&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui125" class="edui-box edui-button edui-for-blockquote edui-default"><div id="edui125_state" onmousedown="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui125_body" unselectable="on" title="引用" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui125&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui125&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui126" class="edui-box edui-button edui-for-pasteplain edui-default"><div id="edui126_state" onmousedown="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui126_body" unselectable="on" title="纯文本粘贴模式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui126&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui126&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui127" class="edui-box edui-menubutton edui-for-insertorderedlist edui-default"><div title="有序列表" id="edui127_state" onmousedown="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui127_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onArrowClick();"></div></div></div></div><div id="edui140" class="edui-box edui-menubutton edui-for-insertunorderedlist edui-default"><div title="无序列表" id="edui140_state" onmousedown="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui140_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onArrowClick();"></div></div></div></div><div id="edui147" class="edui-box edui-button edui-for-cleardoc edui-default"><div id="edui147_state" onmousedown="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui147_body" unselectable="on" title="清空文档" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui147&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui147&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div></div></div></div><div id="edui1_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;"><div id="edui1_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui1&quot;].showWordImageDialog();">点击上传</div><div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui1&quot;].hideToolbarMsg();">x</div><div id="edui1_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div><div style="height:0;overflow:hidden;clear:both;" class="edui-default"></div></div><div id="edui1_message_holder" class="edui-editor-messageholder edui-default"></div></div><div id="edui1_iframeholder" class="edui-editor-iframeholder edui-default" style="width: 100%; height: 300px; z-index: 999; overflow: hidden;"><iframe id="ueditor_0" width="100%" height="100%" frameborder="0" src="javascript:void(function(){document.open();document.write(&quot;<!DOCTYPE html><html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}
body{margin:8px;font-family:sans-serif;font-size:13px;}p{margin:5px 0;}</style><link rel='stylesheet' type='text/css' href='/static/ueditor/themes/iframe.css'/></head><body class='view' ></body><script type='text/javascript'  id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant0'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);</script></html>&quot;);document.close();}())"></iframe></div><div id="edui1_bottombar" class="edui-editor-bottomContainer edui-default"><table class="edui-default"><tbody class="edui-default"><tr class="edui-default"><td id="edui1_elementpath" class="edui-editor-bottombar edui-default"><div class="edui-editor-breadcrumb">元素路径:</div></td><td id="edui1_wordcount" class="edui-editor-wordcount edui-default">字数统计</td><td id="edui1_scale" class="edui-editor-scale edui-default" style="display: none;"><div class="edui-editor-icon edui-default"></div></td></tr></tbody></table></div><div id="edui1_scalelayer" class="edui-default"></div></div></div> <div class="edit-btn"><button type="button" class="button">提交回答</button> <label data-v-0fe26aab="" for="commonCheckbox" class="checkbox"><input data-v-0fe26aab="" type="checkbox" id="commonCheckbox" name="commonCheckbox"> <i data-v-0fe26aab=""></i> 需要彈窗，請勾選</label></div></div></div> <div class="index-right"><div class="right-bk"><h3 class="com-title"><a href="javascript:;">我的最爱</a></h3> <div class="item-box clearfix"><span>錯誤，請您登入後再操作！</span></div></div> <div class="hotTag right-bk"><h3 class="com-title">热门标签</h3> <div class="hotTag-box item-box clearfix"><p>無數據</p></div></div> <div class="right-bk rank"><h3 class="com-title"><a href="javascript:;">冠軍排行榜</a></h3> <ul><li><div><span class="number">1</span> <img src="/static/timg.jpeg"> <span>王菊芳</span></div> <span class="score">612分</span></li><li><div><span class="number">2</span> <img src="/upload/head/10177.gif?time=1556271256"> <span>黃雪靜</span></div> <span class="score">258分</span></li><li><div><span class="number">3</span> <img src="/static/timg.jpeg"> <span>田霞</span></div> <span class="score">240分</span></li><li><div><span class="number">4</span> <img src="/static/timg.jpeg"> <span>曹超雲</span></div> <span class="score">168分</span></li><li><div><span class="number">5</span> <img src="/static/timg.jpeg"> <span>文宇</span></div> <span class="score">96分</span></li><li><div><span class="number">6</span> <img src="/upload/head/10217.png?time=1548059679"> <span>黃興興</span></div> <span class="score">78分</span></li><li><div><span class="number">7</span> <img src="/static/timg.jpeg"> <span>黃佳麗</span></div> <span class="score">62分</span></li><li><div><span class="number">8</span> <img src="/static/timg.jpeg"> <span>马泽鹏</span></div> <span class="score">46分</span></li><li><div><span class="number">9</span> <img src="/static/timg.jpeg"> <span>陳敏南</span></div> <span class="score">40分</span></li><li><div><span class="number">10</span> <img src="/static/timg.jpeg"> <span>Cs-刘婷</span></div> <span class="score">10分</span></li><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></ul></div> <div class="activeUser right-bk"><h3 class="com-title">活跃用户</h3> <div class="activeUser-box"><a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a></div></div> <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFQAAABUCAMAAAArteDzAAAAt1BMVEWjo6MAAAAAAACUlJRcXFySkpIAAABWVlYSEhKioqKdnZ2ZmZkAAACOjo4AAAAAAAAtLS0AAAChoaFISEhBQUEcHByIiIh9fX1nZ2c3NzcAAAAAAABsbGyDg4NxcXEAAAAAAADHx8f7+/v9/f3CwsL+/v78/Pz6+vq5ubm1tbUAAAAAAAAAAAAAAAC+vr7MzMz39/fz8/OwsLD19fXl5eXf39/v7+/r6+vZ2dn9/f3U1NSpqan///8GohFlAAAAPHRSTlNpADNgSF8xRjZpZWMuXB8KOyNnQUA4WVRMPRYNTlZPEwaI8PaD+/TtenYrKhsZfo/j1XLbtqrLwqH4mG11d9WNAAAEt0lEQVRYw5TT2VLjMBAFUA2JLEIcyUu8xNlcwASyURBqHqbg/79r5G7J7YytGPdLnnLq9lWb/eobtT3L8Oh7d3eefwzleat6/8Jug4U8aO16vIMs1FCUxNAzYsv1QnCHokvpG9Hh+nI5CCVyMOtGVZO8T4J5HAsxHgsRx/MguW+y6odoWWiSQK1djwDYskX5E3QZ1mQQjx0TBzUbLvvRrY2ZLCCja8QisWG3PWh58k1KIldZOuN8VA3nszRb1axJ65/KW6iSnlnckpu1BlHE4RpebwxrSvCkcqPK1JmYLvMMIrZGu1mO3SamWOVA0aSYuQ45co2Om1NYUgkls9FmVpPT6UMUTWCi6GE6tWyGzZLaRkuJ5hy7TA2pwQm7momGkU2h2zmqsuxAT00zq0kSm65ls4Z6IpTuk8w85YZkzkGWp3mt2nsldNkwV2A2UzrSgroidXmNliGcxgLMGcaMWM9EEHZWqQv4e1heoQUUGggyKeatsFYVAdRaNFEFyycC+kST/WhArXoVCRSgGqiEQuE7InOAWn1bUKtElF4poFsis1+1lxXQWzEKKqqb5x199vfKN7oAisrqoIt6+ak2B6hTU8ACoxpU1q+Uccct9V0Wz+xbSURVHTSfDSyUap3lNqoCtKgbXXNafmABfG1bLQAN4elbQYdHxQMIK1R5eKO2UdY9+4/dx8WlYqt4q57SKGyfjPXcCrp/+f7+frnciqqFBPbXqPTM9psbje4ftQmqu9UN7u9JjR4qfQ7P5Az6qk1U946o+FTzyjr8YlipsNtHneYOSbca4f4CS2VbW+mKtm+bNI975/4rU+qWnc1Bud/+Dc3dn9+ovrrfH4/qzKStNHVU+vYE1tMze3aqWGpqSpUMTj92VYomSpT51VlqDOfPjhblnejzU/0+dAW7t06UG/TI/OpHWHTyv/kXlPcLfQPQRUudICoqzWeeRUdt1Lb4/sXsXN6t2kZHBvXYXTVjQjvMT9aYr0/zbp3oGLh/zZm7asNAEEUXTCq7cSM1tmFlh0AaYfBDMfn/7wrMRByZ0Wi8auQpVVxmF+3svWenRLNqntNzneXrz8kXnVh+/pW6pTSq+nnyl79xRNG0dVPV7Ihu3F+qVc17Gqs7qqO/1HZEFM1LSo6qVB4V3XJMGdG+JnVBlTHdH1MGStUPFDSvKUWq7WCgVP1AYfTVjL6Mpl/X/14ZfXU/+hjSOzZVT02XpqvTk8GW7vohzXXCpqroI0XViajZ0m8uvqGVyM2xaVNcj+b4lbETXHxc0QdcZGmp8+OKxkzo+tdzRNeyeswEticwvKHxxfZg0MRLzTRo6qUwaEMruafV4kb3WMmB6aXVctOrjWJ6seeyqzPt+UeNPTdBolrNChKrygQJaRXnVxx5cHzSqA1nNaoFmjXhzMRINqAgRuriiZE28Eo2LQm8mk0JvE40lxT9ajTXFE009yACqjFEUE0ggoc7IBMx7lAyAe7wwQwMJQYzylAAMz5CIqFPISRoDwgpgl0HQV0u7IJLAbtiLAdBswVBM1guAogx6zMAMUadKutTSQd1BlA24qc+lI3xsSW9Bh8vA7rLkfwSjwflzxzLP8i88nT0Po9c85/j/gCd9anl0qbQowAAAABJRU5ErkJggg==" class="to-top"></div></div> <!----> <!----> <!----> <div class="page-cnt footer"><p>T5客服知识库<span>|</span>zsk.591.com.tw</p></div></div></div><script type="text/javascript" src="/js/manifest.f192b.js"></script><script type="text/javascript" src="/js/vendor.17570.js"></script><script type="text/javascript" src="/js/app.58254.js"></script><div id="edui_fixedlayer" class="edui-default" style="position: fixed; left: 0px; top: 0px; width: 0px; height: 0px;"><div id="edui148" class="edui-popup  edui-bubble edui-default" onmousedown="return false;" style="display: none;"> <div id="edui148_body" class="edui-popup-body edui-default"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank" class="edui-default"></iframe> <div class="edui-shadow edui-default"></div> <div id="edui148_content" class="edui-popup-content edui-default">  </div> </div></div></div></body></html>