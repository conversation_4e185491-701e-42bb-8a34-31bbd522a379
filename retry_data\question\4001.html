<html class=" "><head><meta charset="utf-8"><title>591客服知識庫</title><link rel="stylesheet" href="/static/font/font-awesome.min.css"><link href="/css/app.e5aa4.css" rel="stylesheet"><script type="text/javascript" charset="utf-8" async="" src="/js/questionDetail.d0a6b.js"></script><style type="text/css">.que-detail-page .index-left .button{width:74px;height:32px;line-height:32px;font-size:13px;margin-right:10px}.que-detail-page .index-left .button:hover{color:#fff}.que-detail-page .detail-box{padding:20px 30px;border:1px solid #e5e5e5;border-radius:4px}.que-detail-page .detail-box h3{font-size:18px;color:#333;margin-bottom:10px}.que-detail-page .detail-box h3 span{color:#f77d24}.que-detail-page .detail-box .add-fav{cursor:pointer}.que-detail-page .d-b-menu{margin:15px 0 25px;font-size:13px;color:#999}.que-detail-page .d-b-menu b{display:inline-block;font-weight:400;margin:0 10px}.que-detail-page .d-b-menu .r{float:right;color:#f77d24}.que-detail-page .d-b-menu .r .fa{margin-right:5px;font-size:16px}.que-detail-page .d-b-cnt{margin-bottom:25px;font-size:13px;color:#666}.que-detail-page .submit-ask{display:block;margin:32px auto 0}.que-detail-page .reply-list{margin:30px 0 10px;overflow:hidden}.que-detail-page .reply-list .reply-item{padding:15px 30px 25px;margin-bottom:30px;border-radius:4px;border:1px solid #e5e5e5}.que-detail-page .reply-list .reply-item h4{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;line-height:50px;font-size:13px}.que-detail-page .reply-list .reply-item h4 img{width:40px;height:40px;border-radius:50%}.que-detail-page .reply-list .reply-item h4 .time{color:#999}.que-detail-page .reply-list .reply-item h4 .button{-ms-flex-item-align:center;align-self:center}.que-detail-page .reply-list .reply-item h4 .delete{color:#666;cursor:pointer}.que-detail-page .reply-list .reply-item .reply-detail{padding:20px 0 30px}.que-detail-page .more-answer{font-size:18px;color:#666;margin-bottom:5px}.que-detail-page .editor-content table{width:100%;border:1px solid #ddd}.que-detail-page .editor-content table td,.que-detail-page .editor-content table th{border:1px solid #ddd;padding:2px 8px;line-height:2}.que-detail-page .editor-content img{max-width:100%;vertical-align:text-bottom}.que-detail-page .edit-btn{text-align:center;margin-top:20px}.que-detail-page .edit-btn .edit-cancle{background:#aaa}</style><style type="text/css">.head-info{line-height:26px;background:#f5f5f5;font-size:13px;color:#999}.head-info a{margin-left:20px;color:#333}.head-search{margin-top:22px}.head-search img{float:left}.head-search .srh-box{float:left;margin:20px 0 0 20px}.head-search .srh-box .srh-input{float:left;width:260px;height:38px;line-height:38px;border:2px solid #37a508;border-radius:4px 0 0 4px}.head-search .srh-box a,.head-search .srh-box button{float:left;width:80px;height:38px;line-height:36px;font-size:16px;color:#fff}.head-search .srh-box .srh-btn{border-radius:0 4px 4px 0}.head-search .srh-box .ask-btn{margin-left:10px}.head-nav{float:left;margin:20px 0 0 60px;color:#fff;font-size:16px;line-height:36px}.head-nav a{display:block;height:100%;color:#333}.head-nav li{position:relative;float:left;margin-right:35px}.head-nav .active a,.head-nav li:hover a{color:#30b940}.head-nav .active:after,.head-nav li:hover:after{display:block;content:"";height:3px;width:100%;background:#30b940}.head-pic{width:100%;height:90px;margin-top:20px;background:url(/static/banner.png) top no-repeat}</style><style type="text/css">.top-breadcrumb{height:20px;margin-top:10px;margin-bottom:40px;font-size:13px;color:#666}.top-breadcrumb .t-b-icon{color:#30b940}.top-breadcrumb span{margin-left:8px}.top-breadcrumb span .fa{font-size:16px;margin-left:8px}.top-breadcrumb span:first-child{margin-left:0}.top-breadcrumb .now{color:#30b940}</style><script type="text/javascript" src="/static/ueditor/ueditor.config.js?20210319" id="configScriptTag"></script><script type="text/javascript" src="/static/ueditor/ueditor.all.js?20210319" id="editorScriptTag"></script><style type="text/css">.checkbox[data-v-0fe26aab]{word-spacing:4px;vertical-align:bottom;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.checkbox i[data-v-0fe26aab]{display:inline-block;width:14px;height:14px;border-radius:2px;border:1px solid #ccc;background:#fff;position:relative;top:1px}.checkbox input[data-v-0fe26aab]{opacity:0;width:0;height:0;margin:0}.checkbox input:checked+i[data-v-0fe26aab]{background:#30b940;border-color:#30b940}.checkbox input:checked+i[data-v-0fe26aab]:before{content:"";position:absolute;left:2px;top:1px;width:11px;height:6px;border-bottom:2px solid #fff;border-left:2px solid #fff;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}</style><style type="text/css">.popbox{position:fixed;top:50%;left:50%;z-index:1011;background:#fff;border-radius:6px;overflow:hidden}.popbox h3{font-size:16px;line-height:40px;padding:0 20px}.popbox h3 span{float:right;color:#666;font-size:13px;cursor:pointer}.popbox .pb-cnt{padding:0 20px;font-size:13px}.popbox .pb-btn-area{position:absolute;bottom:0;left:0;z-index:1;width:100%;text-align:right;padding:0 20px 20px 0}.popbox .pb-btn-area button{height:32px;line-height:32px;border:0;background:#fff;cursor:pointer;border-radius:6px;margin-left:15px;padding:0 15px}.popbox .pb-btn-area button.pb-btn-ok{background:#daf3cf;color:#279005}.popbox .pb-btn-area button.pb-btn-ok:hover{background:#279005;color:#fff}</style><style type="text/css">.shadow{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1010;background:#000}</style><style type="text/css">.footer{margin-top:70px;margin-bottom:20px;padding-top:20px;text-align:center;color:#999;border-top:1px solid #f5f5f5}.footer span{margin:0 10px}</style><script src="/static/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript" defer="defer"></script><link href="/static/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet"><script src="/static/ueditor/third-party/codemirror/codemirror.js" type="text/javascript" defer="defer"></script><link rel="stylesheet" type="text/css" href="/static/ueditor/third-party/codemirror/codemirror.css"><script src="/static/ueditor/third-party/zeroclipboard/ZeroClipboard.js" type="text/javascript" defer="defer"></script><style type="text/css">.activeUser,.favorite,.hotTag,.rank{margin-bottom:40px}</style><style type="text/css">.rank .rank-page{height:60px;line-height:60px;font-size:18px;margin-bottom:20px;text-align:center;background:#fafafa;border-radius:5px}.rank ul{padding:0 20px}.rank li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;border-bottom:1px dotted #f5f5f5;line-height:50px}.rank li .flag{position:relative;display:inline-block;width:20px;height:30px;background:#476472;vertical-align:middle;margin-right:12px;color:#fff;text-align:center;line-height:20px;font-size:13px;font-style:normal}.rank li .flag:after{content:"";position:absolute;bottom:-4px;left:-4px;border:14px solid transparent;border-bottom-color:#fff}.rank li:first-child .flag{background:#fb6757}.rank li:nth-child(2) .flag{background:#fac525}.rank li:nth-child(3) .flag{background:#00a9fb}.rank li img{width:36px;height:36px;border-radius:50%;margin-right:12px}</style><style type="text/css">.activeUser .activeUser-box a{display:inline-block;width:64px;height:64px;border-radius:50%;overflow:hidden;margin:0 6px 12px}</style><style type="text/css">.to-top{display:none;position:fixed;bottom:150px;z-index:5;width:50px;height:50px;cursor:pointer}</style></head><body style=""><div id="app" style=""><div class="que-detail-page" style=""><div class="wiki-hd"><div class="head-info"><p class="page-cnt">
      您好，欢迎来到591客服知识库
      <a href="https://www.591.com.tw/admin.php?new_url=https://zsk.591.com.tw">[请登入]</a> <!----></p></div> <div class="page-cnt head-search clearfix"><a href="/" class="router-link-active"><img src="/static/logo.png"></a> <ul class="head-nav clearfix"><li class="router-link-active"><a href="/">591房屋交易网</a></li> <li class="active"><a href="/life">客服充电站</a></li> <li class=""><a href="/notification">通知事项</a></li></ul> <div class="srh-box"><input type="text" placeholder="请输入搜索内容" class="srh-input"> <button type="button" class="button srh-btn">搜索</button> <a href="/ask/3" class="button button-orange ask-btn">發佈</a></div></div> <div class="head-pic"></div></div> <div class="top-breadcrumb com-width"><span><a href="/life/collection/1254" class="">培訓分享<i class="fa fa-angle-double-right"></i></a></span><span><a href="/life/collection/1254/1590" class="">團隊分享/培訓<i class="fa fa-angle-double-right"></i></a></span><span><strong class="now">问题详情</strong></span></div> <div class="com-width clearfix" style=""><div class="index-left" style=""><div class="detail-box"><h3><!---->新建案業務知識培訓2020.3.18</h3> <p class="clearfix d-b-menu"><span class="l">
            王菊芳&nbsp;&nbsp;&nbsp;浏览27次&nbsp;&nbsp;&nbsp;2020-03-20 09:48:11
          </span> <span class="r"><span class="add-fav"><i class="fa fa-heart"></i>加入最爱</span></span></p> <div class="d-b-cnt editor-content big-img-parent"><div><p><span style="font-family: 微软雅黑, Microsoft YaHei;">1、認識新建案</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">2、591新建案介紹</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">3、其他周邊產品（720、賞屋團、新聞、廣告）</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">4、來電處理流程</span><br></p></div></div> <div class="d-b-button"><a href="/edit/3/4001" class="button">编辑</a> <button class="button button-orange">关闭提问</button></div></div> <div class="hr-line"></div> <ul class="reply-list big-img-parent"><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-03-20 15:03:38</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">一、認識新建案</span></strong></p><p><span style="font-size: 16px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei;"><br></span></strong></span></p><p><span style="font-size: 16px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">1、新建案流程</span></strong></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">1）土地取得的方式：<span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; letter-spacing: 2px;"></span></span><span style="color: #000000; font-family: 微软雅黑; letter-spacing: 2px; font-size: 14px;">自行開發；中人介紹；標售取得</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="letter-spacing: 2px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;"><br></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="letter-spacing: 2px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">2）規劃設計：</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; letter-spacing: 2px; color: #000000; font-size: 14px;">a、規劃平面圖、外觀圖</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; letter-spacing: 2px; color: #000000; font-size: 14px;">b、樓層數、配坪、房數規劃確定</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; letter-spacing: 2px; color: #000000; font-size: 14px;">c、規劃水電圖等</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="letter-spacing: 2px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;"></span><br></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; letter-spacing: 2px;">3）申請建照：<br></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="color: #000000; font-size: 16px; font-family: 微软雅黑; letter-spacing: 2px;">a.申請建照執照&nbsp;&nbsp; 申請拆除執照</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="color: #000000; font-size: 16px; font-family: 微软雅黑; letter-spacing: 2px;">b.製作</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="color: #000000; font-size: 16px; font-family: 微软雅黑; letter-spacing: 2px;">c.建照執照核發</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><br><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; letter-spacing: 2px;"></span><br></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; letter-spacing: 2px;">4）營造工程：<br></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; letter-spacing: 2px;"></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; letter-spacing: 2px; font-size: 14px; color: #000000;">a.施工計畫</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; letter-spacing: 2px; font-size: 14px; color: #000000;">b.開工</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; letter-spacing: 2px; font-size: 14px; color: #000000;">c.放樣</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; letter-spacing: 2px; font-size: 14px; color: #000000;">d.查驗</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; letter-spacing: 2px; font-size: 14px; color: #000000;">e.變更設計、變更起造人</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; letter-spacing: 2px; font-size: 14px; color: #000000;">f.完工</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><br><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; letter-spacing: 2px;"></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; letter-spacing: 2px;">5）申請使照</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; letter-spacing: 2px;">a.申請使用執照</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; letter-spacing: 2px;">b.製作副本</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; letter-spacing: 2px;">c.使用使照核發</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; letter-spacing: 2px;"><br></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; letter-spacing: 2px;">6）申請水電</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; letter-spacing: 2px;"><br></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; letter-spacing: 2px;">7）完工交屋</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; letter-spacing: 2px;">a.建物保存登記</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; letter-spacing: 2px;">b.申請建築改良所有權狀<br></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; letter-spacing: 2px;"><br></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size: 16px; color: #000000;"><strong><span style="font-size: 16px; font-family: 微软雅黑, Microsoft YaHei; letter-spacing: 2px;">2、建商開發到銷售有哪些角色</span></strong></span><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; letter-spacing: 2px;"><br></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; letter-spacing: 2px;">1）建設公司：是投資興建單位<strong>，<span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; letter-spacing: 2px; color: #E36C09;">負責<span style="font-family: 微软雅黑, Microsoft YaHei; letter-spacing: 2px; font-weight: bold;">出資與規劃、設計</span></span></strong></span></p><p style=";line-height:130%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2）營造商：<span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #FE7E00; letter-spacing: 2px; font-weight: bold;">負責蓋房子</span><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242; letter-spacing: 2px;">，有的建商有自己的營造團隊，有的是關係企業，有的是委外給專業的營造商</span></span></p><p style=";line-height:130%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">3）代銷公司：<span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #FE7E00; letter-spacing: 2px; font-weight: bold;">負責規劃賣房子</span><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242; letter-spacing: 2px;">，專業的代銷公司，在建商取得土地初期，就開始提供市場分析並規劃定位，定位後，由代銷公司負責企劃與業務執行</span></span></p><p style=";line-height:130%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242; letter-spacing: 2px;">4）企劃公司：</span><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #FE7E00; letter-spacing: 2px; font-weight: bold;">負責包裝</span><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242; letter-spacing: 2px;">，響亮的案名與炫麗的平面廣告都出自企劃手筆，大多數代銷業者會與專業的企劃公司合作</span></span></p><p style=";line-height:130%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242; letter-spacing: 2px;">5）廣告代理商：</span><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #FE7E00; letter-spacing: 2px; font-weight: bold;">負責媒體採購</span><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242; letter-spacing: 2px;">，廣告代理商可能搜集到最多的建案與預算，然後向各大媒體（報紙、電臺、電視、網站、戶外媒體等）統購，由於量大，媒體往往會給代理商更低的價格或更多版面</span></span></p><p style=";line-height:130%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242; letter-spacing: 2px;"><br></span></p><p style=";line-height:130%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="color: #000000;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; letter-spacing: 2px; font-size: 16px;">3、建案開發的流程：</span></strong></span></p><p style=";line-height:130%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242; letter-spacing: 2px;"><img src="/upload/question/20200320/1584695010916142.png" alt="圖片.png"><br></span></p><p style=";line-height:130%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size:15px;font-family:微软雅黑;color:#424242;letter-spacing:2px"></span><br></p><p style=";line-height:130%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><span style="font-family: 微软雅黑, Microsoft YaHei; letter-spacing: 2px;"></span>4、新建案的類型：</span></strong></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;"><br></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">新成屋：</span><span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242; letter-spacing: 2px; font-size: 14px;">主體結構、內外環境、相關設施...等基本上均已完成，購置後可直接入住的建案）</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><br></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">預售屋</span><span style="color: #424242; letter-spacing: 2px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">：尚未開始建造或是仍在建造中，即開始銷售的房子，通常建商會以樣品屋、模型或圖片、影片等呈現新房屋樣貌</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><br></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">預推案</span><span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242; letter-spacing: 2px; font-size: 14px;">：預推案不屬於新建案類型，是在部分建案資訊還不完整時，591為了預告推案訊息而增設的分類</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242; letter-spacing: 2px; font-size: 14px;"></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; color: #FE7E00; letter-spacing: 2px; font-size: 14px;">（<span style="font-family: 微软雅黑; color: #FE7E00; letter-spacing: 2px; font-size: 14px;">只有編輯才可以刊登預推案</span>）</span><br></p><p style=";line-height:130%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;"></span><br></p><p style=";line-height:130%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><br></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">二、591新建案介紹</span></strong></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><strong><span style="font-family: 微软雅黑, Microsoft YaHei;"><br></span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">1、新建案刊登：</span></strong></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="color: #000000;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1）優先推薦給編輯代刊</span></strong></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">編輯代刊流程：接到資訊會馬上電話約拜訪時間，若時間無法配合，會用E-MAIL或通訊軟體傳送刊登表單，請現場先提供刊登資訊</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;"><br></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="color: #000000;"><strong><span style="color: #000000; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2）編輯代刊的優勢：</span></strong></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;"><span style="color: #000000; font-family: Arial;">•</span><span style="color: #000000; font-family: 微软雅黑;">刊登內容更完整、專業</span></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;"><span style="font-size: 14px; color: #000000; font-family: 微软雅黑;"></span> <span style="font-size: 14px; color: #000000; font-family: Arial;">•</span><span style="font-size: 14px; color: #000000; font-family: 微软雅黑;">站台免費曝光項目會以編輯刊登的建案為主，例如：720、建案影音直播、賞屋團….等</span> <br></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;"><span style="font-size: 14px; color: #000000; font-family: Arial;">•</span><span style="font-size: 14px; color: #000000; font-family: 微软雅黑;">編輯會頻繁與現場接觸，更新動態資訊，動態的變更會透過系統推播讓個案更頻繁的觸及消費者</span></span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size:24px;font-family:微软雅黑;color:white">優先推</span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size: 16px;"><strong><span style="color: #000000;"><span style="color: #000000; letter-spacing: 2px; font-family: 微软雅黑; font-weight: bold;">2、</span><span style="color: #000000; font-family: 微软雅黑;">自刊條件</span></span></strong></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="letter-spacing: 2px; font-size: 16px; font-family: 微软雅黑; color: #FE7E00; font-weight: bold;"></span><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1）會員身份：<span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242;">必須為建商/代銷公司，用其他身份刊登後需提供證明</span></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2）建案資質：<span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242;">大於1棟1戶，且已取得建照執照的建案</span></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242;"></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size: 14px; font-family: 微软雅黑; color: #FE7E00; letter-spacing: 2px;">PS：1.僅電腦版能刊登新建案</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size: 14px; font-family: 微软雅黑; color: #FE7E00; letter-spacing: 2px;">2.若會員要刊登一棟一戶，需記錄聯絡方式與區域，轉交給編輯聯絡/記錄</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242;"><br></span><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;">3、建案刊登規則介紹</span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;">1）建案基本資料<br></span></strong></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">a、建案名稱不可添加描述性文字與符號</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">b、不得將A建案資料改為B建案，故建案名稱與基地位址不可改</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">c、禁止刊登建案：BTO、定制化、不實的、已完銷、重複的、未取得建照的、單獨店面</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">d、規劃用途需與實際相符，非住宅用地建案：用途、格局、特色等，不能有可做住家的描述。PS：用途為透天，樓層就不能有幾十樓</span></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"></span></strong><br></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="letter-spacing: 2px; font-size: 16px; font-family: 微软雅黑; font-weight: bold; color: #000000;">2）付款信息</span><span style="letter-spacing: 2px; font-size: 16px; font-family: 微软雅黑; color: #FE7E00; font-weight: bold;"><br></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:24px;font-family:微软雅黑;color:white;letter-spacing:4px;font-weight:bold"></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">a、預售屋的自備款可以分期付款，確認要買時先付訂金，確認合約沒問題後，需簽約，並繳納簽約金、開工款 。訂金、簽約金、開工款，稱為「訂、簽、開」金，也就是預售屋的頭期款，通常約為房屋總價的10%～15%。</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">b、收費方式分為兩種：</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1）按工程期繳納：指依照工程進度，分期繳納該期工程款。</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2）按月繳納：開工之後，無論施工進度如何，皆須按月繳納工程款。</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">c、建商領到使用執照後，購房者需到銀行對保，由銀行審核貸款資格後確定最終貸款成數。</span></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><br></span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;">3）建築規劃</span></strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><br></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">a、樓層規劃、棟戶規劃、車位數量、基地面積、結構工程、建照執照、土地分區、建蔽率、投資建設、營造公司、企劃銷售、建築設計，應以建照執照、使用執照上的資訊一致，若會員主張相關資訊已變更，但政府系統未更新，請提供變更證明</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">b、基地面積：執照上基地面積單位為平方公尺，591顯示的基地面積單位為坪。故需將執照面積*0.3025進行換算。一般別墅透天產品會有多張執照，需將所有執照的基地面積加總後再*0.3025</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">c、當有多張執照時，棟戶規劃與車位(含法定與自定)需加總計算，樓層規劃每張執照基本相同，若出現部分是3樓，部分是5樓，則可標注為：地上3，5樓<br></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">d、若會員已取得使照，則以使照的資訊為主</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">e、土地分區非常重要，必須按執照填寫。土地分區主要分為都市計畫內的：住宅區、商業區、工業區...與都市計畫外的特種農業區、一般農業區、工業區、鄉村區、森林區、山坡地保護區、風景區等<br></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">f、建蔽率：建蔽率是指一塊土地可以用來蓋房子的範圍，每個縣市對每種土地分區的建蔽率規定不同，通常住宅區標準為60%，工業區70%，商業區80%。</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><img src="/upload/question/20200324/1585024138106044.png" alt="圖片.png"></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><br></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size: 16px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; letter-spacing: 2px;">4）聯絡方式</span></strong></span><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; letter-spacing: 2px;"><br></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">a、諮詢電話只能填寫真實有效的固定電話，填寫後系統會生成0986851088的免費電話</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">b、在非服務時間，【請案場聯絡我】會變為【預約洽詢】，填寫期望的來電時候後，案場會在約定的時間回撥 <br></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">c、好康活動限20字需客服審核，審核通過好康訊息會在建案清單中與【優惠建案】清單中顯示</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><br></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><br><span style="letter-spacing: 2px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;"></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size: 16px;"><strong><span style="letter-spacing: 2px; font-family: 微软雅黑, Microsoft YaHei; color: #000000;">5）特色介紹</span></strong></span><br></p><p><span style="font-size:16px;font-family:微软雅黑;color:#7F7F7F;letter-spacing:2px"></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">a、特色描述及其他所有欄位中，不得出現非591官方的電話、網址、Line ID等聯絡資訊；</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">b、若建案使用分區為工業用，不可在特色描述中留有住家相關資訊。</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><br></span></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 16px;">6）建案相冊介紹</span></strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><br></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">a、照片規定</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;"></span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">1.不可留有非591電話、網址、QR code、FB等</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">2.不可一圖多用</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">3.不可與建案無關</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">4.畫質清晰，不可歪斜</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">5.不可為網路截圖，或谷歌街景圖（版權問題）</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">6.最大支持10M，比例4:3</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;"><br></span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">b、封面圖</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">1.上傳正面完整的樓宇外觀圖或3D圖</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">2.不能有文字、邊框、拼接（3D圖備註參考外）</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">3.不可上傳素描照</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">4.圖片兩邊不能留白</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;"><br></span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">c、實景圖</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;"></span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">1.預售屋不少於3張不同角度外觀圖（基地圖）</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">2.新成屋不少於5張不同角度外觀圖</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">3.若接待中心不在基地，不可作為實景圖</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><br></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">d、環境圖</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;"></span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">1.機能、交通、學區、綠地等必須7張照片以上</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;"><br></span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">e、平面圖</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;"></span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">1.平面圖需符合建照用途（如規劃住家不可當成店面使用）</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">2.平面圖不可違法二次施工（有虛線）</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;"><br></span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">f、影音全景高空</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">1.有拍攝影音、720全景、高空環境圖的建案才需上傳</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-size:15px;font-family:Arial;color:#424242"></span><br></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="color: #000000;"><strong><span style="font-size: 16px; font-family: 微软雅黑;">7）、刊登問題小結</span></strong></span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242"></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="color: #424242; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">a、會員是仲介身份，想要刊登建案如何處理？</span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242; font-weight: bold; font-size: 14px;">告知仲介暫時無法刊登新建案，確認會員要刊登建案的原因，若會員表示是代銷公司，請會員提供<a href="https://zsk.591.com.tw/question/3927">銷售委託合約書</a>。</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><br><span style="font-size:16px;font-family:微软雅黑;color:#424242"></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">b、<span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242;">會員來電詢問為何不能刊登1棟1戶，如何處理？</span></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="color: #424242; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"></span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242; font-weight: bold; font-size: 14px;">確認會員是要刊登哪個區域的建案，並說明1棟1戶，只有網站編輯可以刊登，確認是否同意由編輯聯絡他代刊，無論會員是否同意，都要轉交對應區域的編輯說明，方便編輯記錄。</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242; font-size: 14px;"><br></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242;">C、會員表示我的建案還沒有名字要如何填寫？</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="color: #424242; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"></span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242; font-weight: bold; font-size: 14px;">建議可以寫：投資建設公司名+xx區（xx段、xx路）案，如：昌佑建設 草屯新案</span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242; font-weight: bold; font-size: 14px;"><br></span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242; font-weight: bold; font-size: 14px;"><br></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size:21px;font-family:微软雅黑;color:#424242"></span><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;">4、新建案審核<br></span></strong></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-align:justify;text-justify:inter-ideograph;direction:ltr;unicode-bidi:embed"><strong><span style="font-size: 16px; color: #424242; font-family: 微软雅黑, Microsoft YaHei;"></span></strong></p><p style=";line-height:46px;margin-top:0;margin-bottom:0;text-indent:0in;text-align: left;direction:ltr;unicode-bidi:embed"><strong><span style="font-family: 微软雅黑; vertical-align: baseline; font-size: 16px; color: #000000;">1）新建案審核頁面規則</span></strong></p><p style=";line-height:46px;margin-top:0;margin-bottom:0;text-indent:0in;text-align: left;direction:ltr;unicode-bidi:embed"><strong><span style="font-family: 微软雅黑; vertical-align: baseline; font-size: 16px; color: #000000;"></span></strong></p><table style="width: 799px;" width="1137" cellspacing="0" cellpadding="0"><colgroup><col style=";width:216px" width="216"><col style=";width:125px" width="125"><col style=";width:132px" width="133"><col style=";width:665px" width="665"></colgroup><tbody><tr style=";height:32px" class="firstRow" height="32"><td style="" width="216" height="32"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:19px;font-family:微软雅黑;color:#FEFFFF;font-weight:bold">審核類別</span></p></td><td style="" width="125"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:19px;font-family:微软雅黑;color:#FEFFFF;font-weight:bold">編輯狀態</span></p></td><td style="" width="132"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:19px;font-family:微软雅黑;color:#FEFFFF;font-weight:bold">客服狀態</span></p></td><td style="" width="665"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:19px;font-family:微软雅黑;color:#FEFFFF;font-weight:bold">狀態說明</span></p></td></tr><tr style=";height:45px" height="45"><td rowspan="4" style="" width="216" height="186"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">新建案刊登</span></p><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242;font-style:italic">編輯許可權與客服許可權同時通過審核，建案才會開啟</span></p></td><td style="" width="125"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">未處理</span></p></td><td style="" width="132"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#749530">已自動審核</span></p></td><td style="" width="665"><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">該筆建案為編輯刊登，</span><span style="font-size:16px;font-family:微软雅黑;color:#FE7E00;font-weight:bold">無需客服處理</span></p></td></tr><tr style=";height:39px" height="39"><td style="" width="125" height="39"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">未處理</span></p></td><td style="" width="132"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">未處理</span></p></td><td style="" width="665"><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">會員自刊建案，需客服處理，當前處於待審核狀態</span></p></td></tr><tr style=";height:44px" height="44"><td style="" width="125" height="44"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:red">待處理</span></p></td><td style="" width="132"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">未處理</span></p></td><td style="" width="665"><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">專員已審核，建案資料有誤，等待客服審核與撥出</span></p></td></tr><tr style=";height:59px" height="59"><td style="" width="125" height="59"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#749530">已通過</span></p></td><td style="" width="132"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">未處理</span></p></td><td style="" width="665"><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">專員已審核，建案資料正確，若客服審核其他資料無誤，通過審核，則建案開啟，若客服審核資料有誤放入待處理，則進入新建案刊登撥出列表，待跟進</span></p></td></tr><tr style=";height:63px" height="63"><td style="" width="216" height="63"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">新建案修改</span></p><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242;font-style:italic">先通過後審核</span></p></td><td style="" width="125"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">未處理</span></p></td><td style="" width="132"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">無</span></p></td><td style="" width="665"><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">會員修改建案後，需要專員審核，無誤則直接通過，有誤放入待處理則進入新建案修改撥出，待跟進</span></p></td></tr><tr style=";height:48px" height="48"><td rowspan="3" style="" width="216" height="143"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">新建案刊登撥出</span></p><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242;font-style:italic">編輯許可權與客服許可權同時通過審核，建案才會開啟</span></p></td><td style="" width="125"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:red">待處理</span></p></td><td style="" width="132"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:red">待處理</span></p></td><td style="" width="665"><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">建案資料與其他資料（圖片等）均需修改</span></p></td></tr><tr style=";height:48px" height="48"><td style="" width="125" height="48"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#749530">已通過</span></p></td><td style="" width="132"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:red">待處理</span></p></td><td style="" width="665"><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">建案資料正確，專員已審核通過，其他資料還需修改（或客服還未審核）</span></p></td></tr><tr style=";height:48px" height="48"><td style="" width="125" height="48"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:red">待處理</span></p></td><td style="" width="132"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#749530">已通過</span></p></td><td style="" width="665"><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">其他資料正確，客服已審核通過，建案資料還需修改（或專員還未審核）</span></p></td></tr><tr style=";height:46px" height="46"><td rowspan="2" style="" width="216" height="91"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">新建案修改撥出</span></p><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242;font-style:italic">編輯許可權通過審核即可</span></p></td><td style="" width="125"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:red">待處理</span></p></td><td style="" width="132"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:red">待處理</span></p></td><td style="" width="665"><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">會員修改的資料不符合規範，聯絡處理</span></p></td></tr><tr style=";height:46px" height="46"><td style="" width="125" height="46"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:red">待處理</span></p></td><td style="" width="132"><p style=";margin-top:0;margin-bottom:0;text-align:center;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#749530">已通過</span></p></td><td style="" width="665"><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">客服檢查會員修改的資料已符合規範，等待專員審核即可</span></p></td></tr></tbody></table><p style=";line-height:46px;margin-top:0;margin-bottom:0;text-indent:0in;text-align: left;direction:ltr;unicode-bidi:embed"><br></p><p style=";line-height:46px;margin-top:0;margin-bottom:0;text-indent:0in;text-align: left;direction:ltr;unicode-bidi:embed"><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">2）建案審核流程--編輯審核</span></strong><strong><span style="font-family: 微软雅黑; vertical-align: baseline; font-size: 16px; color: #000000;"></span></strong><br></p><p style=";margin-top:0;margin-bottom:0;text-indent:0in;text-align:justify;text-justify:distribute-all-lines;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242; font-weight: bold;">a、確認身份 </span><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">避免仲介、屋主身份刊登建案</span></span></p><p style=";margin-top:0;margin-bottom:0;text-indent:0in;text-align:justify;text-justify:distribute-all-lines;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">•<span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242; font-weight: bold;">查看刊登身份是否為建商/代銷</span></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><img src="/upload/question/20200324/1585029687915342.png" alt="圖片.png"><br></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">•<span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242; font-weight: bold;">搜尋諮詢電話是否為仲介公司</span></span><br></p><p><img src="/upload/question/20200324/1585029713364926.png" alt="圖片.png"></p><p><br></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">b、<span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242; font-weight: bold;">至</span></span><span style="color: #424242; font-weight: bold; font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;"><a href="http://cpabm.cpami.gov.tw/twmap.jsp">全國建築管理資訊系統入口網</a>核實以下建</span><span style="color: #000000;"><span style="font-weight: bold; font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">案資料 </span><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">確保與執照內容一致</span></span></p><p><span style="color: #000000; font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;"><img src="/upload/question/20200324/1585029886378685.png" alt="圖片.png"><br></span></p><p><span style="color: #000000; font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;"></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="letter-spacing: 4px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #C00000;"><br></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">C、查詢的方法</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1.臺北、新北、台中市可直接進入查詢</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2.其餘縣市需要使用自然人憑證，安裝外掛程式後查詢</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="letter-spacing: 4px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #C00000;"><br></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="letter-spacing: 4px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #C00000;"><img src="/upload/question/20200324/1585030049651421.png" alt="圖片.png"></span></p><p><br><span style="color: #000000; font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;"></span></p><p style=";line-height:46px;margin-top:0;margin-bottom:0;text-indent:0in;text-align: left;direction:ltr;unicode-bidi:embed"><span style="color: #000000;">d、<span style="font-size: 14px; font-family: 微软雅黑; vertical-align: baseline;">執照查詢會遇到的N種問題</span></span></p><p style=";line-height:46px;margin-top:0;margin-bottom:0;text-indent:0in;text-align: left;direction:ltr;unicode-bidi:embed"><span style="color: #000000; font-size: 14px; font-family: 微软雅黑; vertical-align: baseline;"></span></p><p style=";line-height:normal;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi: embed;vertical-align:auto"><span style="color: #595959; letter-spacing: 2px; font-weight: bold; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1、執照號碼查詢不到資料</span></p><p style=";line-height:130%;margin-top:0;margin-bottom:13px;text-align:left;direction:ltr;unicode-bidi: embed;vertical-align:auto"><span style="color: #595959; letter-spacing: 2px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">需用起造人姓名、建築地址進行查詢，仍查詢不到則需聯絡會員提供正確資訊</span></p><p style=";line-height:normal;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi: embed;vertical-align:auto"><span style="color: #595959; letter-spacing: 2px; font-weight: bold; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2、執照號碼查出來只有1棟1戶，但會員刊登了30棟30戶</span></p><p style=";line-height:130%;margin-top:0;margin-bottom:13px;text-indent:0in;text-align:left;direction: ltr;unicode-bidi:embed;vertical-align:auto"><span style="color: #595959; letter-spacing: 2px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">表示該建案有30張執照，一般剩餘的執照號碼為該執照相鄰的數字，如001、002...030。建議用起造人姓名或建築地號搜尋出所有執照後，批量開啟進行計算</span></p><p style=";line-height:normal;margin-top:0;margin-bottom:0;text-indent:0in;text-align:left;direction: ltr;unicode-bidi:embed;vertical-align:auto"><span style="color: #595959; letter-spacing: 2px; font-weight: bold; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">3、有多個執照號碼，但執照號碼查詢結果不能翻頁，不能批量打開</span></p><p style=";line-height:130%;margin-top:0;margin-bottom:13px;text-indent:0in;text-align:left;direction: ltr;unicode-bidi:embed;vertical-align:auto"><span style="color: #595959; letter-spacing: 2px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">用其中一筆執照查出建築地號，用地號搜尋看，若地號搜尋不出，只能一筆一筆輸入執照號碼查詢</span></p><p style=";line-height:normal;margin-top:0;margin-bottom:0;text-indent:0in;text-align:left;direction: ltr;unicode-bidi:embed;vertical-align:auto"><span style="color: #595959; letter-spacing: 2px; font-weight: bold; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">4、臺北、新北市的查詢頁面不太一樣</span></p><p style=";line-height:130%;margin-top:0;margin-bottom:13px;text-indent:0in;text-align:left;direction: ltr;unicode-bidi:embed;vertical-align:auto"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #595959; letter-spacing: 2px; font-size: 14px;">臺北市的建蔽率、車位元訊息只能在存根上找，且基地面積、棟戶規劃等訊息也要翻存根，以變更後的訊息為准。<a href="https://zsk.591.com.tw/question/3666">具體方法見知識庫</a></span></p><p style=";line-height:130%;margin-top:0;margin-bottom:13px;text-indent:0in;text-align:left;direction: ltr;unicode-bidi:embed;vertical-align:auto"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #595959; letter-spacing: 2px; font-size: 14px;"><a href="https://zsk.591.com.tw/question/3666"><strong><span style="font-family: 微软雅黑, Microsoft YaHei;"><br></span></strong></a></span></p><p style=";line-height:130%;margin-top:0;margin-bottom:13px;text-indent:0in;text-align:left;direction: ltr;unicode-bidi:embed;vertical-align:auto"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; letter-spacing: 2px; color: #000000; font-size: 16px;">2）建案審核流程--客服審核</span></strong></p><p style=";line-height:130%;margin-top:0;margin-bottom:13px;text-indent:0in;text-align:left;direction: ltr;unicode-bidi:embed;vertical-align:auto"><span style="color: #000000;"><span style="font-family: 微软雅黑, Microsoft YaHei; letter-spacing: 2px; font-size: 14px;">a、<span style="letter-spacing: 2px; font-family: 微软雅黑; font-weight: bold;">檢查其他資料是否正確</span></span><span style="font-family: 微软雅黑, Microsoft YaHei; letter-spacing: 2px; font-size: 16px;"><br></span></span></p><p style=";line-height:46px;margin-top:0;margin-bottom:0;text-indent:0in;text-align: left;direction:ltr;unicode-bidi:embed"><span style="color: #000000; font-size: 14px; font-family: 微软雅黑; vertical-align: baseline;"><img src="/upload/question/20200324/1585030540465203.png" alt="圖片.png"></span></p><p style=";line-height:46px;margin-top:0;margin-bottom:0;text-indent:0in;text-align: left;direction:ltr;unicode-bidi:embed">b<span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">、<span style="font-size: 14px; color: #424242; font-weight: bold;">聯絡會員說明審核結果，並跟進</span></span></p><p style=";line-height:46px;margin-top:0;margin-bottom:0;text-indent:0in;text-align: left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">•<span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242;">聯絡到會員說明審核結果，並確定最終修改時間，超過約定時間未處理，建案將被關閉</span></span></p><p style=";line-height:46px;margin-top:0;margin-bottom:0;text-indent:0in;text-align: left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">•<span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242;">若會員回饋我們的審核結果有誤，則回饋給審核專員重新再核實1次，若核實無誤，則需請會員提供證明</span></span></p><p style=";line-height:46px;margin-top:0;margin-bottom:0;text-indent:0in;text-align: left;direction:ltr;unicode-bidi:embed"><span style="color: #424242; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">•聯絡兩天無人接聽，發簡訊關閉建案</span></p><p style=";line-height:46px;margin-top:0;margin-bottom:0;text-indent:0in;text-align: left;direction:ltr;unicode-bidi:embed"><strong><span style="color: #424242; vertical-align: baseline; font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">c、好康審核</span></strong><span style="color: #424242; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"></span><span style="color: #424242; font-size: 14px; font-family: 微软雅黑; font-weight: bold;"><br></span></p><ul style="list-style-type: square;" class=" list-paddingleft-2"><li><p style=";line-height:46px;margin-top:0;margin-bottom:0;text-indent:0in;text-align: left;direction:ltr;unicode-bidi:embed"><span style="color: #424242; font-size: 14px; font-family: 微软雅黑; font-weight: bold;">好康發佈規則<br></span></p></li></ul><p><span style="font-size: 14px;"><span style="font-size: 14px; font-family: Arial;">•</span><span style="font-size: 14px; font-family: 微软雅黑; color: #424242;">真實有效的優惠資訊：促銷活動(SP)、促銷方案(如低首付)、賞屋贈品等，不超過15字</span><br></span></p><p><span style="font-size: 14px;"><span style="font-size: 14px; font-family: Arial;">•</span><span style="font-size: 14px; font-family: 微软雅黑; color: #424242;">審核後方可顯示，若未顯示請確認是否未審核或審核未通過</span></span></p><p><span style="font-size: 14px; font-family: 微软雅黑; color: #424242;"><br></span></p><ul style="list-style-type: square;" class=" list-paddingleft-2"><li><p><span style="font-size: 14px; font-family: 微软雅黑; color: #424242; font-weight: bold;">好康審核流程</span></p></li></ul><p><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">•<span style="font-size: 14px; color: #424242;">第一步：至後臺【客服相關-資料審核】點【建案新刊登】-【優惠資訊管理】</span></span></p><p><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;"><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei; color: #424242;"></span>•<span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei; color: #424242;">點擊【添加】編輯為15個字的通順句子，按【保存】</span></span><span style="font-family: 微软雅黑; color: #424242; font-size: 16px;"><br></span></p><p><span style="color: #424242; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><img src="/upload/question/20200324/1585030940969844.png" alt="圖片.png"><br></span></p><p><span style="font-family: 微软雅黑; color: #424242; font-size: 14px;"><span style="color: #424242; font-family: Arial;">•</span>勾選該建案，按【發佈】即可</span><span style="font-family: 微软雅黑; color: #424242; font-size: 16px;"><span style="font-size:16px;font-family:微软雅黑;color:#424242"></span><img src="/upload/question/20200324/1585030929152678.png" alt="圖片.png"><br></span></p><p><br></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000;">d、審核回顧</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000;"></span></strong></span></p><p style=";line-height:200%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 等线; color: #424242; font-weight: bold; font-size: 14px;">1）假設會員填寫的建案資料、圖片都正確，請問該筆建案你的審核結果是通過還是放待處理？為什麼？</span></p><p style=";line-height:200%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 等线; color: #424242; font-weight: bold; font-size: 14px;"></span></p><p style=";line-height:200%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size: 14px; font-family: 微软雅黑; color: #FE7E00; font-weight: bold;">答：放待處理，從刊登的標題與動態中來看，會員想賣店面，需確認是否只剩店面，若是，需請會員到店面去刊登，因為店面價格跟住家會有落差，會造成資料錯誤。若會員表示不僅僅是店面，則需更改建案名稱。</span></p><p style=";line-height:200%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 等线; color: #424242; font-weight: bold; font-size: 14px;"></span><br></p><p><img src="/upload/question/20200324/1585031044222017.png" alt="圖片.png"></p><p><span style="font-family: 微软雅黑; color: #424242; font-size: 16px;"><br></span></p><p style=";line-height:200%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size: 14px;"><span style="font-family: 微软雅黑; color: #424242;">2）</span><span style="font-family: 等线; color: #424242; font-weight: bold;">在建案資料正確的前提下，右邊的兩筆建案資訊是否皆符合刊登規範？</span></span></p><p style=";line-height:200%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 等线; color: #424242; font-weight: bold; font-size: 14px;"></span></p><p style=";line-height:200%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size: 14px; font-family: 微软雅黑; color: #FE7E00; font-weight: bold;">答：第一個標題中有特殊符號，需聯絡修改。第二個廠辦，只要資料正確，可以刊登。</span></p><p style=";line-height:200%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size: 14px; font-family: 微软雅黑; color: #FE7E00; font-weight: bold;"><img src="/upload/question/20200324/1585031142517684.png" alt="圖片.png"><br></span></p><p style=";line-height:200%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size: 14px; font-family: 微软雅黑; color: #FE7E00; font-weight: bold;"><img src="/upload/question/20200324/1585031157193362.png" alt="圖片.png"></span></p><p style=";line-height:200%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size:21px;font-family:等线;color:#424242;font-weight:bold"></span><br></p><p style=";line-height:200%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; color: #424242; font-size: 16px;"></span><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">3）<span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242; font-weight: bold;">右手邊的建案是否可以通過審核？為什麼？</span></span></p><p style=";line-height:200%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #FE7E00; font-weight: bold; font-size: 14px;">答：不可以，因為該筆建案的土地分區為工業區，且樓層概要中使用類組中沒有住家，故在格局、棟戶規劃、特色中都不可以提及住家訊息，避免被罰。</span></p><p style=";line-height:200%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #FE7E00; font-weight: bold; font-size: 14px;"><img src="/upload/question/20200324/1585031216303854.png" alt="圖片.png"><img src="/upload/question/20200324/1585031234891214.png" alt="圖片.png"><br></span></p><p><br></p><p style=";line-height:200%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">4）<span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242; font-weight: bold;">該建案的圖片是否符合刊登規範？</span></span></p><p style=";line-height:200%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="color: #FE7E00; font-weight: bold; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">答：不格局圖中有虛線，需聯絡會員確認是否為二工，若是，需將圖片刪除，若不是，做好記錄即可。</span></p><p style=";line-height:200%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="color: #FE7E00; font-weight: bold; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><img src="/upload/question/20200324/1585031275520853.png" alt="圖片.png"><br></span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-03-24 14:33:19</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="color: #000000;">5、建案展示</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000;">1）建案列表展示規則</span></strong></span><br></p><p><img src="/upload/question/20200324/1585031422600383.png" alt="圖片.png"><br></p><p><span style="color: #FE7E00; font-weight: bold; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">PS：清單排序規則隨時在變化，以上只做參考，若有會員一定要問規則，需與主任確認。</span></p><p><span style="color: #FE7E00; font-weight: bold; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><br></span></p><p style=";line-height:46px;margin-top:0;margin-bottom:0;text-indent:0in;text-align: left;direction:ltr;unicode-bidi:embed"><strong><span style="color: #000000;"><span style="font-weight: bold; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2）</span><span style="font-size: 14px; font-family: 微软雅黑; vertical-align: baseline;">新建案詳情頁 - 展示規則</span></span></strong></p><p style=";line-height:46px;margin-top:0;margin-bottom:0;text-indent:0in;text-align: left;direction:ltr;unicode-bidi:embed"><strong><span style="color: #000000; font-size: 14px; font-family: 微软雅黑; vertical-align: baseline;"><img src="/upload/question/20200324/1585031539718987.png" alt="圖片.png"><br></span></strong></p><p><span style="color: #FE7E00; font-weight: bold; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"></span><img src="/upload/question/20200324/1585031688308901.png" alt="圖片.png"></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; color: #FE7E00; font-size: 14px;">一公里以內的：</span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; color: #FE7E00; font-size: 14px;">交通：捷運站、公車站、快速道路、高速公路、高鐵、台鐵按順序展示</span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; color: #FE7E00; font-size: 14px;">學區：幼稚園、國小、國中、大學</span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size: 14px; font-family: 微软雅黑; color: #FE7E00;">生活機能：超商/賣場、傳統市場、商圈、公園</span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; color: #FE7E00; font-size: 14px;">其他機構：銀行、醫療機構、政府機構</span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size: 14px; font-family: 微软雅黑; color: #FE7E00;">嫌惡設施：墳墓（含殯儀館）、焚化爐、垃圾場（含回收廠）、輻射發射源（電塔、基地台、變電所）、工廠（含家庭代工廠）、特種營業場所、高架道路、加油站（含瓦斯儲氣槽）、宮廟</span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size: 14px; font-family: 微软雅黑; color: #FE7E00;"><br></span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size: 14px; font-family: 微软雅黑; color: #FE7E00;"><br></span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><strong><span style="font-size: 14px; font-family: 微软雅黑; color: #000000;">3）會員反饋地址錯誤</span></strong><span style="font-size: 14px; font-family: 微软雅黑; color: #FE7E00;"><br></span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size: 14px; font-family: 微软雅黑; color: #FE7E00;"><img src="/upload/question/20200324/1585031823452381.png" alt="圖片.png"><br></span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size: 14px; font-family: 微软雅黑; color: #FE7E00;"><span style="font-size: 16px; font-family: Arial;">•</span><span style="font-size:16px;font-family:微软雅黑;color:#424242">至谷歌地圖重新輸入會員的基地位址，確認與詳情頁顯示是否一致</span><br></span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size: 14px; font-family: 微软雅黑; color: #FE7E00;"><span style="font-size: 16px; font-family: Arial;">•</span><span style="font-size:16px;font-family:微软雅黑;color:#424242">若顯示一致，需確認會員的位址填寫是否正確</span></span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; font-size: 16px; color: #424242;"><span style="font-size: 16px; font-family: Arial;">•</span><span style="font-size:16px;font-family:微软雅黑;color:#424242">若不一致，重新至<a href="https://admin.591.com.tw/admin/housing/editHousingLocation">後臺</a>輸入正確的經緯度（或直接請會員提供經緯度）</span><br></span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; font-size: 16px; color: #424242;"></span></p><p style=";margin-top:0;margin-bottom:0;text-indent:0in;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242">PS：注意區分基地位址與接待會館</span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><img src="/upload/question/20200324/1585031905392340.png" alt="圖片.png"></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><br></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><br></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="color: #000000;">6、建案管理</span></strong></span></p><p style=";line-height:46px;margin-top:0;margin-bottom:0;text-indent:0in;text-align: left;direction:ltr;unicode-bidi:embed"><strong><span style="font-size: 14px; color: #000000;"><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">1）</span><span style="font-size: 14px; font-family: 微软雅黑; vertical-align: baseline;">新建案會員中心 - 建案管理</span></span></strong></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="color: #000000;"></span></strong></span><img src="/upload/question/20200324/1585032336612977.png" alt="圖片.png"><br></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">更新：<span style="font-family: 微软雅黑, Microsoft YaHei; color: #262626; letter-spacing: 2px;">更新後，將在建案默認排序條件下，排在廣告建案後面</span></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #262626; letter-spacing: 2px; font-size: 14px;">結案：操作結案後，該筆建案在前臺顯示“已完銷”與操作時間，已完銷的建案再次開啟需重新通過審核。</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="color: #262626; letter-spacing: 2px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">修改資料：已通過審核的建案，修改資料後系統會先顯示在網路上，同時進入後臺審核列表</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="color: #262626; letter-spacing: 2px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">刪除：無論是什麼狀態下，資料刪除後就無法找回了</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="color: #262626; letter-spacing: 2px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">銷售狀態：</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="color: #262626; letter-spacing: 2px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">待售：房子已經建好了，還沒開始賣，更改此狀態不影響物件的展示與搜尋</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="color: #262626; letter-spacing: 2px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">封盤：樓盤停止銷售（原因可能為：專案違規，證件不齊，行情冷淡開發商不賣了等等），更改此狀態不影響物件的展示與搜尋</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="color: #262626; letter-spacing: 2px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">換銷售：是指建商委託給代銷公司，更改此狀態物件會關閉下架</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="color: #262626; letter-spacing: 2px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><br></span></p><p style=";line-height:46px;margin-top:0;margin-bottom:0;text-indent:0in;text-align: left;direction:ltr;unicode-bidi:embed"><strong><span style="font-size: 14px;"><span style="color: #262626; letter-spacing: 2px; font-family: 微软雅黑, Microsoft YaHei;">2）</span><span style="font-family: 微软雅黑; color: #424242; vertical-align: baseline;">新建案會員中心 - 建案動態</span></span></strong></p><p style=";line-height:46px;margin-top:0;margin-bottom:0;text-indent:0in;text-align: left;direction:ltr;unicode-bidi:embed"><strong><span style="font-size: 14px; font-family: 微软雅黑; color: #424242; vertical-align: baseline;">a、展示<br></span></strong></p><p style=";line-height:46px;margin-top:0;margin-bottom:0;text-indent:0in;text-align: left;direction:ltr;unicode-bidi:embed"><strong><span style="font-size: 14px; font-family: 微软雅黑; color: #424242; vertical-align: baseline;"><img src="/upload/question/20200324/1585032524665594.png" alt="圖片.png"><br></span></strong></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="color: #262626; letter-spacing: 2px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><br></span><span style="font-family: 微软雅黑; color: #424242; font-weight: bold; font-size: 14px;">b、動態發佈規則</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; color: #424242; font-weight: bold; font-size: 14px;"></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; color: #424242; letter-spacing: 2px; font-size: 14px;">動態內容以銷售動態為重</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; color: #424242; letter-spacing: 2px; font-size: 14px;">不可重複發佈動態訊息</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; color: #262626; letter-spacing: 2px; font-size: 14px;"></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; color: #424242; letter-spacing: 2px; font-size: 14px;">不得出現廣告宣傳語，如：熱銷、精華、精選</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; color: #424242; letter-spacing: 2px; font-size: 14px;">不得為祝福及其他無意義資訊</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; color: #424242; letter-spacing: 2px; font-size: 14px;">不得留有聯絡方式</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; color: #424242; letter-spacing: 2px; font-size: 14px;"><br></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><strong><span style="font-family: 微软雅黑; color: #424242; letter-spacing: 2px; font-size: 14px;">c、動態審核流程</span></strong><span style="font-family: 微软雅黑; color: #424242; letter-spacing: 2px; font-size: 14px;"><br></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#262626;letter-spacing:2px"><br></span><span style="font-size:16px"><span style=";font-family:Arial"></span></span><span style="font-size:16px;font-family:微软雅黑;color:#424242"><span style="font-size:16px"><span style=";font-family:Arial">•</span></span><span style="font-size:16px;font-family:微软雅黑;color:#424242">若發現會員動態中有廣告詞，需將廣告詞添加至【</span><span style="font-size:16px;font-family:微软雅黑;color:#424242"><a href="https://admin.591.com.tw/admin/buildDynamic/buildDynamicKeyword">關鍵字管理</a></span><span style="font-size:16px;font-family:微软雅黑;color:#424242">】</span><span style="font-size: 16px;font-family:微软雅黑;color:#424242">中</span>進入【</span><span style="font-size: 16px;font-family:微软雅黑;color:#424242"><a href="https://admin.591.com.tw/admin/housing/buildDynamicList">建案動態</a></span><span style="font-size:16px;font-family:微软雅黑;color:#424242">】點</span><span style="font-size: 16px;font-family:微软雅黑;color:#424242">擊【未審核】</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size:16px;font-family:微软雅黑;color:#424242"><span style="font-size:16px"><span style=";font-family:Arial">•</span></span><span style="font-size:16px;font-family:微软雅黑;color:#424242">若發現會員動態中有廣告詞，需將廣告詞添加至【</span><span style="font-size:16px;font-family:微软雅黑;color:#424242"><a href="https://admin.591.com.tw/admin/buildDynamic/buildDynamicKeyword">關鍵字管理</a></span><span style="font-size:16px;font-family:微软雅黑;color:#424242">】</span><span style="font-size: 16px;font-family:微软雅黑;color:#424242">中</span>符合發佈規則點擊通過，不符合發佈規則，點擊不通過。不通過先選擇不符合的原因，系統會顯示給會員</span></p><p><img src="/upload/question/20200324/1585032641353795.png" alt="圖片.png"></p><p><span style="font-size:16px"><span style=";font-family:Arial">•</span></span><span style="font-size:16px;font-family:微软雅黑;color:#424242">若發現會員動態中有廣告詞，需將廣告詞添加至【</span><span style="font-size:16px;font-family:微软雅黑;color:#424242"><a href="https://admin.591.com.tw/admin/buildDynamic/buildDynamicKeyword">關鍵字管理</a></span><span style="font-size:16px;font-family:微软雅黑;color:#424242">】</span><span style="font-size: 16px;font-family:微软雅黑;color:#424242">中</span></p><p><br></p><p><strong><span style="color: #000000; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">3）資料統計</span></strong></p><p><strong><span style="color: #000000; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><img src="/upload/question/20200324/1585032754862511.png" alt="圖片.png"></span></strong></p><p><img src="/upload/question/20200324/1585032779442349.png" alt="圖片.png"><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-03-24 14:54:32</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><img src="/upload/question/20200324/1585032870700734.png" alt="圖片.png"></p><p><br></p><p><strong><span style="color: #424242; font-weight: bold; font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">2）資料統計</span></strong></p><p><strong><span style="color: #424242; font-weight: bold; font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;"></span></strong></p><p style=";margin-top:0;margin-bottom:0;text-indent:0in;text-align:justify;text-justify:distribute-all-lines;direction:ltr;unicode-bidi:embed"><span style="color: #424242; font-weight: bold; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">如何才能看到房客的完整電話號碼？如何取得回撥功能</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">•<span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242;">為保護個資安全，目前無法看到對方的完整號碼。如果希望能獲得回撥功能，需聯絡數位廣告購買廣告才可以擁有回撥功能</span></span></p><p style=";margin-top:0;margin-bottom:0;text-indent:0in;text-align:justify;text-justify:distribute-all-lines;direction:ltr;unicode-bidi:embed"><span style="color: #424242; font-weight: bold; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">我的建案是編輯代刊的，但我沒有收到資料包表，可以補給嗎？</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">•<span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242;">編輯代刊的建案，有填寫接收報表的郵箱功能，系統每週四上午9點會發送資料包表。因報表為系統自動發送，無法補發。可建議對方找編輯進入會員中心幫忙查看相關資料（報表的資料內容就是來電情況與訪問量分析）</span></span><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-03-24 15:25:14</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">三、其他周邊產品</span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;"><br></span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">1、環景、高空圖</span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;"></span></strong></p><p style=";line-height:46px;margin-top:0;margin-bottom:0;text-indent:0in;text-align: left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; vertical-align: baseline; color: #000000; font-size: 14px;">1）什麼是全景賞屋及高空賞屋</span></p><p style=";line-height:130%;margin-top:0;margin-bottom:0;text-indent:0in;text-align:left;direction: ltr;unicode-bidi:embed;vertical-align:auto"><span style="font-size: 14px; color: #000000;"><strong><span style="font-size: 14px; color: #000000; font-family: 微软雅黑, Microsoft YaHei;"></span></strong>720<span style="font-size: 14px; color: #000000; font-family: 微软雅黑, Microsoft YaHei;">環景</span>：<span style="font-size: 14px; color: #000000; font-family: 微软雅黑; font-variant: normal; letter-spacing: 0px; font-weight: normal; font-style: normal;">室內全景賞屋又稱室內環景720賞屋，主要拍攝室內的屋況</span></span></p><p><span style="font-size: 14px; color: #000000;"><strong><span style="font-size: 14px; color: #000000; font-family: 微软雅黑, Microsoft YaHei;"></span></strong></span></p><p style=";line-height:130%;margin-top:0;margin-bottom:0;text-indent:0in;text-align:left;direction: ltr;unicode-bidi:embed;vertical-align:auto"><span style="color: #000000;"><span style="color: #000000; font-size: 14px;"><span style="color: #000000; font-size: 14px; font-family: Arial; font-variant: normal; letter-spacing: 0px; font-weight: bold; font-style: normal;">Sky </span><span style="color: #000000; font-size: 14px; font-family: 微软雅黑; font-variant: normal; letter-spacing: 0px; font-weight: bold; font-style: normal;">高空</span></span><span style="color: #000000; font-family: 微软雅黑; font-variant: normal; letter-spacing: 0px; font-weight: normal; font-style: normal; font-size: 14px;">：又稱室外環景720賞屋，主要拍攝大樓的座標及周邊路況</span></span></p><p style=";line-height:130%;margin-top:0;margin-bottom:0;text-indent:0in;text-align:left;direction: ltr;unicode-bidi:embed;vertical-align:auto"><span style="color: #000000; font-family: 微软雅黑; font-variant: normal; letter-spacing: 0px; font-weight: normal; font-style: normal; font-size: 14px;"><br></span></p><p style=";line-height:130%;margin-top:0;margin-bottom:0;text-indent:0in;text-align:left;direction: ltr;unicode-bidi:embed;vertical-align:auto"><span style="color: #000000; font-family: 微软雅黑; font-variant: normal; letter-spacing: 0px; font-weight: normal; font-style: normal; font-size: 14px;">2）720作業流程：</span></p><p style=";line-height:130%;margin-top:0;margin-bottom:0;text-indent:0in;text-align:left;direction: ltr;unicode-bidi:embed;vertical-align:auto"><span style="color: #000000; font-family: 微软雅黑; font-variant: normal; letter-spacing: 0px; font-weight: normal; font-style: normal; font-size: 14px;"><img src="/upload/question/20200324/1585033274291400.png" alt="圖片.png"><br></span></p><p><br><strong><span style="font-family: 微软雅黑, Microsoft YaHei;"></span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">3）常見問題</span></strong></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">a、可以將A建案的環景圖換到B建案嗎？</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">確定是同一個建案，要換帳號刊登，是可以的，請專員在debug後臺操作更換即可</span></p><p><br></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><br></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">b、為什麼我的720打不開了？</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">這種情況需要用會員的建案名稱在</span><a href="https://admin.591.com.tw/admin/housing/searchBuild" style="font-family: 微软雅黑, Microsoft YaHei; text-decoration: underline; font-size: 14px;"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">後臺</span></a><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">搜尋，確認會員的建案是否有更換過，比如已完銷的建案，重新開啟後，建案ID變更了，若是，請專員在debug後臺操作更換720即可。若未更換過，則需轉交專員與工程師確認原因。</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><br></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">c、不想展示720了，如何刪除</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">進入該筆建案相冊，將720或高空圖的封面確認刪除即可。</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><br></span></p><p><span style="font-size: 16px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">2、賞屋團</span></strong></span><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><br></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1）什麼是賞屋團</span>：591精選出熱門建案，通過網頁報名，聚集賞屋客，由591編輯帶團賞屋，全程免費</p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">2）目的：媒合交易，提升591新建案品牌知名度，擴寬營收管道</span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">3）時間：週六 9:30~17:00，約2周1次<br></span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">4）地區：北部、中部、南部熱門城市</span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;"><br></span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">5）賞屋團流程</span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;"><img src="/upload/question/20200324/1585033786712495.png" alt="圖片.png"><br></span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;"><br></span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">6）賞屋團常見問題</span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;"></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">a、建商來電表示希望能加入賞屋建案</span></p><p></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">記錄聯絡方式、區域、建案名，轉交對應區域編輯聯絡</span></p><p><br></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">b、購房者來電查詢是否有報名成功？</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">至賞屋團活動報名資訊-</span><a href="https://admin.591.com.tw/admin/activity/enjoyHouse" style="font-family: 微软雅黑, Microsoft YaHei; text-decoration: underline; font-size: 14px; color: rgb(0, 0, 0);"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">報名統計</span></a><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">中查詢，由於報名後需要專員聯絡後才能確定是否成功，建議直接與客服主任確認是否已聯絡。</span></p><p><br></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">c、已報名成功的購房者來電表示要取消報名</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">詢問不參加的原因，另提供取消者的：姓名、身分字型大小、聯絡電話（若要取消多名人員，需一併提供）給客服主任，若當下無法核對身份字型大小的，請確認清楚是誰要取消。確認取消後，需發手機簡訊給會員。</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">您好，因您來電要求退出本期賞屋團活動，現已為您取消成功，如有疑問，請您聯絡客服中心02-55722000，謝謝</span></p><p><br></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">d、外國人是否可以報名賞屋團</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">可以，可提供護照號碼</span></p><p><br></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">e、是否可以帶小朋友參加賞屋團</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">6個月以下的嬰兒不可以參加，6個月以上的需確認是否是監護人帶，若不是監護人帶，則需要在統一買保險之前，提前由監護人簽好要保書，才能參加活動，否則不能帶小孩。</span></p><p><br></p><p></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">f、為什麼我明明是前面幾個報名的，結果沒有報名成功？我都已經請好假了，你們要賠償我的損失！</span></p><p></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">很抱歉造成困擾，客服非常理解您的心情，為避免出現這樣的問題，我們已經在活動報名頁面提醒：客服人員與您聯繫後，才屬報名成功。同時本公司保有決定參團名單的權益，因最終參團名單為隨機抽取，查詢此次未抽取到您。由於本期名額已滿，無法再加人，建議您後續再關注我們的活動，因此給您帶來不便，還請您多多見諒。 </span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size:21px;font-family: 微软雅黑;color:#595959"><img src="/upload/question/20200324/1585033989566758.png" alt="圖片.png"><br></span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size:21px;font-family: 微软雅黑;color:#595959"><br></span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><strong><span style="font-family: 微软雅黑; font-size: 16px; color: #000000;">3、新聞頻道</span></strong></p><p style=";margin-top:0;margin-bottom:0;text-indent:0in;text-align:justify;text-justify:distribute-all-lines;direction:ltr;unicode-bidi:embed"><span style="font-size: 14px; color: #000000;"><span style="font-size: 14px; font-family: 微软雅黑;">1）</span><span style="font-size: 14px; font-family: 微软雅黑; font-weight: bold;">建案一周動態規則</span></span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">•<span style="font-size: 14px; color: #424242;">只顯示上周編輯代刊的：有新動態與新刊登的建案</span> •<span style="font-size: 14px; color: #424242;">抓取週期：上週一~上周日，於下週一顯示</span> •<span style="font-size: 14px; color: #424242;">分城市展示，如該城市上周動態更新數量少於3個，則不展示</span></span></p><p style=";margin-top:0;margin-bottom:0;text-indent:0in;text-align:justify;text-justify:distribute-all-lines;direction:ltr;unicode-bidi:embed"><br><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">2）<span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-weight: bold;">FB直播規則</span></span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">•目前每月全台5個開箱直播，有3位主持人出鏡講解，在fb平臺推廣 •開箱直播，同時將上線至月臺新建案詳情頁 •選取評估的標準為：</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-indent:0in;text-align:left;direction:ltr;unicode-bidi: embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">1）不要找尾案</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-indent:0in;text-align:left;direction:ltr;unicode-bidi: embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">2）樣品屋或實品屋至少1間</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-indent:0in;text-align:left;direction:ltr;unicode-bidi: embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">3）北部量體10億以上、中南部量體5億以上</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-indent:0in;text-align:left;direction:ltr;unicode-bidi: embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">4）銷售期不要超過一年</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-indent:0in;text-align:left;direction:ltr;unicode-bidi: embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">5）可售成數5成內</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-indent:0in;text-align:left;direction:ltr;unicode-bidi: embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">6）以上條件不符皆可討論</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-indent:0in;text-align:left;direction:ltr;unicode-bidi: embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">7）超級大案特殊處理</span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-indent:0in;text-align:left;direction:ltr;unicode-bidi: embed"><span style="font-size: 16px; font-family: 微软雅黑, Microsoft YaHei; color: #000000;"><br></span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-indent:0in;text-align:left;direction:ltr;unicode-bidi: embed"><strong><span style="font-size: 16px; font-family: 微软雅黑, Microsoft YaHei; color: #000000;">4、如何購買圖片廣告</span></strong></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-indent:0in;text-align:left;direction:ltr;unicode-bidi: embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">•直接聯絡數字廣告<br></span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-indent:0in;text-align:left;direction:ltr;unicode-bidi: embed"><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; font-size: 14px;">•或線上填寫資料，等待廣告專員聯絡<span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242;"></span></span><span style="font-size: 16px; font-family: 微软雅黑, Microsoft YaHei; color: #000000;"><br></span></p><p style=";line-height:150%;margin-top:0;margin-bottom:0;text-indent:0in;text-align:left;direction:ltr;unicode-bidi: embed"><span style="font-size: 16px; font-family: 微软雅黑, Microsoft YaHei; color: #000000;"><img src="/upload/question/20200324/1585034654611974.png" alt="圖片.png"></span></p><p style=";margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size: 16px;font-family:微软雅黑;color:#424242"></span><strong><span style="font-family: 微软雅黑; font-size: 16px; color: #000000;"></span></strong><br></p><p style=";margin-top:0;margin-bottom:0;text-align:right;direction:ltr;unicode-bidi:embed"><span style="font-size:21px;font-family:微软雅黑;color:#595959"><br></span></p><p style=";margin-top:0;margin-bottom:0;text-align:right;direction:ltr;unicode-bidi:embed"><span style="font-size:21px;font-family:微软雅黑;color:#595959"><br></span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-03-24 15:41:03</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;">四、來電處理流程</span></strong></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;"><strong>1、<span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; font-weight: bold;">查出會員資料，確認來電者身份</span></strong></span><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242; font-weight: bold;"></span><br></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong>•<span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242;">同時快速查看會員的建案刊登情況與客服記錄，提前掌握會員帳號內的情況</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242;"><img src="/upload/question/20200324/1585035035719776.png" alt="圖片.png"></span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242;"></span></strong></span><span style="font-size: 14px; font-family: 微软雅黑; font-weight: bold; color: #000000;"><br></span></p><p><span style="font-size: 14px; font-family: 微软雅黑; font-weight: bold; color: #000000;">2、瞭解會員訴求，按流程針對性回復/處理</span></p><p><br></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">3、<span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; vertical-align: baseline;">識別VIP</span></span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; vertical-align: baseline;">1）品牌建商：</span></strong></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑; letter-spacing: 2px; font-size: 14px; color: #000000;">目前的名單是固定的：華固&gt;宏盛&gt;國泰&gt;冠徳&gt;升陽&gt;聯聚&gt;潤泰&gt;中悅&gt;長虹</span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="font-size:17px;font-family:微软雅黑;color:#595959;letter-spacing:2px"><img src="/upload/question/20200324/1585035114251937.png" alt="圖片.png"></span></p><p><br></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="color: #000000; font-size: 14px;">2）AD廣告戶</span></strong></span></p><p><span style="color: #000000; font-size: 14px;"><strong><span style="color: #000000; font-size: 14px; font-family: 微软雅黑, Microsoft YaHei; vertical-align: baseline;"></span></strong></span></p><p style=";line-height:120%;margin-top:0;margin-bottom:0;text-align:left;direction:ltr;unicode-bidi:embed"><span style="color: #000000; font-size: 14px;"><span style="color: #000000; font-size: 14px; font-family: 微软雅黑; letter-spacing: 2px;">即將在</span><span style="color: #000000; font-size: 14px; font-family: Arial; letter-spacing: 2px;">591</span><span style="color: #000000; font-size: 14px; font-family: 微软雅黑; letter-spacing: 2px;">跑廣告，或正在跑廣告的建案</span><span style="font-size: 14px; font-family: 微软雅黑; letter-spacing: 2px; font-weight: bold; color: #C00000;">不可直接關閉廣告</span></span></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; vertical-align: baseline;"><img src="/upload/question/20200324/1585035194838386.png" alt="圖片.png"><br></span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; vertical-align: baseline;"><br></span></strong></p><p><span style="font-size: 16px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000; vertical-align: baseline;">4、<span style="vertical-align: baseline; font-family: 微软雅黑;">詢問刊登流程</span></span></strong></span></p><p style=";margin-top:0;margin-bottom:0;text-indent:0in;text-align:justify;text-justify:distribute-all-lines;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; vertical-align: baseline;">1）</span></strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; font-weight: bold;">優先推薦編輯代刊</span></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">•網站目前有兩種刊登方式，1是由591的編輯為您代刊，2是您自行註冊後進行刊登，兩種都是免費的，但編輯代刊可以省去您日後管理的時間成本，且刊登內容更完整、專業。請問您傾向於哪一種呢？</span></p><p><br><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000; vertical-align: baseline;"></span></strong></span></p><p style=";margin-top:0;margin-bottom:0;text-indent:0in;text-align:justify;text-justify:distribute-all-lines;direction:ltr;unicode-bidi:embed"><span style="color: #000000;"><span style="color: #000000; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2）</span><span style="color: #000000; font-weight: bold; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">會員選擇編輯代刊</span></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">•確認建案是在什麼縣市、區域 •是新成屋，還是預售屋 •確認對方的聯絡方式（聯絡電話與稱呼），說明稍後會有負責的編輯聯絡處理 •發群轉交專員提交給編輯聯絡</span><span style="font-weight: bold; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;"><br></span></p><p style=";margin-top:0;margin-bottom:0;text-indent:0in;text-align:justify;text-justify:distribute-all-lines;direction:ltr;unicode-bidi:embed"><br></p><p style=";margin-top:0;margin-bottom:0;text-indent:0in;text-align:justify;text-justify:distribute-all-lines;direction:ltr;unicode-bidi:embed"><span style="color: #000000;"><span style="color: #000000; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">3）</span><span style="color: #000000; font-weight: bold; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">會員選擇自己刊登</span></span></p><p style=";margin-top:0;margin-bottom:0;text-indent:0in;text-align:justify;text-justify:distribute-all-lines;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #000000;">•確認會員身份（是代銷公司，還是建商還是屋主自建，仲介不予刊登） •詳細說明刊登流程與規則</span></p><p style=";margin-top:0;margin-bottom:0;text-indent:0in;text-align:justify;text-justify:distribute-all-lines;direction:ltr;unicode-bidi:embed"><span style="color: #424242; font-weight: bold; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><br></span></p><p style=";margin-top:0;margin-bottom:0;text-indent:0in;text-align:justify;text-justify:distribute-all-lines;direction:ltr;unicode-bidi:embed"><span style="color: #000000; font-size: 16px; font-weight: bold; font-family: 微软雅黑, Microsoft YaHei;">5、<span style="font-weight: bold; font-family: 微软雅黑, Microsoft YaHei; vertical-align: baseline;">自刊修改建案資料/動態</span></span></p><p><span style="color: #000000; font-size: 16px; font-weight: bold; font-family: 微软雅黑, Microsoft YaHei; vertical-align: baseline;"></span><span style="font-size: 16px; color: #424242; font-weight: bold; font-family: 微软雅黑, Microsoft YaHei; vertical-align: baseline;"></span><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1）查看來電號碼，核實是否是註冊電話/案場電話來電</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">•若是，可在電話中直接協助修改，若修改項為：執照號碼、樓棟/層規劃、土地分區、建蔽率、基地面積、動態，需說明相關資料需專員審核。若審核結果有誤，專員會再聯絡他說明。<br></span></p><p><br></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2）非註冊電話來電，需回撥案場或註冊電話核實身份</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">•告知會員，為保障建案資料的安全，我們需要確認身份，確認案場電話或註冊電話是否方便接聽？或我們聯絡過去是否可以核實其身份，若可以，則先記錄會員要修改的資料以及會員的稱呼。回撥核實身份後，再幫會員操作修改。</span></p><p><br></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">3）無法確認身份，則請會員自行上線修改</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">•若會員表示這個建案之前是其他人刊登的，跟他們沒有關係，找不到對方修改，則確認清楚他是什麼身份？建商？還是代銷公司？公司名字是什麼？記錄好聯絡方式。然後再回撥之前已刊登建案的註冊電話或案場電話瞭解情況，若確定現在要換帳號刊登，則關閉原建案，請會員重新註冊帳號刊登或推薦編輯代刊。</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><br></span></p><p><span style="font-size: 16px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000;">6、其他刊登問題</span></strong></span><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><br></span></p><p style=";margin-top:0;margin-bottom:0;text-indent:0in;text-align:justify;text-justify:distribute-all-lines;direction:ltr;unicode-bidi:embed"><span style="font-size: 14px;"><span style="font-family: 微软雅黑, Microsoft YaHei;">1）</span><span style="font-family: 微软雅黑; color: #424242; font-weight: bold;">刊登時提示建案名重複</span></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">•<span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242;">請會員提供建案名，至</span></span><span style="color: #424242; font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;"><a href="https://admin.591.com.tw/admin/housing/searchBuild">後臺</a>搜尋，找到另外一筆相同的開啟的物件，查看刊登者</span><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">•<span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242;">並告知會員該筆已有人刊登，確認要再次刊登的原因</span> •<span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242;">根據會員回饋的情況靈活處理，如：之前是編輯刊登的，會員確定要自己刊登，則記錄會員的聯絡方式，建案名稱，建案地區，提交到【新建案審核群】請編輯確認，同意後方可刪除編輯刊登的物件；</span></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><br></span></p><p style=";margin-top:0;margin-bottom:0;text-indent:0in;text-align:justify;text-justify:distribute-all-lines;direction:ltr;unicode-bidi:embed"><span style="font-size: 14px;"><span style="font-family: 微软雅黑, Microsoft YaHei;">2）</span><span style="font-family: 微软雅黑; color: #424242; font-weight: bold;">確認是否刊登成功/搜尋不到建案</span></span></p><p><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">•<span style="font-size: 14px; color: #424242;">查看會員建案的狀態，開啟中才代表已審核通過</span> •<span style="font-size: 14px; color: #424242;">搜尋時，必須要選擇對應的縣市，如臺北市的建案，必須要在左上角選“臺北市”才可以搜到</span></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><img src="/upload/question/20200324/1585035564226200.png" alt="圖片.png"><br></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><br></span></p><p><span style="font-size: 16px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000;">7、其他</span></strong></span></p><p style=";margin-top:0;margin-bottom:0;text-indent:0in;text-align:justify;text-justify:distribute-all-lines;direction:ltr;unicode-bidi:embed"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000;">1）</span></strong><span style="font-family: 微软雅黑, Microsoft YaHei; color: #424242; font-weight: bold;">會員來電詢問想要上591開箱文要怎麼處理？</span></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">•<span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242;">請記錄會員的聯絡方式，建案名稱，建案地區，提交到【新建案審核群】請編輯聯絡</span> •<span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242;">目前開箱文僅臺北、新北、桃園、台中、台南、高雄、新竹地區建案可寫開箱文</span><br></span></p><p><br></p><p style=";margin-top:0;margin-bottom:0;text-indent:0in;text-align:justify;text-justify:distribute-all-lines;direction:ltr;unicode-bidi:embed"><span style="color: #424242; font-weight: bold; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2）如何知道哪些帳號屬於編輯的</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">•<span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242;">編輯帳號的公司名字為：數字科技</span> •<span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: #424242;">建案刊登量大</span></span><span style="font-size: 16px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; color: #000000;"></span></strong></span><br></p></div></div> <div><span class="button">我要編輯</span></div></li></ul> <h3 class="more-answer">我要補充</h3> <div style=""><div id="editor_37633099609636190" class="edui-default" style=""><div id="edui1" class="edui-editor  edui-default" style="width: 100%; z-index: 999;"><div id="edui1_toolbarbox" class="edui-editor-toolbarbox edui-default"><div id="edui1_toolbarboxouter" class="edui-editor-toolbarboxouter edui-default"><div class="edui-editor-toolbarboxinner edui-default"><div id="edui2" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui2&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui3" class="edui-box edui-button edui-for-fullscreen edui-default"><div id="edui3_state" onmousedown="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui3_body" unselectable="on" title="全屏" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui3&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui3&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui4" class="edui-box edui-button edui-for-source edui-default"><div id="edui4_state" onmousedown="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui4_body" unselectable="on" title="源代码" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui4&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui4&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui5" class="edui-box edui-button edui-for-undo edui-default"><div id="edui5_state" onmousedown="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui5_body" unselectable="on" title="撤销" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui5&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui5&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui6" class="edui-box edui-button edui-for-redo edui-default"><div id="edui6_state" onmousedown="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui6_body" unselectable="on" title="重做" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui6&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui6&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui7" class="edui-box edui-button edui-for-simpleupload edui-default"><div id="edui7_state" onmousedown="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui7_body" unselectable="on" title="单图上传" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui7&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui7&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"><iframe style="display: block; width: 20px; height: 20px; overflow: hidden; border: 0px; margin: 0px; padding: 0px; position: absolute; top: 0px; left: 0px; opacity: 0; cursor: pointer;"></iframe></div></div></div></div></div><div id="edui8" class="edui-box edui-button edui-for-imageleft edui-default"><div id="edui8_state" onmousedown="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui8_body" unselectable="on" title="左浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui8&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui8&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui9" class="edui-box edui-button edui-for-imageright edui-default"><div id="edui9_state" onmousedown="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui9_body" unselectable="on" title="右浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui9&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui9&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui10" class="edui-box edui-button edui-for-imagecenter edui-default"><div id="edui10_state" onmousedown="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui10_body" unselectable="on" title="居中" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui10&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui10&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui11" class="edui-box edui-separator  edui-default"></div><div id="edui12" class="edui-box edui-button edui-for-date edui-default"><div id="edui12_state" onmousedown="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui12_body" unselectable="on" title="日期" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui12&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui12&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui13" class="edui-box edui-button edui-for-time edui-default"><div id="edui13_state" onmousedown="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui13_body" unselectable="on" title="时间" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui13&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui13&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui14" class="edui-box edui-button edui-for-horizontal edui-default"><div id="edui14_state" onmousedown="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui14_body" unselectable="on" title="分隔线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui14&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui14&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui15" class="edui-box edui-separator  edui-default"></div><div id="edui16" class="edui-box edui-splitbutton edui-for-inserttable edui-default"><div title="插入表格" id="edui16_state" onmousedown="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui16_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onArrowClick();"></div></div></div></div><div id="edui19" class="edui-box edui-button edui-for-deletetable edui-default"><div id="edui19_state" onmousedown="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui19_body" unselectable="on" title="删除表格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui19&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui19&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui20" class="edui-box edui-button edui-for-insertrow edui-default"><div id="edui20_state" onmousedown="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui20_body" unselectable="on" title="前插入行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui20&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui20&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui21" class="edui-box edui-button edui-for-insertcol edui-default"><div id="edui21_state" onmousedown="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui21_body" unselectable="on" title="前插入列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui21&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui21&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui22" class="edui-box edui-button edui-for-mergeright edui-default"><div id="edui22_state" onmousedown="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui22_body" unselectable="on" title="右合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui22&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui22&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui23" class="edui-box edui-button edui-for-mergedown edui-default"><div id="edui23_state" onmousedown="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui23_body" unselectable="on" title="下合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui23&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui23&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui24" class="edui-box edui-button edui-for-deleterow edui-default"><div id="edui24_state" onmousedown="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui24_body" unselectable="on" title="删除行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui24&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui24&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui25" class="edui-box edui-button edui-for-deletecol edui-default"><div id="edui25_state" onmousedown="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui25_body" unselectable="on" title="删除列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui25&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui25&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui26" class="edui-box edui-button edui-for-splittorows edui-default"><div id="edui26_state" onmousedown="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui26_body" unselectable="on" title="拆分成行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui26&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui26&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui27" class="edui-box edui-button edui-for-splittocols edui-default"><div id="edui27_state" onmousedown="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui27_body" unselectable="on" title="拆分成列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui27&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui27&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui28" class="edui-box edui-button edui-for-splittocells edui-default"><div id="edui28_state" onmousedown="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui28_body" unselectable="on" title="完全拆分单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui28&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui28&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui29" class="edui-box edui-button edui-for-mergecells edui-default"><div id="edui29_state" onmousedown="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui29_body" unselectable="on" title="合并多个单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui29&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui29&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui36" class="edui-box edui-button edui-for-edittable edui-default"><div id="edui36_state" onmousedown="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui36_body" unselectable="on" title="表格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui36&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui36&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui41" class="edui-box edui-button edui-for-edittd edui-default"><div id="edui41_state" onmousedown="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui41_body" unselectable="on" title="单元格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui41&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui41&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui42" class="edui-box edui-separator  edui-default"></div><div id="edui43" class="edui-box edui-menubutton edui-for-lineheight edui-default"><div title="行间距" id="edui43_state" onmousedown="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui43_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onArrowClick();"></div></div></div></div><div id="edui52" class="edui-box edui-menubutton edui-for-rowspacingtop edui-default"><div title="段前距" id="edui52_state" onmousedown="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui52_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onArrowClick();"></div></div></div></div><div id="edui59" class="edui-box edui-menubutton edui-for-rowspacingbottom edui-default"><div title="段后距" id="edui59_state" onmousedown="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui59_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onArrowClick();"></div></div></div></div></div><div id="edui66" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui66&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui67" class="edui-box edui-combox edui-for-fontfamily edui-default"><div title="字体" id="edui67_state" onmousedown="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui67_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onButtonClick(event, this);">字体</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onArrowClick();"></div></div></div></div><div id="edui80" class="edui-box edui-combox edui-for-customstyle edui-default"><div title="自定义标题" id="edui80_state" onmousedown="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui80_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onButtonClick(event, this);">自定义标题</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onArrowClick();"></div></div></div></div><div id="edui86" class="edui-box edui-combox edui-for-paragraph edui-default"><div title="段落格式" id="edui86_state" onmousedown="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui86_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onButtonClick(event, this);">段落格式</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onArrowClick();"></div></div></div></div><div id="edui95" class="edui-box edui-combox edui-for-fontsize edui-default"><div title="字号" id="edui95_state" onmousedown="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui95_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onButtonClick(event, this);">字号</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onArrowClick();"></div></div></div></div><div id="edui110" class="edui-box edui-button edui-for-link edui-default"><div id="edui110_state" onmousedown="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui110_body" unselectable="on" title="超链接" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui110&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui110&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui111" class="edui-box edui-button edui-for-indent edui-default"><div id="edui111_state" onmousedown="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui111_body" unselectable="on" title="首行缩进" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui111&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui111&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui112" class="edui-box edui-button edui-for-bold edui-default"><div id="edui112_state" onmousedown="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui112_body" unselectable="on" title="加粗" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui112&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui112&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui113" class="edui-box edui-splitbutton edui-for-forecolor edui-default edui-colorbutton"><div title="字体颜色" id="edui113_state" onmousedown="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui113_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui113_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onArrowClick();"></div></div></div></div><div id="edui116" class="edui-box edui-splitbutton edui-for-backcolor edui-default edui-colorbutton"><div title="背景色" id="edui116_state" onmousedown="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui116_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui116_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onArrowClick();"></div></div></div></div><div id="edui119" class="edui-box edui-button edui-for-italic edui-default"><div id="edui119_state" onmousedown="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui119_body" unselectable="on" title="斜体" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui119&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui119&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui120" class="edui-box edui-button edui-for-underline edui-default"><div id="edui120_state" onmousedown="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui120_body" unselectable="on" title="下划线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui120&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui120&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui121" class="edui-box edui-button edui-for-strikethrough edui-default"><div id="edui121_state" onmousedown="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui121_body" unselectable="on" title="删除线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui121&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui121&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui122" class="edui-box edui-button edui-for-superscript edui-default"><div id="edui122_state" onmousedown="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui122_body" unselectable="on" title="上标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui122&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui122&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui123" class="edui-box edui-button edui-for-subscript edui-default"><div id="edui123_state" onmousedown="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui123_body" unselectable="on" title="下标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui123&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui123&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui124" class="edui-box edui-button edui-for-removeformat edui-default"><div id="edui124_state" onmousedown="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui124_body" unselectable="on" title="清除格式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui124&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui124&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui125" class="edui-box edui-button edui-for-blockquote edui-default"><div id="edui125_state" onmousedown="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui125_body" unselectable="on" title="引用" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui125&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui125&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui126" class="edui-box edui-button edui-for-pasteplain edui-default"><div id="edui126_state" onmousedown="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui126_body" unselectable="on" title="纯文本粘贴模式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui126&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui126&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui127" class="edui-box edui-menubutton edui-for-insertorderedlist edui-default"><div title="有序列表" id="edui127_state" onmousedown="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui127_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onArrowClick();"></div></div></div></div><div id="edui140" class="edui-box edui-menubutton edui-for-insertunorderedlist edui-default"><div title="无序列表" id="edui140_state" onmousedown="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui140_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onArrowClick();"></div></div></div></div><div id="edui147" class="edui-box edui-button edui-for-cleardoc edui-default"><div id="edui147_state" onmousedown="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui147_body" unselectable="on" title="清空文档" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui147&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui147&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div></div></div></div><div id="edui1_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;"><div id="edui1_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui1&quot;].showWordImageDialog();">点击上传</div><div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui1&quot;].hideToolbarMsg();">x</div><div id="edui1_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div><div style="height:0;overflow:hidden;clear:both;" class="edui-default"></div></div><div id="edui1_message_holder" class="edui-editor-messageholder edui-default"></div></div><div id="edui1_iframeholder" class="edui-editor-iframeholder edui-default" style="width: 100%; height: 300px; z-index: 999; overflow: hidden;"><iframe id="ueditor_0" width="100%" height="100%" frameborder="0" src="javascript:void(function(){document.open();document.write(&quot;<!DOCTYPE html><html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}
body{margin:8px;font-family:sans-serif;font-size:13px;}p{margin:5px 0;}</style><link rel='stylesheet' type='text/css' href='/static/ueditor/themes/iframe.css'/></head><body class='view' ></body><script type='text/javascript'  id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant0'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);</script></html>&quot;);document.close();}())"></iframe></div><div id="edui1_bottombar" class="edui-editor-bottomContainer edui-default"><table class="edui-default"><tbody class="edui-default"><tr class="edui-default"><td id="edui1_elementpath" class="edui-editor-bottombar edui-default"><div class="edui-editor-breadcrumb">元素路径:</div></td><td id="edui1_wordcount" class="edui-editor-wordcount edui-default">字数统计</td><td id="edui1_scale" class="edui-editor-scale edui-default" style="display: none;"><div class="edui-editor-icon edui-default"></div></td></tr></tbody></table></div><div id="edui1_scalelayer" class="edui-default"></div></div></div> <div class="edit-btn"><button type="button" class="button">提交回答</button> <label data-v-0fe26aab="" for="commonCheckbox" class="checkbox"><input data-v-0fe26aab="" type="checkbox" id="commonCheckbox" name="commonCheckbox"> <i data-v-0fe26aab=""></i> 需要彈窗，請勾選</label></div></div></div> <div class="index-right"><div class="right-bk"><h3 class="com-title"><a href="javascript:;">我的最爱</a></h3> <div class="item-box clearfix"><span>錯誤，請您登入後再操作！</span></div></div> <div class="hotTag right-bk"><h3 class="com-title">热门标签</h3> <div class="hotTag-box item-box clearfix"><p>無數據</p></div></div> <div class="right-bk rank"><h3 class="com-title"><a href="javascript:;">冠軍排行榜</a></h3> <ul><li><div><span class="number">1</span> <img src="/static/timg.jpeg"> <span>王菊芳</span></div> <span class="score">612分</span></li><li><div><span class="number">2</span> <img src="/upload/head/10177.gif?time=1556271256"> <span>黃雪靜</span></div> <span class="score">258分</span></li><li><div><span class="number">3</span> <img src="/static/timg.jpeg"> <span>田霞</span></div> <span class="score">240分</span></li><li><div><span class="number">4</span> <img src="/static/timg.jpeg"> <span>曹超雲</span></div> <span class="score">168分</span></li><li><div><span class="number">5</span> <img src="/static/timg.jpeg"> <span>文宇</span></div> <span class="score">96分</span></li><li><div><span class="number">6</span> <img src="/upload/head/10217.png?time=1548059679"> <span>黃興興</span></div> <span class="score">78分</span></li><li><div><span class="number">7</span> <img src="/static/timg.jpeg"> <span>黃佳麗</span></div> <span class="score">62分</span></li><li><div><span class="number">8</span> <img src="/static/timg.jpeg"> <span>马泽鹏</span></div> <span class="score">46分</span></li><li><div><span class="number">9</span> <img src="/static/timg.jpeg"> <span>陳敏南</span></div> <span class="score">40分</span></li><li><div><span class="number">10</span> <img src="/static/timg.jpeg"> <span>Cs-刘婷</span></div> <span class="score">10分</span></li><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></ul></div> <div class="activeUser right-bk"><h3 class="com-title">活跃用户</h3> <div class="activeUser-box"><a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a></div></div> <img src="data:image/png;base64,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" class="to-top"></div></div> <!----> <!----> <!----> <div class="page-cnt footer"><p>T5客服知识库<span>|</span>zsk.591.com.tw</p></div></div></div><script type="text/javascript" src="/js/manifest.f192b.js"></script><script type="text/javascript" src="/js/vendor.17570.js"></script><script type="text/javascript" src="/js/app.58254.js"></script><div id="edui_fixedlayer" class="edui-default" style="position: fixed; left: 0px; top: 0px; width: 0px; height: 0px;"><div id="edui148" class="edui-popup  edui-bubble edui-default" onmousedown="return false;" style="display: none;"> <div id="edui148_body" class="edui-popup-body edui-default"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank" class="edui-default"></iframe> <div class="edui-shadow edui-default"></div> <div id="edui148_content" class="edui-popup-content edui-default">  </div> </div></div></div></body></html>