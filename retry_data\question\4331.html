<html class=" "><head><meta charset="utf-8"><title>591客服知識庫</title><link rel="stylesheet" href="/static/font/font-awesome.min.css"><link href="/css/app.e5aa4.css" rel="stylesheet"><script type="text/javascript" charset="utf-8" async="" src="/js/questionDetail.d0a6b.js"></script><style type="text/css">.que-detail-page .index-left .button{width:74px;height:32px;line-height:32px;font-size:13px;margin-right:10px}.que-detail-page .index-left .button:hover{color:#fff}.que-detail-page .detail-box{padding:20px 30px;border:1px solid #e5e5e5;border-radius:4px}.que-detail-page .detail-box h3{font-size:18px;color:#333;margin-bottom:10px}.que-detail-page .detail-box h3 span{color:#f77d24}.que-detail-page .detail-box .add-fav{cursor:pointer}.que-detail-page .d-b-menu{margin:15px 0 25px;font-size:13px;color:#999}.que-detail-page .d-b-menu b{display:inline-block;font-weight:400;margin:0 10px}.que-detail-page .d-b-menu .r{float:right;color:#f77d24}.que-detail-page .d-b-menu .r .fa{margin-right:5px;font-size:16px}.que-detail-page .d-b-cnt{margin-bottom:25px;font-size:13px;color:#666}.que-detail-page .submit-ask{display:block;margin:32px auto 0}.que-detail-page .reply-list{margin:30px 0 10px;overflow:hidden}.que-detail-page .reply-list .reply-item{padding:15px 30px 25px;margin-bottom:30px;border-radius:4px;border:1px solid #e5e5e5}.que-detail-page .reply-list .reply-item h4{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;line-height:50px;font-size:13px}.que-detail-page .reply-list .reply-item h4 img{width:40px;height:40px;border-radius:50%}.que-detail-page .reply-list .reply-item h4 .time{color:#999}.que-detail-page .reply-list .reply-item h4 .button{-ms-flex-item-align:center;align-self:center}.que-detail-page .reply-list .reply-item h4 .delete{color:#666;cursor:pointer}.que-detail-page .reply-list .reply-item .reply-detail{padding:20px 0 30px}.que-detail-page .more-answer{font-size:18px;color:#666;margin-bottom:5px}.que-detail-page .editor-content table{width:100%;border:1px solid #ddd}.que-detail-page .editor-content table td,.que-detail-page .editor-content table th{border:1px solid #ddd;padding:2px 8px;line-height:2}.que-detail-page .editor-content img{max-width:100%;vertical-align:text-bottom}.que-detail-page .edit-btn{text-align:center;margin-top:20px}.que-detail-page .edit-btn .edit-cancle{background:#aaa}</style><style type="text/css">.head-info{line-height:26px;background:#f5f5f5;font-size:13px;color:#999}.head-info a{margin-left:20px;color:#333}.head-search{margin-top:22px}.head-search img{float:left}.head-search .srh-box{float:left;margin:20px 0 0 20px}.head-search .srh-box .srh-input{float:left;width:260px;height:38px;line-height:38px;border:2px solid #37a508;border-radius:4px 0 0 4px}.head-search .srh-box a,.head-search .srh-box button{float:left;width:80px;height:38px;line-height:36px;font-size:16px;color:#fff}.head-search .srh-box .srh-btn{border-radius:0 4px 4px 0}.head-search .srh-box .ask-btn{margin-left:10px}.head-nav{float:left;margin:20px 0 0 60px;color:#fff;font-size:16px;line-height:36px}.head-nav a{display:block;height:100%;color:#333}.head-nav li{position:relative;float:left;margin-right:35px}.head-nav .active a,.head-nav li:hover a{color:#30b940}.head-nav .active:after,.head-nav li:hover:after{display:block;content:"";height:3px;width:100%;background:#30b940}.head-pic{width:100%;height:90px;margin-top:20px;background:url(/static/banner.png) top no-repeat}</style><style type="text/css">.top-breadcrumb{height:20px;margin-top:10px;margin-bottom:40px;font-size:13px;color:#666}.top-breadcrumb .t-b-icon{color:#30b940}.top-breadcrumb span{margin-left:8px}.top-breadcrumb span .fa{font-size:16px;margin-left:8px}.top-breadcrumb span:first-child{margin-left:0}.top-breadcrumb .now{color:#30b940}</style><script type="text/javascript" src="/static/ueditor/ueditor.config.js?20210319" id="configScriptTag"></script><script type="text/javascript" src="/static/ueditor/ueditor.all.js?20210319" id="editorScriptTag"></script><style type="text/css">.checkbox[data-v-0fe26aab]{word-spacing:4px;vertical-align:bottom;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.checkbox i[data-v-0fe26aab]{display:inline-block;width:14px;height:14px;border-radius:2px;border:1px solid #ccc;background:#fff;position:relative;top:1px}.checkbox input[data-v-0fe26aab]{opacity:0;width:0;height:0;margin:0}.checkbox input:checked+i[data-v-0fe26aab]{background:#30b940;border-color:#30b940}.checkbox input:checked+i[data-v-0fe26aab]:before{content:"";position:absolute;left:2px;top:1px;width:11px;height:6px;border-bottom:2px solid #fff;border-left:2px solid #fff;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}</style><style type="text/css">.popbox{position:fixed;top:50%;left:50%;z-index:1011;background:#fff;border-radius:6px;overflow:hidden}.popbox h3{font-size:16px;line-height:40px;padding:0 20px}.popbox h3 span{float:right;color:#666;font-size:13px;cursor:pointer}.popbox .pb-cnt{padding:0 20px;font-size:13px}.popbox .pb-btn-area{position:absolute;bottom:0;left:0;z-index:1;width:100%;text-align:right;padding:0 20px 20px 0}.popbox .pb-btn-area button{height:32px;line-height:32px;border:0;background:#fff;cursor:pointer;border-radius:6px;margin-left:15px;padding:0 15px}.popbox .pb-btn-area button.pb-btn-ok{background:#daf3cf;color:#279005}.popbox .pb-btn-area button.pb-btn-ok:hover{background:#279005;color:#fff}</style><style type="text/css">.shadow{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1010;background:#000}</style><style type="text/css">.footer{margin-top:70px;margin-bottom:20px;padding-top:20px;text-align:center;color:#999;border-top:1px solid #f5f5f5}.footer span{margin:0 10px}</style><script src="/static/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript" defer="defer"></script><link href="/static/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet"><script src="/static/ueditor/third-party/codemirror/codemirror.js" type="text/javascript" defer="defer"></script><link rel="stylesheet" type="text/css" href="/static/ueditor/third-party/codemirror/codemirror.css"><script src="/static/ueditor/third-party/zeroclipboard/ZeroClipboard.js" type="text/javascript" defer="defer"></script><style type="text/css">.activeUser,.favorite,.hotTag,.rank{margin-bottom:40px}</style><style type="text/css">.rank .rank-page{height:60px;line-height:60px;font-size:18px;margin-bottom:20px;text-align:center;background:#fafafa;border-radius:5px}.rank ul{padding:0 20px}.rank li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;border-bottom:1px dotted #f5f5f5;line-height:50px}.rank li .flag{position:relative;display:inline-block;width:20px;height:30px;background:#476472;vertical-align:middle;margin-right:12px;color:#fff;text-align:center;line-height:20px;font-size:13px;font-style:normal}.rank li .flag:after{content:"";position:absolute;bottom:-4px;left:-4px;border:14px solid transparent;border-bottom-color:#fff}.rank li:first-child .flag{background:#fb6757}.rank li:nth-child(2) .flag{background:#fac525}.rank li:nth-child(3) .flag{background:#00a9fb}.rank li img{width:36px;height:36px;border-radius:50%;margin-right:12px}</style><style type="text/css">.activeUser .activeUser-box a{display:inline-block;width:64px;height:64px;border-radius:50%;overflow:hidden;margin:0 6px 12px}</style><style type="text/css">.to-top{display:none;position:fixed;bottom:150px;z-index:5;width:50px;height:50px;cursor:pointer}</style></head><body style=""><div id="app" style=""><div class="que-detail-page" style=""><div class="wiki-hd"><div class="head-info"><p class="page-cnt">
      您好，欢迎来到591客服知识库
      <a href="https://www.591.com.tw/admin.php?new_url=https://zsk.591.com.tw">[请登入]</a> <!----></p></div> <div class="page-cnt head-search clearfix"><a href="/" class="router-link-active"><img src="/static/logo.png"></a> <ul class="head-nav clearfix"><li class="router-link-active active"><a href="/">591房屋交易网</a></li> <li class=""><a href="/life">客服充电站</a></li> <li class=""><a href="/notification">通知事项</a></li></ul> <div class="srh-box"><input type="text" placeholder="请输入搜索内容" class="srh-input"> <button type="button" class="button srh-btn">搜索</button> <a href="/ask/591" class="button button-orange ask-btn">提問</a></div></div> <div class="head-pic"></div></div> <div class="top-breadcrumb com-width"><span><a href="/collection/591/1209" class="">客服督導相關<i class="fa fa-angle-double-right"></i></a></span><span><a href="/collection/591/1209/1658" class="">業務作業指導<i class="fa fa-angle-double-right"></i></a></span><span><strong class="now">问题详情</strong></span></div> <div class="com-width clearfix" style=""><div class="index-left" style=""><div class="detail-box"><h3><!---->新建案與編輯配合的其他事務作業指導書</h3> <p class="clearfix d-b-menu"><span class="l">
            王菊芳&nbsp;&nbsp;&nbsp;浏览107次&nbsp;&nbsp;&nbsp;2020-11-05 14:58:58
          </span> <span class="r"><span class="add-fav"><i class="fa fa-heart"></i>加入最爱</span></span></p> <div class="d-b-cnt editor-content big-img-parent"><div><p><span style="font-family: 微软雅黑, Microsoft YaHei;">如題</span><br></p></div></div> <div class="d-b-button"><a href="/edit/591/4331" class="button">编辑</a> <button class="button button-orange">关闭提问</button></div></div> <div class="hr-line"></div> <ul class="reply-list big-img-parent"><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-05 15:05:08</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong>1、如何修改地圖位置</strong></span></p><p><strong><span style="font-size: 16px;">1.1<span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif;"> 什麼情況下需要修改建案地圖位置</span></span></strong></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">會員來回饋基地位址無誤，但地圖位置顯示有誤時，需請客服協助修改地圖位置</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif"><br></span></p><p><strong><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 16px;">1.2 操作步驟</span></strong><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif"><br></span></p><p><br></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">至新客服後台--修改建案地圖位置，使用建案ID搜尋看看是否能搜尋到這筆建案</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><img src="https://zsk.591.com.tw/upload/question/20210317/1615980414599287.png" alt="image.png" style="box-sizing: border-box; border: 0px; vertical-align: text-bottom; max-width: 100%; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif !important;"></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">如果不能搜尋到這筆建案，就只能手動修改</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><img src="https://zsk.591.com.tw/upload/question/20210317/1615980450698781.png" alt="image.png" style="box-sizing: border-box; border: 0px; vertical-align: text-bottom; max-width: 100%; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif !important;">如果要修改基地地址或接待中心地址選擇相應的修改類型</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><img src="https://zsk.591.com.tw/upload/question/20210317/1615980523345204.png" alt="image.png" style="box-sizing: border-box; border: 0px; vertical-align: text-bottom; max-width: 100%; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif !important;"></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">點擊修改即可</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><img src="https://zsk.591.com.tw/upload/question/20210317/1615980563490082.png" alt="image.png" style="box-sizing: border-box; border: 0px; vertical-align: text-bottom; max-width: 100%; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif !important;"></span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-05 15:07:36</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><h4><span style="font-family: 微软雅黑, Microsoft YaHei;"><span style="line-height: 155%; font-weight: normal; font-size: 16px;">2、新建案電話為空號檢測流程及方法</span></span></h4><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">2.1新建案空號確認規則（每天確認）</span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;"></span></strong></p><table cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td colspan="4" style="border: 1px solid windowtext; padding: 0px 7px;" width="930" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">廣告已完銷、已關閉、回收站中，根據廣告狀態做好記錄表格中，無需聯絡</span></p></td></tr><tr style=";height:12px"><td rowspan="3" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="75" height="12"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">廣告正常刊登中</span></p></td><td rowspan="3" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="66" valign="top" height="12"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">聯絡核實轉接電話是否無法接通</span></p></td><td colspan="2" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="788" valign="top" height="12"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">若可正常撥打做好記錄</span></p></td></tr><tr style=";height:47px"><td rowspan="2" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="100" height="47"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">若轉接電話無法接通，則聯絡室內電話是否可以接通</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="637" height="47"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">廣告內電話可以接通：說明轉接電話故障，記錄提交現場處理</span></p></td></tr><tr style=";height:12px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="637" height="12"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">廣告內電話也不可以接通：聯絡註冊電話請其修改聯絡電話（若廣告為編輯刊登，需請編輯聯絡案場修改電話）</span></p></td></tr></tbody></table><p><br></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;"><br></span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">2.2 操作步驟</span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;"></span></strong></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">進入新客服後臺→新建案→電話總數→空號→聯絡並做好記錄。請見圖</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif"><img src="/upload/question/20201105/1604560049236389.png" alt="圖片.png" style="width: 821px; height: 442px;" width="821" height="442"><br></span></p><p><img src="/upload/question/20201105/1604560152433509.png" alt="圖片.png"></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-05 15:13:44</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><strong>3、新建案系統異常檢測流程及方法</strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><strong><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif;">3.1 系統異常確認規則（週一到週五）</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><strong><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif;"></span></strong></span></p><table cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td colspan="4" style="border: 1px solid windowtext; padding: 0px 7px;" width="930" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">廣告已完銷、已關閉、回收站中，根據廣告狀態做好記錄表格中，無需聯絡</span></p></td></tr><tr style=";height:12px"><td rowspan="3" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="75" height="12"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">廣告正常刊登中</span></p></td><td rowspan="3" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="66" valign="top" height="12"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">聯絡核實轉接電話是否無法接通</span></p></td><td colspan="2" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="788" valign="top" height="12"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">若可正常撥打做好記錄</span></p></td></tr><tr style=";height:47px"><td rowspan="2" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="100" height="47"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">若轉接電話無法接通，則聯絡室內電話是否可以接通</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="637" height="47"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">廣告內電話可以接通：說明轉接電話故障，記錄提交現場處理</span></p></td></tr><tr style=";height:12px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="637" height="12"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">廣告內電話也不可以接通：聯絡註冊電話請其修改聯絡電話（若廣告為編輯刊登，需請編輯聯絡案場修改電話）</span></p></td></tr></tbody></table><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><strong><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif;"><br></span></strong></span><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">3.2操作步驟</span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;"></span></strong></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">進入新客服後臺→新建案→電話總數→系統異常→選擇日期對應的數位開啟→聯絡並做好記錄表格中。請見圖</span></p><p><img src="/upload/question/20201105/1604560416726243.png" alt="圖片.png" style="width: 817px; height: 520px;" width="817" height="520"><img src="/upload/question/20201105/1604560455932600.png" alt="圖片.png" style="width: 813px; height: 230px;" width="813" height="230"><img src="/upload/question/20201105/1604560491891179.png" alt="圖片.png"></p><p><a href="https://docs.google.com/spreadsheets/d/1PHHE5DC-r5jGWIRtY-EjblPzPFYiWyyJs0WrSr6go90/edit#gid=255645118"><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">新建案系統異常記錄表格</span></a></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">PS: </span><span style="font-size:13px;font-family: 'Microsoft YaHei',sans-serif;color:red">只處理週一到週五，當天處理前一天的，例如週一處理上週五的，週二處理週一的</span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-05 15:18:59</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;">4、</span></strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><strong>新增動態有什麼規範及注意事項</strong></span></p><p><strong><span style="font-size: 16px;"><span style="font-family: 微软雅黑, Microsoft YaHei;">4.1</span><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif;">什麼是動態</span></span></strong></p><p><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">該欄位元可以展示建案的最新進度，銷售情況，可售戶數等，動態審核通過後會在前臺展示，每條動態一天僅可修改兩次</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">&nbsp;<img src="/upload/question/20201105/1604560576320601.png" alt="圖片.png"></span></p><p><br></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;">4.2 新建案動態業務邏輯</span></strong></p><p><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">1)使用者新增動態後，會提交至【建案動態-統計清單】未審核狀態；</span></p><p><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">2)未通過審核的動態消息，推案管理顯示審核中，其他使用者無法看到；</span></p><p><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">3)審核通過：狀態變為已審核，推案管理顯示已審核，該使用者可修改2次，且其他用戶可見；</span></p><p><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">4)審核不通過：操作刪除即可，刪除僅會刪除該條動態，該動態進入【未通過清單】，推案管理顯示未通過，其他使用者不可見；</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif"><br></span></p><p><br><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif"></span><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;">4.3 新建案動態審核規範：</span></strong></p><p><span style="font-size: 14px; font-family: &quot;Microsoft YaHei&quot;, sans-serif;">1)內容需包括建案真實銷售狀況、可售戶數、可售格局、建案活動、廣告戶、樣品屋等資訊；</span></p><p><span style="font-size: 14px; font-family: &quot;Microsoft YaHei&quot;, sans-serif;">2)內容需與上條動態略作變化；</span></p><p><span style="font-size: 14px; font-family: &quot;Microsoft YaHei&quot;, sans-serif;">3)不得進行廣告宣傳動態（如有關鍵字：熱銷、精品、限量、精緻）；</span></p><p><span style="font-size: 14px; font-family: &quot;Microsoft YaHei&quot;, sans-serif;">4)不得為祝福及其他無意義資訊；</span></p><p><span style="font-size: 14px; font-family: &quot;Microsoft YaHei&quot;, sans-serif;">5)不得留有電話；</span></p><p><span style="font-size: 14px; font-family: &quot;Microsoft YaHei&quot;, sans-serif;"></span></p><table cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td colspan="2" style="border: 1px solid windowtext; padding: 0px 7px;" width="930" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">標準範例</span></p></td></tr><tr style=";height:51px"><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px; word-break: break-all;" width="930" valign="top" height="51"><img src="/upload/question/20201105/1604560692246730.png" alt="圖片.png"></td></tr><tr style=";height:47px"><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px; word-break: break-all;" width="930" valign="top" height="47"><img src="/upload/question/20201105/1604560703994161.png" alt="圖片.png"><br></td></tr><tr><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="930" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">不通過範例</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px; word-break: break-all;" width="465" valign="top"><img src="/upload/question/20201105/1604560714263911.png" alt="圖片.png"></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="465" valign="top"><img src="/upload/question/20201105/1604560722755693.png" alt="圖片.png"></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px; word-break: break-all;" width="465" valign="top"><img src="/upload/question/20201105/1604560729245038.png" alt="圖片.png"></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="465" valign="top"><img src="/upload/question/20201105/1604560737101754.png" alt="圖片.png"></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="465" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">動態內容</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="465" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">原因</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="465" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">2018/08/23 &nbsp; </span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">大台南地區豪大雨皆無淹水狀況</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="465" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">動態放的是與工程進度及銷售有關聯的</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="465" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">全新完工 &nbsp; 歡迎鑒賞</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="465"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">歡迎鑒賞有點擦邊球 就字面來看 &nbsp; 只有藝術品才有資格用"鑒賞"字眼 所以算廣告詞 &nbsp; 不允用</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="465"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">15</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">坪只剩下最後一戶 請把握最後機會</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="465" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">請把握最後機會不ok</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="465" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">電梯已經好嘍，目標在鎖A棟基座大理石、可售戶數不多了，你還不快點來</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="465" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">你還不快點來不Ok</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="465"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">絕版ㄧ字頭景觀公園宅，最後6戶72坪最大心4房，單層兩戶最單純，雙主臥最大心，超低公設比最實用。</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="465" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">絕版ㄧ字頭景觀公園宅、最實用</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="465" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">目前陸續對保中 10月底將陸續交屋；現場銷售9成5，熱銷最後搶席 25坪1+1房；現場有實品屋可供參觀。</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="465" valign="top"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">熱銷、搶席屬於廣告字眼</span></p></td></tr></tbody></table><p><span style="font-size: 14px; font-family: &quot;Microsoft YaHei&quot;, sans-serif;"></span><br></p><p><span style="font-size: 14px; font-family: &quot;Microsoft YaHei&quot;, sans-serif;"><br></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"></span><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-05 15:20:52</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">4.4 操作步驟</span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;"></span></strong></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">進入新客服後臺→新建案→建案動態→“通過”或“不通過”。請見圖</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><img src="/upload/question/20201105/1604560825510430.png" alt="圖片.png" style="width: 812px; height: 485px;" width="812" height="485"><br></span></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;"><br></span></strong><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-05 15:24:31</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;">5、優化訊息</span></strong></p><p><strong><span style="font-size: 16px;"><span style="font-family: 微软雅黑, Microsoft YaHei;">5.1</span><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif;"> 什麼是優惠資訊</span></span></strong></p><p><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">即填寫廣告資訊時的【好康活動】，填寫審核通過後將在建案列表頁展示</span></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;"><br>5.2 建案優惠資訊填寫規則：</span></strong></p><p><span style="font-size: 14px; font-family: &quot;Microsoft YaHei&quot;, sans-serif;">*不超過15個字</span></p><p><span style="font-size: 14px; font-family: &quot;Microsoft YaHei&quot;, sans-serif;">*真實有效的優惠資訊：促銷活動(SP)、促銷方案(如低首付)、賞屋贈品等</span></p><p><span style="font-size: 14px; font-family: &quot;Microsoft YaHei&quot;, sans-serif;">*審核後方可顯示，若未顯示請確認是否未審核或審核未通過唷</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;"></span></p><p><img src="/upload/question/20201105/1604561141528570.png" alt="圖片.png"></p><p><br></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;">5.3操作步驟</span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei;"></span></strong></p><p><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">進入客服後臺→客服相關→資料審核→建案新刊登→優惠資訊管理。請見圖</span></p><p><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;"><img src="/upload/question/20201105/1604561224245163.png" alt="圖片.png"><br></span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-05 15:30:43</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;">6、如何上傳720環景圖</span></strong></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;">6.1 什麼是全景賞屋及高空賞屋</span></strong></p><p><span style="font-size: 14px; font-family: &quot;Microsoft YaHei&quot;, sans-serif;">*室內全景賞屋又稱室內環景720賞屋，主要拍攝室內的屋況</span></p><p><span style="font-size: 14px; font-family: &quot;Microsoft YaHei&quot;, sans-serif;">*高空環景賞屋又稱室外環景720賞屋，主要是拍攝大樓的座標及周邊的路況</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">&nbsp;</span></p><p><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px;">6.2 會員如何上傳720賞屋</span></strong></p><p><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">會員若需要有拍攝室內720賞屋或高空720，需請會員留下聯絡方式，請編輯聯絡上門拍攝（客服跟會員說明到此，然後提交到群內即可）</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">&nbsp;</span></p><p><strong><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 16px;">6.3 環景賞屋物件需符合以下條件：</span></strong></p><p><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">1、建案戶型有格局圖（必需條件）</span></p><p><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">2、新案、首購案為主，換屋案需是市場熱度高的建案（符合用戶群）</span></p><p><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">3、建案相冊除封面及宣傳圖，須有1類以上其他相冊。編輯部刊登建案為佳（不配合則不提供）</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">&nbsp;</span></p><p><strong><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 16px;">6.4 室內環景720客服操作步驟（一月2-3次）</span></strong></p><p><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">已拍攝好並製作完成的環景賞屋圖片，由產品同事整理資料，並由客服上傳至廣告中。</span></p><p><strong><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">第一步：產品同事會群內通知需上傳720，\192.168.8.29PublicFile591房屋交易網（臺灣）【05】產品研發2018年於泳波T5-客服配合需求-20170703全景上刊</span></strong></p><p><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;"><img src="/upload/question/20201105/1604561441243749.png" alt="圖片.png"></span></p><p><strong><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;"><br></span></strong></p><p><strong><span style="font-size: 14px;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif;">第二步：</span><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif;">客服打開共用文件，點擊每一筆建案的第一個文件夾，</span></span></strong></p><p><strong><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">例如：1162893dspacelink</span></strong></p><p><strong><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;"><img src="/upload/question/20201105/1604561517425377.png" alt="圖片.png"><br></span></strong></p><p><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;"><br></span><img src="/upload/question/20201105/1604561534520041.png" alt="圖片.png"><br></p><p><strong><span style="font-size: 14px;"><span style="font-family: 微软雅黑, Microsoft YaHei;"><br></span>第三步：<span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif;">進入修改頁面的建案相冊中，點擊“全景賞屋置圖”上傳720環景圖</span></span></strong></p><p><strong><span style="font-size: 14px;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif;"><img src="/upload/question/20201105/1604561576268837.png" alt="圖片.png"><br></span></span></strong></p><p><strong><span style="font-size: 14px;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif;"><br></span></span></strong></p><p><strong><span style="font-size: 14px;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif;"></span></span></strong></p><p><strong><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">第四步：從共用文件中選擇一張客廳正面照片上傳（亦可先將室內照片保存至電腦桌面，比較容易找），提交後就會顯示720環景圖</span></strong></p><p><img src="/upload/question/20201105/1604561605314604.png" alt="圖片.png"><br></p><p><strong><span style="font-size: 14px;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif;"><br></span></span></strong><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-05 15:34:29</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><h3><span style="line-height: 172%; font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 16px;">7、需要編輯配合處理的情況及辦法</span></h3><table width="109" cellspacing="0" cellpadding="0"><tbody><tr style=";height:182px" class="firstRow"><td style="border: 1px solid black; padding: 1px 1px 0px;" width="15" height="182"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">1</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">、會員來電詢問是否可以請編輯幫忙刊登廣告（包含預推案）嗎？</span></p></td><td style="border-color: black black black currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 1px 1px 0px;" width="34" height="182"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">答：請記錄會員的聯絡方式，建案名稱，建案地區，提交到【新建案審核群】請編輯聯絡</span></p></td><td style="border-color: black black black currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 1px 1px 0px; word-break: break-all;" width="49" valign="top" height="182"><img src="/upload/question/20201105/1604561710338920.png" alt="圖片.png"></td></tr><tr style=";height:129px"><td style="border-color: currentcolor black black; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 1px 1px 0px;" width="15" height="129"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">2</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">、會員來電回饋編輯刊登的廣告是否可以改為自行刊登？</span></p></td><td style="border-color: currentcolor black black currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 1px 1px 0px;" width="34" height="129"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">答：請記錄會員的聯絡方式，建案名稱，建案地區，提交到【新建案審核群】請編輯確認，同意後方可刪除編輯刊登的物件</span></p></td><td style="border-color: currentcolor black black currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 1px 1px 0px;" width="49" valign="top" height="129"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">建案名稱：春禹朗朗(H124022)</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">諮訊電話：06-6569717</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">刊登編輯：張一笙</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">刊登區域：台南市新營區</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">編輯，下午好，案場表示要求自行刊登，請問可以關閉建案嗎</span></p></td></tr><tr style=";height:218px"><td style="border-color: currentcolor black black; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 1px 1px 0px;" width="15" height="218"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">3</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">、會員來電回饋編輯刊登的廣告已經完銷要求下架？</span></p></td><td style="border-color: currentcolor black black currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 1px 1px 0px;" width="34" height="218"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">答：客服需回撥案場電話核實情況是否屬實，並記錄會員的聯絡方式，建案名稱，建案地區，提交到【新建案審核群】請編輯確認，同意後方可完銷下架</span></p></td><td style="border-color: currentcolor black black currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 1px 1px 0px; word-break: break-all;" width="49" valign="top" height="218"><img src="/upload/question/20201105/1604561740146313.png" alt="圖片.png"></td></tr><tr style=";height:175px"><td style="border-color: currentcolor black black; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 1px 1px 0px;" width="15" height="175"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">4</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">、會員來電或信箱回饋編輯刊登的廣告需要修改資料？</span></p></td><td style="border-color: currentcolor black black currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 1px 1px 0px;" width="34" height="175"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">答：客服需回撥案場電話核實情況是否屬實，若屬實可直接修改後，再提交到【新建案審核群】通知編輯知曉即可</span></p></td><td style="border-color: currentcolor black black currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 1px 1px 0px; word-break: break-all;" width="49" valign="top" height="175"><img src="/upload/question/20201105/1604561757591533.png" alt="圖片.png"></td></tr><tr style=";height:94px"><td style="border-color: currentcolor black black; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 1px 1px 0px;" width="15" height="94"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">5</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">、會員來電表示編輯未經委託就擅自刊登</span></p></td><td style="border-color: currentcolor black black currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 1px 1px 0px;" width="34" height="94"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">答：請記錄會員的聯絡方式，建案名稱，建案地區，提交到【新建案審核群】請編輯確認，同意後方可暫時關閉物件，後再請編輯持續跟進該建案</span></p></td><td style="border-color: currentcolor black black currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 1px 1px 0px;" width="49" valign="top" height="94"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">建案名稱：春禹朗朗(H124022)</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">諮訊電話：06-6569717</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">刊登編輯：張一笙</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">刊登區域：台南市新營區</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">編輯，下午好，案場表示未委託編輯刊登，請問可以關閉建案嗎</span></p></td></tr><tr style=";height:150px"><td style="border-color: currentcolor black black; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 1px 1px 0px;" width="15" height="150"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">6</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">、會員來電詢問想要上591開箱文要怎麼處理？</span></p></td><td style="border-color: currentcolor black black currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 1px 1px 0px;" width="34" height="150"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">答：請記錄會員的聯絡方式，建案名稱，建案地區，提交到【新建案審核群】請編輯聯絡<br> &nbsp; 目前開箱文僅臺北、新北、桃園、台中、台南、高雄、新竹地區建案可寫開箱文</span></p></td><td style="border-color: currentcolor black black currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 1px 1px 0px;" width="49" valign="top" height="150"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">建案名稱：春禹朗朗(H124022)</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">諮訊電話：06-6569717</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">刊登區域：台南市新營區</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">編輯，下午好，09*********陳小姐表示想要上591開箱文，請幫忙聯絡，謝謝</span></p></td></tr><tr style=";height:110px"><td style="border-color: currentcolor black black; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 1px 1px 0px;" width="15" height="110"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">7</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">、會員來電詢問想要上傳720看屋要怎麼處理？</span></p></td><td style="border-color: currentcolor black black currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 1px 1px 0px;" width="34" height="110"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">答：請記錄會員的聯絡方式，建案名稱，建案地區，提交到【新建案審核群】請編輯聯絡</span></p></td><td style="border-color: currentcolor black black currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 1px 1px 0px;" width="49" valign="top" height="110"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">建案名稱：春禹朗朗(H124022)</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">諮訊電話：06-6569717</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">刊登區域：台南市新營區</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">編輯，下午好，09*********陳小姐表示想要上傳720環景圖，請幫忙聯絡，謝謝</span></p></td></tr><tr style=";height:156px"><td style="border-color: currentcolor black black; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 1px 1px 0px;" width="15" height="156"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">8</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">、會員來電詢問想要修改室內720素材要怎麼處理？</span></p></td><td style="border-color: currentcolor black black currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 1px 1px 0px;" width="34" height="156"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">*</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">改圖片，我們客服可以直接協助修改<br> &nbsp; *改素材，格局改變，需記錄會員的聯絡方式，建案名稱提交到【新建案審核群】請編輯聯絡廠商重新拍<br> &nbsp; *完銷，不允許修改，需記錄會員的聯絡方式，建案名稱提交到【新建案審核群】請編輯聯絡廠商進行備註</span></p></td><td style="border-color: currentcolor black black currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 1px 1px 0px;" width="49" valign="top" height="156"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">建案名稱：春禹朗朗(H124022)</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">編輯，下午好，09*********陳小姐表示需要修改720素材，請幫忙聯絡下，謝謝</span></p></td></tr><tr style=";height:59px"><td style="border-color: currentcolor black black; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 1px 1px 0px;" width="15" height="59"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">9</span><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">、會員來電詢問自己的建案想要參加賞屋團要怎麼處理？</span></p></td><td style="border-color: currentcolor black black currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 1px 1px 0px;" width="34" height="59"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">答：請記錄會員的聯絡方式，建案名稱，建案地區，提交到【新建案審核群】請編輯聯絡</span></p></td><td style="border-color: currentcolor black black currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 1px 1px 0px;" width="49" valign="top" height="59"><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">建案名稱：春禹朗朗(H124022)</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">諮訊電話：06-6569717</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">刊登區域：台南市新營區</span></p><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">編輯，下午好，09*********陳小姐表示想參加591賞屋團，請幫忙聯絡下，謝謝</span></p></td></tr></tbody></table></div></div> <div><span class="button">我要編輯</span></div></li></ul> <h3 class="more-answer">我要補充</h3> <div style=""><div id="editor_99581436195129360" class="edui-default" style=""><div id="edui1" class="edui-editor  edui-default" style="width: 100%; z-index: 999;"><div id="edui1_toolbarbox" class="edui-editor-toolbarbox edui-default"><div id="edui1_toolbarboxouter" class="edui-editor-toolbarboxouter edui-default"><div class="edui-editor-toolbarboxinner edui-default"><div id="edui2" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui2&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui3" class="edui-box edui-button edui-for-fullscreen edui-default"><div id="edui3_state" onmousedown="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui3_body" unselectable="on" title="全屏" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui3&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui3&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui4" class="edui-box edui-button edui-for-source edui-default"><div id="edui4_state" onmousedown="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui4_body" unselectable="on" title="源代码" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui4&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui4&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui5" class="edui-box edui-button edui-for-undo edui-default"><div id="edui5_state" onmousedown="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui5_body" unselectable="on" title="撤销" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui5&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui5&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui6" class="edui-box edui-button edui-for-redo edui-default"><div id="edui6_state" onmousedown="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui6_body" unselectable="on" title="重做" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui6&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui6&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui7" class="edui-box edui-button edui-for-simpleupload edui-default"><div id="edui7_state" onmousedown="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui7_body" unselectable="on" title="单图上传" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui7&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui7&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui8" class="edui-box edui-button edui-for-imageleft edui-default"><div id="edui8_state" onmousedown="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui8_body" unselectable="on" title="左浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui8&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui8&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui9" class="edui-box edui-button edui-for-imageright edui-default"><div id="edui9_state" onmousedown="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui9_body" unselectable="on" title="右浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui9&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui9&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui10" class="edui-box edui-button edui-for-imagecenter edui-default"><div id="edui10_state" onmousedown="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui10_body" unselectable="on" title="居中" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui10&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui10&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui11" class="edui-box edui-separator  edui-default"></div><div id="edui12" class="edui-box edui-button edui-for-date edui-default"><div id="edui12_state" onmousedown="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui12_body" unselectable="on" title="日期" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui12&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui12&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui13" class="edui-box edui-button edui-for-time edui-default"><div id="edui13_state" onmousedown="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui13_body" unselectable="on" title="时间" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui13&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui13&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui14" class="edui-box edui-button edui-for-horizontal edui-default"><div id="edui14_state" onmousedown="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui14_body" unselectable="on" title="分隔线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui14&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui14&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui15" class="edui-box edui-separator  edui-default"></div><div id="edui16" class="edui-box edui-splitbutton edui-for-inserttable edui-default"><div title="插入表格" id="edui16_state" onmousedown="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui16_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onArrowClick();"></div></div></div></div><div id="edui19" class="edui-box edui-button edui-for-deletetable edui-default"><div id="edui19_state" onmousedown="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui19_body" unselectable="on" title="删除表格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui19&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui19&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui20" class="edui-box edui-button edui-for-insertrow edui-default"><div id="edui20_state" onmousedown="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui20_body" unselectable="on" title="前插入行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui20&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui20&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui21" class="edui-box edui-button edui-for-insertcol edui-default"><div id="edui21_state" onmousedown="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui21_body" unselectable="on" title="前插入列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui21&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui21&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui22" class="edui-box edui-button edui-for-mergeright edui-default"><div id="edui22_state" onmousedown="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui22_body" unselectable="on" title="右合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui22&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui22&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui23" class="edui-box edui-button edui-for-mergedown edui-default"><div id="edui23_state" onmousedown="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui23_body" unselectable="on" title="下合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui23&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui23&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui24" class="edui-box edui-button edui-for-deleterow edui-default"><div id="edui24_state" onmousedown="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui24_body" unselectable="on" title="删除行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui24&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui24&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui25" class="edui-box edui-button edui-for-deletecol edui-default"><div id="edui25_state" onmousedown="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui25_body" unselectable="on" title="删除列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui25&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui25&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui26" class="edui-box edui-button edui-for-splittorows edui-default"><div id="edui26_state" onmousedown="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui26_body" unselectable="on" title="拆分成行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui26&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui26&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui27" class="edui-box edui-button edui-for-splittocols edui-default"><div id="edui27_state" onmousedown="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui27_body" unselectable="on" title="拆分成列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui27&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui27&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui28" class="edui-box edui-button edui-for-splittocells edui-default"><div id="edui28_state" onmousedown="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui28_body" unselectable="on" title="完全拆分单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui28&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui28&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui29" class="edui-box edui-button edui-for-mergecells edui-default"><div id="edui29_state" onmousedown="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui29_body" unselectable="on" title="合并多个单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui29&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui29&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui36" class="edui-box edui-button edui-for-edittable edui-default"><div id="edui36_state" onmousedown="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui36_body" unselectable="on" title="表格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui36&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui36&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui41" class="edui-box edui-button edui-for-edittd edui-default"><div id="edui41_state" onmousedown="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui41_body" unselectable="on" title="单元格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui41&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui41&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui42" class="edui-box edui-separator  edui-default"></div><div id="edui43" class="edui-box edui-menubutton edui-for-lineheight edui-default"><div title="行间距" id="edui43_state" onmousedown="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui43_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onArrowClick();"></div></div></div></div><div id="edui52" class="edui-box edui-menubutton edui-for-rowspacingtop edui-default"><div title="段前距" id="edui52_state" onmousedown="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui52_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onArrowClick();"></div></div></div></div><div id="edui59" class="edui-box edui-menubutton edui-for-rowspacingbottom edui-default"><div title="段后距" id="edui59_state" onmousedown="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui59_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onArrowClick();"></div></div></div></div></div><div id="edui66" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui66&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui67" class="edui-box edui-combox edui-for-fontfamily edui-default"><div title="字体" id="edui67_state" onmousedown="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui67_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onButtonClick(event, this);">字体</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onArrowClick();"></div></div></div></div><div id="edui80" class="edui-box edui-combox edui-for-customstyle edui-default"><div title="自定义标题" id="edui80_state" onmousedown="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui80_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onButtonClick(event, this);">自定义标题</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onArrowClick();"></div></div></div></div><div id="edui86" class="edui-box edui-combox edui-for-paragraph edui-default"><div title="段落格式" id="edui86_state" onmousedown="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui86_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onButtonClick(event, this);">段落格式</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onArrowClick();"></div></div></div></div><div id="edui95" class="edui-box edui-combox edui-for-fontsize edui-default"><div title="字号" id="edui95_state" onmousedown="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui95_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onButtonClick(event, this);">字号</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onArrowClick();"></div></div></div></div><div id="edui110" class="edui-box edui-button edui-for-link edui-default"><div id="edui110_state" onmousedown="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui110_body" unselectable="on" title="超链接" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui110&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui110&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui111" class="edui-box edui-button edui-for-indent edui-default"><div id="edui111_state" onmousedown="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui111_body" unselectable="on" title="首行缩进" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui111&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui111&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui112" class="edui-box edui-button edui-for-bold edui-default"><div id="edui112_state" onmousedown="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui112_body" unselectable="on" title="加粗" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui112&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui112&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui113" class="edui-box edui-splitbutton edui-for-forecolor edui-default edui-colorbutton"><div title="字体颜色" id="edui113_state" onmousedown="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui113_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui113_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onArrowClick();"></div></div></div></div><div id="edui116" class="edui-box edui-splitbutton edui-for-backcolor edui-default edui-colorbutton"><div title="背景色" id="edui116_state" onmousedown="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui116_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui116_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onArrowClick();"></div></div></div></div><div id="edui119" class="edui-box edui-button edui-for-italic edui-default"><div id="edui119_state" onmousedown="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui119_body" unselectable="on" title="斜体" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui119&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui119&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui120" class="edui-box edui-button edui-for-underline edui-default"><div id="edui120_state" onmousedown="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui120_body" unselectable="on" title="下划线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui120&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui120&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui121" class="edui-box edui-button edui-for-strikethrough edui-default"><div id="edui121_state" onmousedown="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui121_body" unselectable="on" title="删除线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui121&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui121&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui122" class="edui-box edui-button edui-for-superscript edui-default"><div id="edui122_state" onmousedown="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui122_body" unselectable="on" title="上标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui122&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui122&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui123" class="edui-box edui-button edui-for-subscript edui-default"><div id="edui123_state" onmousedown="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui123_body" unselectable="on" title="下标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui123&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui123&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui124" class="edui-box edui-button edui-for-removeformat edui-default"><div id="edui124_state" onmousedown="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui124_body" unselectable="on" title="清除格式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui124&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui124&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui125" class="edui-box edui-button edui-for-blockquote edui-default"><div id="edui125_state" onmousedown="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui125_body" unselectable="on" title="引用" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui125&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui125&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui126" class="edui-box edui-button edui-for-pasteplain edui-default"><div id="edui126_state" onmousedown="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui126_body" unselectable="on" title="纯文本粘贴模式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui126&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui126&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui127" class="edui-box edui-menubutton edui-for-insertorderedlist edui-default"><div title="有序列表" id="edui127_state" onmousedown="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui127_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onArrowClick();"></div></div></div></div><div id="edui140" class="edui-box edui-menubutton edui-for-insertunorderedlist edui-default"><div title="无序列表" id="edui140_state" onmousedown="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui140_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onArrowClick();"></div></div></div></div><div id="edui147" class="edui-box edui-button edui-for-cleardoc edui-default"><div id="edui147_state" onmousedown="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui147_body" unselectable="on" title="清空文档" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui147&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui147&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div></div></div></div><div id="edui1_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;"><div id="edui1_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui1&quot;].showWordImageDialog();">点击上传</div><div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui1&quot;].hideToolbarMsg();">x</div><div id="edui1_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div><div style="height:0;overflow:hidden;clear:both;" class="edui-default"></div></div><div id="edui1_message_holder" class="edui-editor-messageholder edui-default"></div></div><div id="edui1_iframeholder" class="edui-editor-iframeholder edui-default" style="width: 100%; height: 300px; z-index: 999; overflow: hidden;"><iframe id="ueditor_0" width="100%" height="100%" frameborder="0" src="javascript:void(function(){document.open();document.write(&quot;<!DOCTYPE html><html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}
body{margin:8px;font-family:sans-serif;font-size:13px;}p{margin:5px 0;}</style><link rel='stylesheet' type='text/css' href='/static/ueditor/themes/iframe.css'/></head><body class='view' ></body><script type='text/javascript'  id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant0'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);</script></html>&quot;);document.close();}())"></iframe></div><div id="edui1_bottombar" class="edui-editor-bottomContainer edui-default"><table class="edui-default"><tbody class="edui-default"><tr class="edui-default"><td id="edui1_elementpath" class="edui-editor-bottombar edui-default"><div class="edui-editor-breadcrumb">元素路径:</div></td><td id="edui1_wordcount" class="edui-editor-wordcount edui-default">字数统计</td><td id="edui1_scale" class="edui-editor-scale edui-default" style="display: none;"><div class="edui-editor-icon edui-default"></div></td></tr></tbody></table></div><div id="edui1_scalelayer" class="edui-default"></div></div></div> <div class="edit-btn"><button type="button" class="button">提交回答</button> <label data-v-0fe26aab="" for="commonCheckbox" class="checkbox"><input data-v-0fe26aab="" type="checkbox" id="commonCheckbox" name="commonCheckbox"> <i data-v-0fe26aab=""></i> 需要彈窗，請勾選</label></div></div></div> <div class="index-right"><div class="right-bk"><h3 class="com-title"><a href="javascript:;">我的最爱</a></h3> <div class="item-box clearfix"><span>錯誤，請您登入後再操作！</span></div></div> <div class="hotTag right-bk"><h3 class="com-title">热门标签</h3> <div class="hotTag-box item-box clearfix"><p>無數據</p></div></div> <div class="right-bk rank"><h3 class="com-title"><a href="javascript:;">冠軍排行榜</a></h3> <ul><li>暫無數據</li></ul></div> <div class="activeUser right-bk"><h3 class="com-title">活跃用户</h3> <div class="activeUser-box"><a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a></div></div> <img src="data:image/png;base64,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" class="to-top"></div></div> <!----> <!----> <!----> <div class="page-cnt footer"><p>T5客服知识库<span>|</span>zsk.591.com.tw</p></div></div></div><script type="text/javascript" src="/js/manifest.f192b.js"></script><script type="text/javascript" src="/js/vendor.17570.js"></script><script type="text/javascript" src="/js/app.58254.js"></script><div id="edui_fixedlayer" class="edui-default" style="position: fixed; left: 0px; top: 0px; width: 0px; height: 0px;"><div id="edui148" class="edui-popup  edui-bubble edui-default" onmousedown="return false;" style="display: none;"> <div id="edui148_body" class="edui-popup-body edui-default"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank" class="edui-default"></iframe> <div class="edui-shadow edui-default"></div> <div id="edui148_content" class="edui-popup-content edui-default">  </div> </div></div></div></body></html>