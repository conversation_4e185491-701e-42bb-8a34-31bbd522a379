<html class=" "><head><meta charset="utf-8"><title>591客服知識庫</title><link rel="stylesheet" href="/static/font/font-awesome.min.css"><link href="/css/app.e5aa4.css" rel="stylesheet"><script type="text/javascript" charset="utf-8" async="" src="/js/questionDetail.d0a6b.js"></script><style type="text/css">.que-detail-page .index-left .button{width:74px;height:32px;line-height:32px;font-size:13px;margin-right:10px}.que-detail-page .index-left .button:hover{color:#fff}.que-detail-page .detail-box{padding:20px 30px;border:1px solid #e5e5e5;border-radius:4px}.que-detail-page .detail-box h3{font-size:18px;color:#333;margin-bottom:10px}.que-detail-page .detail-box h3 span{color:#f77d24}.que-detail-page .detail-box .add-fav{cursor:pointer}.que-detail-page .d-b-menu{margin:15px 0 25px;font-size:13px;color:#999}.que-detail-page .d-b-menu b{display:inline-block;font-weight:400;margin:0 10px}.que-detail-page .d-b-menu .r{float:right;color:#f77d24}.que-detail-page .d-b-menu .r .fa{margin-right:5px;font-size:16px}.que-detail-page .d-b-cnt{margin-bottom:25px;font-size:13px;color:#666}.que-detail-page .submit-ask{display:block;margin:32px auto 0}.que-detail-page .reply-list{margin:30px 0 10px;overflow:hidden}.que-detail-page .reply-list .reply-item{padding:15px 30px 25px;margin-bottom:30px;border-radius:4px;border:1px solid #e5e5e5}.que-detail-page .reply-list .reply-item h4{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;line-height:50px;font-size:13px}.que-detail-page .reply-list .reply-item h4 img{width:40px;height:40px;border-radius:50%}.que-detail-page .reply-list .reply-item h4 .time{color:#999}.que-detail-page .reply-list .reply-item h4 .button{-ms-flex-item-align:center;align-self:center}.que-detail-page .reply-list .reply-item h4 .delete{color:#666;cursor:pointer}.que-detail-page .reply-list .reply-item .reply-detail{padding:20px 0 30px}.que-detail-page .more-answer{font-size:18px;color:#666;margin-bottom:5px}.que-detail-page .editor-content table{width:100%;border:1px solid #ddd}.que-detail-page .editor-content table td,.que-detail-page .editor-content table th{border:1px solid #ddd;padding:2px 8px;line-height:2}.que-detail-page .editor-content img{max-width:100%;vertical-align:text-bottom}.que-detail-page .edit-btn{text-align:center;margin-top:20px}.que-detail-page .edit-btn .edit-cancle{background:#aaa}</style><style type="text/css">.head-info{line-height:26px;background:#f5f5f5;font-size:13px;color:#999}.head-info a{margin-left:20px;color:#333}.head-search{margin-top:22px}.head-search img{float:left}.head-search .srh-box{float:left;margin:20px 0 0 20px}.head-search .srh-box .srh-input{float:left;width:260px;height:38px;line-height:38px;border:2px solid #37a508;border-radius:4px 0 0 4px}.head-search .srh-box a,.head-search .srh-box button{float:left;width:80px;height:38px;line-height:36px;font-size:16px;color:#fff}.head-search .srh-box .srh-btn{border-radius:0 4px 4px 0}.head-search .srh-box .ask-btn{margin-left:10px}.head-nav{float:left;margin:20px 0 0 60px;color:#fff;font-size:16px;line-height:36px}.head-nav a{display:block;height:100%;color:#333}.head-nav li{position:relative;float:left;margin-right:35px}.head-nav .active a,.head-nav li:hover a{color:#30b940}.head-nav .active:after,.head-nav li:hover:after{display:block;content:"";height:3px;width:100%;background:#30b940}.head-pic{width:100%;height:90px;margin-top:20px;background:url(/static/banner.png) top no-repeat}</style><style type="text/css">.top-breadcrumb{height:20px;margin-top:10px;margin-bottom:40px;font-size:13px;color:#666}.top-breadcrumb .t-b-icon{color:#30b940}.top-breadcrumb span{margin-left:8px}.top-breadcrumb span .fa{font-size:16px;margin-left:8px}.top-breadcrumb span:first-child{margin-left:0}.top-breadcrumb .now{color:#30b940}</style><script type="text/javascript" src="/static/ueditor/ueditor.config.js?20210319" id="configScriptTag"></script><script type="text/javascript" src="/static/ueditor/ueditor.all.js?20210319" id="editorScriptTag"></script><style type="text/css">.checkbox[data-v-0fe26aab]{word-spacing:4px;vertical-align:bottom;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.checkbox i[data-v-0fe26aab]{display:inline-block;width:14px;height:14px;border-radius:2px;border:1px solid #ccc;background:#fff;position:relative;top:1px}.checkbox input[data-v-0fe26aab]{opacity:0;width:0;height:0;margin:0}.checkbox input:checked+i[data-v-0fe26aab]{background:#30b940;border-color:#30b940}.checkbox input:checked+i[data-v-0fe26aab]:before{content:"";position:absolute;left:2px;top:1px;width:11px;height:6px;border-bottom:2px solid #fff;border-left:2px solid #fff;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}</style><style type="text/css">.popbox{position:fixed;top:50%;left:50%;z-index:1011;background:#fff;border-radius:6px;overflow:hidden}.popbox h3{font-size:16px;line-height:40px;padding:0 20px}.popbox h3 span{float:right;color:#666;font-size:13px;cursor:pointer}.popbox .pb-cnt{padding:0 20px;font-size:13px}.popbox .pb-btn-area{position:absolute;bottom:0;left:0;z-index:1;width:100%;text-align:right;padding:0 20px 20px 0}.popbox .pb-btn-area button{height:32px;line-height:32px;border:0;background:#fff;cursor:pointer;border-radius:6px;margin-left:15px;padding:0 15px}.popbox .pb-btn-area button.pb-btn-ok{background:#daf3cf;color:#279005}.popbox .pb-btn-area button.pb-btn-ok:hover{background:#279005;color:#fff}</style><style type="text/css">.shadow{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1010;background:#000}</style><style type="text/css">.footer{margin-top:70px;margin-bottom:20px;padding-top:20px;text-align:center;color:#999;border-top:1px solid #f5f5f5}.footer span{margin:0 10px}</style><script src="/static/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript" defer="defer"></script><link href="/static/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet"><script src="/static/ueditor/third-party/codemirror/codemirror.js" type="text/javascript" defer="defer"></script><link rel="stylesheet" type="text/css" href="/static/ueditor/third-party/codemirror/codemirror.css"><script src="/static/ueditor/third-party/zeroclipboard/ZeroClipboard.js" type="text/javascript" defer="defer"></script><style type="text/css">.activeUser,.favorite,.hotTag,.rank{margin-bottom:40px}</style><style type="text/css">.rank .rank-page{height:60px;line-height:60px;font-size:18px;margin-bottom:20px;text-align:center;background:#fafafa;border-radius:5px}.rank ul{padding:0 20px}.rank li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;border-bottom:1px dotted #f5f5f5;line-height:50px}.rank li .flag{position:relative;display:inline-block;width:20px;height:30px;background:#476472;vertical-align:middle;margin-right:12px;color:#fff;text-align:center;line-height:20px;font-size:13px;font-style:normal}.rank li .flag:after{content:"";position:absolute;bottom:-4px;left:-4px;border:14px solid transparent;border-bottom-color:#fff}.rank li:first-child .flag{background:#fb6757}.rank li:nth-child(2) .flag{background:#fac525}.rank li:nth-child(3) .flag{background:#00a9fb}.rank li img{width:36px;height:36px;border-radius:50%;margin-right:12px}</style><style type="text/css">.activeUser .activeUser-box a{display:inline-block;width:64px;height:64px;border-radius:50%;overflow:hidden;margin:0 6px 12px}</style><style type="text/css">.to-top{display:none;position:fixed;bottom:150px;z-index:5;width:50px;height:50px;cursor:pointer}</style></head><body style=""><div id="app" style=""><div class="que-detail-page" style=""><div class="wiki-hd"><div class="head-info"><p class="page-cnt">
      您好，欢迎来到591客服知识库
      <a href="https://www.591.com.tw/admin.php?new_url=https://zsk.591.com.tw">[请登入]</a> <!----></p></div> <div class="page-cnt head-search clearfix"><a href="/" class="router-link-active"><img src="/static/logo.png"></a> <ul class="head-nav clearfix"><li class="router-link-active active"><a href="/">591房屋交易网</a></li> <li class=""><a href="/life">客服充电站</a></li> <li class=""><a href="/notification">通知事项</a></li></ul> <div class="srh-box"><input type="text" placeholder="请输入搜索内容" class="srh-input"> <button type="button" class="button srh-btn">搜索</button> <a href="/ask/591" class="button button-orange ask-btn">提問</a></div></div> <div class="head-pic"></div></div> <div class="top-breadcrumb com-width"><span><a href="/collection/591/1329" class="">會員中心（屋主/中介）<i class="fa fa-angle-double-right"></i></a></span><span><a href="/collection/591/1329/1464" class="">物件管理<i class="fa fa-angle-double-right"></i></a></span><span><strong class="now">问题详情</strong></span></div> <div class="com-width clearfix" style=""><div class="index-left" style=""><div class="detail-box"><h3><!---->會員中心物件管理有哪些功能</h3> <p class="clearfix d-b-menu"><span class="l">
            文宇&nbsp;&nbsp;&nbsp;浏览409次&nbsp;&nbsp;&nbsp;2019-09-19 17:21:33
          </span> <span class="r"><span class="add-fav"><i class="fa fa-heart"></i>加入最爱</span></span></p> <div class="d-b-cnt editor-content big-img-parent"><div><p><span style="color: #666666; font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;">1、更新排序</span></p><p><span style="color: #666666; font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;">2、<strong style="box-sizing: border-box; font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">格局圖管理</span></strong></span></p><p><span style="color: #666666; font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong style="box-sizing: border-box; font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、暫時關閉</span></strong></span></p><p><span style="color: #666666; font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong style="box-sizing: border-box; font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4、成交下架</span></strong></span></p><p><span style="color: #666666; font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong style="box-sizing: border-box; font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5、續刊</span></strong></span></p><p><span style="color: #666666; font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong style="box-sizing: border-box; font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">6、瀏覽人數</span></strong></span></p><p><span style="color: #666666; font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong style="box-sizing: border-box; font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">7、昨日瀏覽人數</span></strong></span></p><p><span style="color: #666666; font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong style="box-sizing: border-box; font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">8、<span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">升級VIP</span></span></strong></span></p><p><span style="color: #666666; font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong style="box-sizing: border-box; font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">9、設置更新時間</span></strong></span></p><p><span style="color: #666666; font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong style="box-sizing: border-box; font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">10、剩餘時間（有效期限）<br></span></strong></span></p><p><span style="color: #666666; font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong style="box-sizing: border-box; font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">11、購買行動置頂、加強曝光、加值服務</span></strong></span></p><p><span style="color: #666666; font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong style="box-sizing: border-box; font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">12、修改</span></strong></span></p><p><span style="color: #666666; font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong style="box-sizing: border-box; font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">13、刪除</span></strong></span></p><p><span style="color: #666666; font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong style="box-sizing: border-box; font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">14、開啟</span></strong></span></p><p><span style="color: #666666; font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong style="box-sizing: border-box; font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">15、上傳影印本</span></strong></span></p><p><span style="color: #666666; font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong style="box-sizing: border-box; font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">16、推薦到店鋪</span></strong></span></p><p><span style="color: #666666; font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong style="box-sizing: border-box; font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">17、更換物件</span></strong></span></p><p><span style="color: #666666; font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong style="box-sizing: border-box; font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">18、升級管理</span></strong></span></p><p><span style="color: #666666; font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong style="box-sizing: border-box; font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">19、<span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">刪除圖片查詢</span></span></strong></span></p><p><span style="color:#333333;font-family:微软雅黑, Microsoft YaHei"><span style="font-size: 14px;"><strong>20、<span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">買家找房分析系統、熟悉的街道、熟悉的社區</span></strong></span></span></p><p><span style="color:#333333;font-family:微软雅黑, Microsoft YaHei"><span style="font-size: 14px;"><strong><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">21、<span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">格局圖管理</span></span></strong></span></span></p><p><br></p></div></div> <div class="d-b-button"><a href="/edit/591/3726" class="button">编辑</a> <button class="button button-orange">关闭提问</button></div></div> <div class="hr-line"></div> <ul class="reply-list big-img-parent"><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">文宇</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2019-09-19 17:37:14</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><br></p><table style="width: 801px;" width="881" cellspacing="0" cellpadding="0" data-sort="sortDisabled"><colgroup><col style=";width:68px" width="68"><col style=";width:95px" width="95"><col style=";width:433px" width="433"><col style=";width:285px" width="285"></colgroup><tbody><tr style="height:21px" class="firstRow" height="21"><td width="68" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">一級分類</span></td><td style="" width="95"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">二級分類</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">功能描述</span></td><td style="" width="285"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">規則補充</span></td></tr><tr style="height:21px" height="21"><td rowspan="16" width="68" height="1029"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">開啟中的物件</span></td><td style="" width="95"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">更新排序</span></td><td style="" width="433"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">可以選擇批量更新，排序規則：</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">租屋頁：1500元、900元/600元、200元/套餐方案廣告</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">中古屋：2500元、1500元、600元/套餐方案廣告</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">頂讓：800元、500元</span></p></td><td style="" width="285"><p><strong style="white-space: normal; box-sizing: border-box; color: rgb(192, 0, 0); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、手動更新後，自動更新的次數則少一次，更新次數及排序規則請參見《收費方案說明》</span></strong></p><p><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #C00000;">2、自動更新無法取消，但可將自動更新時間設定在半夜23點55分，會員便可手動操作了。</span></strong></p></td></tr><tr style="height:147px" height="147"><td width="95" height="147"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">升級VIP</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">刊登中途升級，是剩餘廣告的時間，升級費用如下：<br>出租住家類：升級超級VIP389元/筆、升級黃金曝光889元/筆<br></span><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">出租商用類 ：升級VIP200</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">出租商用套餐 ：升級VIP200，超級400，黃金900</span></span></p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">出售單筆 ：升級VIP900<br></span><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">出售套餐 ：升級VIP600</span></p></td><td style="" width="285"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">出售方案不可直接升級為：超級、黃金</span></td></tr><tr style="height:126px" height="126"><td width="95" height="126"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">暫時關閉</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、不方便帶看、房屋裝潢、修繕、出國、收定金、在洽談中等均可做暫時關閉<br> 2、暫時關閉時間照常計算，無法保留或延後；在有效期內可開啟無需付費<br>3、會員關閉：可自行開啟<br> 4、客服關閉：需聯絡客服方可開啟</span></td><td style="" width="285"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、出租住宅/商用套餐未提供暫時關閉功能，</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、若房屋刊登有誤廣告被客服中心強制關閉，要求補償關閉時間，與當班主任申請。</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><img src="/upload/question/20190919/1568885479151330.png" alt="image.png"></span></p></td></tr><tr style="height:84px" height="84"><td width="95" height="84"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">設置更新時間</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">提供給有購買自動更新服務的物件使用：<br>VIP方案以上的物件<br>購買了定時更新的普通方案</span></td><td style="" width="285"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">出售套餐若購買了定時更新則可增加一次自動更新，即前30天買，有四次自動更新，后30天買，有三次手動更新一次自動更新</span></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">購買置頂/續購</span></td><td style="" width="433"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、出租單筆、商用單筆套餐行動版精選推薦、行動版置頂、定時更新、加急標籤、精選推薦</span></span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">區</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、出租住宅套餐使用新版加值活動，活動期至6月31日</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、出售住宅單筆套餐都使用新版競價精選及新版置頂</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4、出售商用單筆套餐都可購買舊版精選推薦、及新版置頂（新舊共存）</span></span></p></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">剩餘時間</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">即廣告剩餘時間</span></td><td style="" width="285"><br></td></tr><tr style="height:168px" height="168"><td width="95" height="168"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">續刊</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">一筆物件僅能續刊一次；<br>套餐筆數不可續刊，只能用單筆方案續刊<br>出租住宅及商用套餐不可續刊<br>續刊時會提醒時間已延長<br>續刊時間未使用到：<br>1、可來電客服中心取消退點數<br>2、若會員點擊成交下架，點數會自動返還會員中心<br>3、續刊後，加值服務的時間未使用到的均可繼續使用</span></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">修改資訊</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細修改規則請參見《刊登租售廣告有什麼規定》</span></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">更新排序</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">僅針對單筆更新，更新次數及排序規則請參見《收費方案說明》</span></td><td style="" width="285"><br></td></tr><tr style="height:105px" height="105"><td width="95" height="105"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">成交下架</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、所刊登的房屋廣告已經簽約完成租掉或賣掉，廣告則需成交下架處理，不能在繼續刊登在網站上了，成交下架後廣告時間隨之結束。<br></span><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、不管是透過591成交或是通過其他方式成交，成交后物件都顯示在已成交列表，若會員自行刪除物件資料，則不會再顯示</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、成交留言如果會員有填寫過，那下一次的成交留言就是會員上次填寫的內容</span></p></td><td style="" width="285"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">廣告誤點成交：<br>下架後三天內，客服可自行幫恢復，超過三天需與當班主任申請<br></span><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">ps：出租套餐物件誤點成交可至會員中心再次開啟</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><img src="/upload/question/20190919/1568885752412986.png" alt="image.png"></span></p></td></tr><tr style="height:42px" height="42"><td width="95" height="42"><span style="font-family:微软雅黑, Microsoft YaHei"><span style="font-size: 14px;">購買精選/調整出價</span></span></td><td style="" width="433"><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、出租單筆、商用單筆套餐行動版精選推薦、行動版置頂、定時更新、加急標籤、精選推薦</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">區</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、出租住宅套餐使用新版加值活動，活動期至6月31日</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、出售住宅單筆套餐都使用新版競價精選及新版置頂</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4、出售商用單筆套餐都可購買舊版精選推薦、及新版置頂（新舊共存）</span></p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span></td><td style="" width="285"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">頂讓廣告不可購買加強曝光，詳情請參見《租售加值服務收費方案》</span></td></tr><tr style="height:168px" height="168"><td width="95" rowspan="2" colspan="1"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">瀏覽人數</span></td><td style="" width="433"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #C00000;">統計規則</span><span style="color: #C00000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">：</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #000000;">每1分鐘更新一次統計人數</span></p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">每兩小時統計一次cookie<br>不同設備為不同cookie<br>管理員瀏覽不計入<br><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #C00000;">展示規則：</span><br></span><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員中心的瀏覽人數均為總瀏覽人數（取PC端+移動端總數）</span></p><p><span style="font-family:微软雅黑, Microsoft YaHei"><span style="font-size: 14px; color: #A5A5A5;">ps：移動端物件管理總瀏覽人數僅區移動端總數，不含pc端</span></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">租屋詳情頁展示昨日瀏覽人數</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">（租屋住宅&amp;商用套餐只能查看昨日瀏覽人數，無法查看總瀏覽人數）</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">售屋詳情頁展示總瀏覽人數&nbsp;</span></p></td><td style="" width="285"><p><span style="color: #000000; font-weight: normal; text-decoration: none; font-style: normal; font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">1、物件管理瀏覽人數【<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">明細</span>】，需要有購買加值服務（置頂、精選推薦、競價）才可查看</span></p><p><span style="color: #000000; font-weight: normal; text-decoration: none; font-style: normal; font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">2、物件管理-開啟中的物件顯示的瀏覽率百分比的計算方式是跟上週的日均值對比</span></p><p><span style="color: #000000; font-weight: normal; text-decoration: none; font-style: normal; font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">3、瀏覽人數明細中數據僅展示近30天的數據</span></p><p>&nbsp;&nbsp;<img src="/upload/question/20200628/1593325878677303.png" alt="image.png"></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4、較上週：xx%，算法：</span></p><p>取自然周点阅均值同比</p><p>1）上周：x天累计点阅/x天，向上取整</p><p>2）本周：x天累计点阅/x天，向上取整</p></td></tr><tr><td colspan="1" rowspan="1" style="word-break: break-all;"><p>增加提示字段：低於12%的物件，立即提升</p><p><img src="/upload/question/20210425/1619319474478793.png" alt="image.png" width="398" height="133" style="width: 398px; height: 133px;"></p></td><td colspan="1" rowspan="1"><p>顯示條件：</p><p>1）未購買置頂且未購買精選推薦（任何一端）的物件</p><p>2）點閱數：數值在10%-50%才會顯示（截止昨日的總瀏覽數）</p><p><br></p><p>比例計算方法：物件纍計點閱數/該物件所在縣市所有競價物件的平均點閱數</p></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">立即搜尋</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員中心提供物件編號、地址，租售頂類別搜尋的功能</span></td><td style="" width="285"><br></td></tr><tr style="height:42px" height="42"><td width="95" height="42"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">推薦到店鋪</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">仲介開通店鋪後即可有此功能，每次可推薦五筆，會在店鋪右手邊展示</span></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">上傳影印本</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">指上傳房屋權狀書或線上謄本</span></td><td style="" width="285"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">電腦與APP有提供刪除影印本功能</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">電腦：進入修改資料頁面處刪除<br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">APP：<span style="white-space: pre-wrap; background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"> 安卓APP點擊三個點的查看影印本，進入後點擊刪除影印本即可</span></span></p></td></tr><tr style="height:21px" height="21"><td rowspan="8" width="68" colspan="1"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">關閉中的物件</span></td><td style="" width="95"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">關閉狀態</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">未開啟、自行關閉、客服關閉、刊登過期</span></td><td style="" width="285"><br></td></tr><tr><td colspan="1" rowspan="1">關閉時間</td><td colspan="1" rowspan="1"><p>1、顯示物件的下架日期</p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、若使用出租套餐欄位開啟廣告，套餐過期後廣告下架，在關閉中的物件中顯示的關閉時間是廣告最後一次的更新時間</span></p></td><td colspan="1" rowspan="1"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">開啟</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">除客服關閉會員無法自行開啟外，其他均可付費後自行開啟</span></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">修改</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細修改規則請參見《刊登租售廣告有什麼規定》</span></td><td style="" width="285"><br></td></tr><tr style="height:126px" height="126"><td width="95" height="126"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">刪除</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、開啟中的物件無刪除功能<br>2、關閉中的物件刪除後資料無法恢復，客服可特殊請會員將文字內容填寫好，照片可以幫會員另存下來幫上傳。<br>3、若有特殊的原因需要恢復，與現場申請，可特殊恢復，刪除前物件是什麼狀態，恢復后物件就是什麼狀態，<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #C00000;">PS：刊登錯誤取消退點被刪除的資料恢復則為免費開啟的狀態，需謹慎操作</span></span></td><td style="" width="285"><img src="/upload/question/20190919/1568885770881809.png" alt="image.png"></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">成交下架</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">僅限狀態為自行關閉使用，規則同開啟中的物件</span></td><td style="" width="285"><br></td></tr><tr><td colspan="1" rowspan="1"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">方案標籤</span><br></td><td colspan="1" rowspan="1"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、原黃金曝光方案過期後，標籤顯示：黃金曝光</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、原超級曝光及VIP方案過期後，標籤顯示：VIP</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、原普通方案物件過期後，無標籤</span></p></td><td colspan="1" rowspan="1"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">注：上線租屋789元方案後過期的物件取消過期物件顯示VIP標籤的功能【僅取消租屋物件】</span></p></td></tr><tr><td colspan="1" rowspan="1">排序規則</td><td colspan="1" rowspan="1">按照物件的更新時間從新到舊排序</td><td colspan="1" rowspan="1"><br></td></tr><tr style="height:21px" height="21"><td rowspan="4" width="68" height="105"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">已成交的物件</span></td><td style="" width="95"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">開啟</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">可付費後自行開啟</span></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">刪除</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">規則同關閉中的物件</span></td><td style="" width="285"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">有提供【批量刪除】已成交功能</span></td></tr><tr style="height:42px" height="42"><td width="95" height="42"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">瀏覽人數</span></td><td style="" width="433"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員中心展示物件總瀏覽人</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">數</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">成交後租售詳情頁均展示總瀏覽人數</span></p></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">櫥窗成交</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">待確定</span></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td rowspan="10" width="68" height="210"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">租屋住宅套餐物件</span></td><td style="" width="95"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">廣告剩餘天數</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">指欄位剩餘天數</span></td><td style="" width="285"><br></td></tr><tr style="height:42px" height="42"><td width="95" height="42"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">更換物件</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、可使用舊物件更新或新填寫一筆物件<br>2、被更換下來的物件狀態為已過期</span></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">升級VIP</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">升級VIP400，對欄位有效，即物件更換後也是VIP</span></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">修改</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細修改規則請參見《刊登租售廣告有什麼規定》</span></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">成交下架</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">規則同開啟中的物件</span></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">更新排序</span></td><td style="" width="433"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、規則同開啟中的物</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">件</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、提供批量更新排序功能（2021/1/14上線）</span></p></td><td style="" width="285"><br></td></tr><tr><td colspan="1" rowspan="1"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">暫時關閉</span></td><td colspan="1" rowspan="1"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">廣告關閉後會進入關閉中的物件，物件狀態為：未使用；欄位時間仍照常計算</span>（2022/7/12上線）</span></td><td colspan="1" rowspan="1"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">四端都有提供關閉功能</span></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">上傳影印本</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">規則同開啟中的物件</span></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">刊登新廣告</span></td><td rowspan="2" style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">欄位未使用時，有此提示，并顯示有效期限</span></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">開啟舊廣告</span></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td rowspan="10" width="68" height="231"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">租屋商用套餐物件</span></td><td style="" width="95"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">廣告剩餘天數</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">指欄位剩餘天數</span></td><td style="" width="285"><br></td></tr><tr style="height:42px" height="42"><td width="95" height="42"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">更換物件</span></td><td style="" width="433"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、可使用舊物件更新或新填寫一筆物</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">件</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、被更換下來的物件狀態為已過期</span></p></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">升級VIP</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">升級VIP200，對欄位有效，即物件更換後也是VIP</span></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">升級超級</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">升級超級400，對欄位有效，即物件更換後也是超級</span></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">升級黃金</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">升級黃金900，對欄位有效，即物件更換後也是黃金</span></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">修改</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳細修改規則請參見《刊登租售廣告有什麼規定》</span></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">成交下架</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">規則同開啟中的物件</span></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">更新排序</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">規則同開啟中的物件</span></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">刊登新廣告</span></td><td rowspan="2" style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">欄位未使用時，有此提示，并顯示有效期限</span></td><td style="" width="285"><br></td></tr><tr style="height:21px" height="21"><td width="95" height="21"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">開啟舊廣告</span></td><td style="" width="285"><br></td></tr><tr style="height:105px" height="105"><td colspan="2" height="105"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">刪除圖片查詢</span></td><td style="" width="433"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">不符合網站刊登的照片：審核專員會按作業流程將照片隱藏/刪除，為了方便會員來電詢問照片為什麼不見了，客服查詢到違規記</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">錄</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、圖片違規記錄顯示內容：顯示物件編號、刪除時間、刪除的照片</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、可查詢近三個月的違規記錄，頁面可顯示最近刪除的50條記錄</span><br></p></td><td style="" width="285"><img src="/upload/question/20190919/1568885788951148.png" alt="image.png"></td></tr><tr style="height:63px" height="63"><td colspan="2" height="63"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">物件昨日瀏覽效果</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、展示所有開啟中的物件之昨日瀏覽人數數據<br>2、提供【我要提交瀏覽人數】入口，購買加值服務<br>3、此功能僅屋主身份有</span></td><td style="" width="285"><br></td></tr><tr style="height:84px" height="84"><td rowspan="2" width="68" height="147"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">買家找房分析系統</span></td><td style="" width="95"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">我熟悉的街道/社區</span></td><td style="" width="433"><br><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">可添加熟悉的社區（三個）、熟悉的街道（三個），不可自行修改，客服也無法修改，會員意見大可記錄提交現場，讓工程師特殊處理；<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #FF0000;">注，不可輕易答應</span><br> &nbsp; &nbsp; </span></td><td rowspan="2" style="" width="285"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">此功能仲介及仲介公司身份有</span></td></tr><tr style="height:63px" height="63"><td width="95" height="63"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">售屋物件分析系統</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、系統根據填寫的街道和社區，分析出【緊缺的物件街道及社區】<br>2、從戶型、價格、坪數分析房客喜愛度，若仲介有相關的房源可考慮是否做刊登。</span></td></tr><tr style="height:105px" height="105"><td colspan="2" width="163" height="105"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">格局圖管理</span></td><td style="" width="433"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、請確保電腦有安裝好最新版flash<br>2、保存已生成的格局圖，請您點擊滑鼠右鍵 － 選擇 圖像另存為(V)... 進行儲存<br>3、繪製的功能為簡易版：文字、核心建築、墻骨架若沒有，建議記錄呈報專員後續考量優化</span></td><td style="" width="285"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">此功能仲介及仲介公司身份有</span></td></tr></tbody></table><p><br></p><p><br></p><p><span style="color: #FF0000; font-family: 微软雅黑, Microsoft YaHei;">PS：商用租屋套餐及租屋住家套餐的總瀏覽人數無法在PC端會員中心查看，僅能查看到詳細頁面昨日瀏覽人數，手機APP【廣告管理】可查看到總瀏覽人數。</span><br></p></div></div> <div><span class="button">我要編輯</span></div></li></ul> <h3 class="more-answer">我要補充</h3> <div style=""><div id="editor_66462261849475220" class="edui-default" style=""><div id="edui1" class="edui-editor  edui-default" style="width: 100%; z-index: 999;"><div id="edui1_toolbarbox" class="edui-editor-toolbarbox edui-default"><div id="edui1_toolbarboxouter" class="edui-editor-toolbarboxouter edui-default"><div class="edui-editor-toolbarboxinner edui-default"><div id="edui2" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui2&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui3" class="edui-box edui-button edui-for-fullscreen edui-default"><div id="edui3_state" onmousedown="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui3_body" unselectable="on" title="全屏" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui3&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui3&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui4" class="edui-box edui-button edui-for-source edui-default"><div id="edui4_state" onmousedown="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui4_body" unselectable="on" title="源代码" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui4&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui4&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui5" class="edui-box edui-button edui-for-undo edui-default"><div id="edui5_state" onmousedown="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui5_body" unselectable="on" title="撤销" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui5&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui5&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui6" class="edui-box edui-button edui-for-redo edui-default"><div id="edui6_state" onmousedown="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui6_body" unselectable="on" title="重做" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui6&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui6&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui7" class="edui-box edui-button edui-for-simpleupload edui-default"><div id="edui7_state" onmousedown="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui7_body" unselectable="on" title="单图上传" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui7&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui7&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui8" class="edui-box edui-button edui-for-imageleft edui-default"><div id="edui8_state" onmousedown="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui8_body" unselectable="on" title="左浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui8&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui8&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui9" class="edui-box edui-button edui-for-imageright edui-default"><div id="edui9_state" onmousedown="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui9_body" unselectable="on" title="右浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui9&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui9&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui10" class="edui-box edui-button edui-for-imagecenter edui-default"><div id="edui10_state" onmousedown="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui10_body" unselectable="on" title="居中" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui10&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui10&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui11" class="edui-box edui-separator  edui-default"></div><div id="edui12" class="edui-box edui-button edui-for-date edui-default"><div id="edui12_state" onmousedown="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui12_body" unselectable="on" title="日期" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui12&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui12&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui13" class="edui-box edui-button edui-for-time edui-default"><div id="edui13_state" onmousedown="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui13_body" unselectable="on" title="时间" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui13&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui13&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui14" class="edui-box edui-button edui-for-horizontal edui-default"><div id="edui14_state" onmousedown="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui14_body" unselectable="on" title="分隔线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui14&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui14&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui15" class="edui-box edui-separator  edui-default"></div><div id="edui16" class="edui-box edui-splitbutton edui-for-inserttable edui-default"><div title="插入表格" id="edui16_state" onmousedown="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui16_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onArrowClick();"></div></div></div></div><div id="edui19" class="edui-box edui-button edui-for-deletetable edui-default"><div id="edui19_state" onmousedown="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui19_body" unselectable="on" title="删除表格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui19&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui19&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui20" class="edui-box edui-button edui-for-insertrow edui-default"><div id="edui20_state" onmousedown="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui20_body" unselectable="on" title="前插入行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui20&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui20&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui21" class="edui-box edui-button edui-for-insertcol edui-default"><div id="edui21_state" onmousedown="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui21_body" unselectable="on" title="前插入列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui21&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui21&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui22" class="edui-box edui-button edui-for-mergeright edui-default"><div id="edui22_state" onmousedown="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui22_body" unselectable="on" title="右合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui22&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui22&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui23" class="edui-box edui-button edui-for-mergedown edui-default"><div id="edui23_state" onmousedown="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui23_body" unselectable="on" title="下合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui23&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui23&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui24" class="edui-box edui-button edui-for-deleterow edui-default"><div id="edui24_state" onmousedown="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui24_body" unselectable="on" title="删除行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui24&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui24&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui25" class="edui-box edui-button edui-for-deletecol edui-default"><div id="edui25_state" onmousedown="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui25_body" unselectable="on" title="删除列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui25&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui25&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui26" class="edui-box edui-button edui-for-splittorows edui-default"><div id="edui26_state" onmousedown="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui26_body" unselectable="on" title="拆分成行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui26&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui26&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui27" class="edui-box edui-button edui-for-splittocols edui-default"><div id="edui27_state" onmousedown="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui27_body" unselectable="on" title="拆分成列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui27&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui27&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui28" class="edui-box edui-button edui-for-splittocells edui-default"><div id="edui28_state" onmousedown="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui28_body" unselectable="on" title="完全拆分单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui28&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui28&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui29" class="edui-box edui-button edui-for-mergecells edui-default"><div id="edui29_state" onmousedown="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui29_body" unselectable="on" title="合并多个单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui29&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui29&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui36" class="edui-box edui-button edui-for-edittable edui-default"><div id="edui36_state" onmousedown="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui36_body" unselectable="on" title="表格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui36&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui36&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui41" class="edui-box edui-button edui-for-edittd edui-default"><div id="edui41_state" onmousedown="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui41_body" unselectable="on" title="单元格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui41&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui41&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui42" class="edui-box edui-separator  edui-default"></div><div id="edui43" class="edui-box edui-menubutton edui-for-lineheight edui-default"><div title="行间距" id="edui43_state" onmousedown="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui43_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onArrowClick();"></div></div></div></div><div id="edui52" class="edui-box edui-menubutton edui-for-rowspacingtop edui-default"><div title="段前距" id="edui52_state" onmousedown="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui52_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onArrowClick();"></div></div></div></div><div id="edui59" class="edui-box edui-menubutton edui-for-rowspacingbottom edui-default"><div title="段后距" id="edui59_state" onmousedown="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui59_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onArrowClick();"></div></div></div></div></div><div id="edui66" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui66&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui67" class="edui-box edui-combox edui-for-fontfamily edui-default"><div title="字体" id="edui67_state" onmousedown="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui67_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onButtonClick(event, this);">字体</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onArrowClick();"></div></div></div></div><div id="edui80" class="edui-box edui-combox edui-for-customstyle edui-default"><div title="自定义标题" id="edui80_state" onmousedown="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui80_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onButtonClick(event, this);">自定义标题</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onArrowClick();"></div></div></div></div><div id="edui86" class="edui-box edui-combox edui-for-paragraph edui-default"><div title="段落格式" id="edui86_state" onmousedown="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui86_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onButtonClick(event, this);">段落格式</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onArrowClick();"></div></div></div></div><div id="edui95" class="edui-box edui-combox edui-for-fontsize edui-default"><div title="字号" id="edui95_state" onmousedown="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui95_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onButtonClick(event, this);">字号</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onArrowClick();"></div></div></div></div><div id="edui110" class="edui-box edui-button edui-for-link edui-default"><div id="edui110_state" onmousedown="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui110_body" unselectable="on" title="超链接" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui110&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui110&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui111" class="edui-box edui-button edui-for-indent edui-default"><div id="edui111_state" onmousedown="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui111_body" unselectable="on" title="首行缩进" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui111&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui111&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui112" class="edui-box edui-button edui-for-bold edui-default"><div id="edui112_state" onmousedown="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui112_body" unselectable="on" title="加粗" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui112&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui112&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui113" class="edui-box edui-splitbutton edui-for-forecolor edui-default edui-colorbutton"><div title="字体颜色" id="edui113_state" onmousedown="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui113_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui113_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onArrowClick();"></div></div></div></div><div id="edui116" class="edui-box edui-splitbutton edui-for-backcolor edui-default edui-colorbutton"><div title="背景色" id="edui116_state" onmousedown="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui116_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui116_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onArrowClick();"></div></div></div></div><div id="edui119" class="edui-box edui-button edui-for-italic edui-default"><div id="edui119_state" onmousedown="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui119_body" unselectable="on" title="斜体" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui119&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui119&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui120" class="edui-box edui-button edui-for-underline edui-default"><div id="edui120_state" onmousedown="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui120_body" unselectable="on" title="下划线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui120&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui120&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui121" class="edui-box edui-button edui-for-strikethrough edui-default"><div id="edui121_state" onmousedown="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui121_body" unselectable="on" title="删除线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui121&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui121&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui122" class="edui-box edui-button edui-for-superscript edui-default"><div id="edui122_state" onmousedown="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui122_body" unselectable="on" title="上标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui122&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui122&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui123" class="edui-box edui-button edui-for-subscript edui-default"><div id="edui123_state" onmousedown="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui123_body" unselectable="on" title="下标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui123&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui123&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui124" class="edui-box edui-button edui-for-removeformat edui-default"><div id="edui124_state" onmousedown="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui124_body" unselectable="on" title="清除格式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui124&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui124&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui125" class="edui-box edui-button edui-for-blockquote edui-default"><div id="edui125_state" onmousedown="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui125_body" unselectable="on" title="引用" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui125&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui125&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui126" class="edui-box edui-button edui-for-pasteplain edui-default"><div id="edui126_state" onmousedown="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui126_body" unselectable="on" title="纯文本粘贴模式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui126&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui126&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui127" class="edui-box edui-menubutton edui-for-insertorderedlist edui-default"><div title="有序列表" id="edui127_state" onmousedown="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui127_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onArrowClick();"></div></div></div></div><div id="edui140" class="edui-box edui-menubutton edui-for-insertunorderedlist edui-default"><div title="无序列表" id="edui140_state" onmousedown="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui140_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onArrowClick();"></div></div></div></div><div id="edui147" class="edui-box edui-button edui-for-cleardoc edui-default"><div id="edui147_state" onmousedown="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui147_body" unselectable="on" title="清空文档" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui147&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui147&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div></div></div></div><div id="edui1_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;"><div id="edui1_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui1&quot;].showWordImageDialog();">点击上传</div><div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui1&quot;].hideToolbarMsg();">x</div><div id="edui1_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div><div style="height:0;overflow:hidden;clear:both;" class="edui-default"></div></div><div id="edui1_message_holder" class="edui-editor-messageholder edui-default"></div></div><div id="edui1_iframeholder" class="edui-editor-iframeholder edui-default" style="width: 100%; height: 300px; z-index: 999; overflow: hidden;"><iframe id="ueditor_0" width="100%" height="100%" frameborder="0" src="javascript:void(function(){document.open();document.write(&quot;<!DOCTYPE html><html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}
body{margin:8px;font-family:sans-serif;font-size:13px;}p{margin:5px 0;}</style><link rel='stylesheet' type='text/css' href='/static/ueditor/themes/iframe.css'/></head><body class='view' ></body><script type='text/javascript'  id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant0'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);</script></html>&quot;);document.close();}())"></iframe></div><div id="edui1_bottombar" class="edui-editor-bottomContainer edui-default"><table class="edui-default"><tbody class="edui-default"><tr class="edui-default"><td id="edui1_elementpath" class="edui-editor-bottombar edui-default"><div class="edui-editor-breadcrumb">元素路径:</div></td><td id="edui1_wordcount" class="edui-editor-wordcount edui-default">字数统计</td><td id="edui1_scale" class="edui-editor-scale edui-default" style="display: none;"><div class="edui-editor-icon edui-default"></div></td></tr></tbody></table></div><div id="edui1_scalelayer" class="edui-default"></div></div></div> <div class="edit-btn"><button type="button" class="button">提交回答</button> <label data-v-0fe26aab="" for="commonCheckbox" class="checkbox"><input data-v-0fe26aab="" type="checkbox" id="commonCheckbox" name="commonCheckbox"> <i data-v-0fe26aab=""></i> 需要彈窗，請勾選</label></div></div></div> <div class="index-right"><div class="right-bk"><h3 class="com-title"><a href="javascript:;">我的最爱</a></h3> <div class="item-box clearfix"><span>錯誤，請您登入後再操作！</span></div></div> <div class="hotTag right-bk"><h3 class="com-title">热门标签</h3> <div class="hotTag-box item-box clearfix"><p>無數據</p></div></div> <div class="right-bk rank"><h3 class="com-title"><a href="javascript:;">冠軍排行榜</a></h3> <ul><li>暫無數據</li></ul></div> <div class="activeUser right-bk"><h3 class="com-title">活跃用户</h3> <div class="activeUser-box"><a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a></div></div> <img src="data:image/png;base64,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" class="to-top"></div></div> <!----> <!----> <!----> <div class="page-cnt footer"><p>T5客服知识库<span>|</span>zsk.591.com.tw</p></div></div></div><script type="text/javascript" src="/js/manifest.f192b.js"></script><script type="text/javascript" src="/js/vendor.17570.js"></script><script type="text/javascript" src="/js/app.58254.js"></script><div id="edui_fixedlayer" class="edui-default" style="position: fixed; left: 0px; top: 0px; width: 0px; height: 0px;"><div id="edui148" class="edui-popup  edui-bubble edui-default" onmousedown="return false;" style="display: none;"> <div id="edui148_body" class="edui-popup-body edui-default"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank" class="edui-default"></iframe> <div class="edui-shadow edui-default"></div> <div id="edui148_content" class="edui-popup-content edui-default">  </div> </div></div></div></body></html>