<html class=" "><head><meta charset="utf-8"><title>591客服知識庫</title><link rel="stylesheet" href="/static/font/font-awesome.min.css"><link href="/css/app.e5aa4.css" rel="stylesheet"><script type="text/javascript" charset="utf-8" async="" src="/js/questionDetail.d0a6b.js"></script><style type="text/css">.que-detail-page .index-left .button{width:74px;height:32px;line-height:32px;font-size:13px;margin-right:10px}.que-detail-page .index-left .button:hover{color:#fff}.que-detail-page .detail-box{padding:20px 30px;border:1px solid #e5e5e5;border-radius:4px}.que-detail-page .detail-box h3{font-size:18px;color:#333;margin-bottom:10px}.que-detail-page .detail-box h3 span{color:#f77d24}.que-detail-page .detail-box .add-fav{cursor:pointer}.que-detail-page .d-b-menu{margin:15px 0 25px;font-size:13px;color:#999}.que-detail-page .d-b-menu b{display:inline-block;font-weight:400;margin:0 10px}.que-detail-page .d-b-menu .r{float:right;color:#f77d24}.que-detail-page .d-b-menu .r .fa{margin-right:5px;font-size:16px}.que-detail-page .d-b-cnt{margin-bottom:25px;font-size:13px;color:#666}.que-detail-page .submit-ask{display:block;margin:32px auto 0}.que-detail-page .reply-list{margin:30px 0 10px;overflow:hidden}.que-detail-page .reply-list .reply-item{padding:15px 30px 25px;margin-bottom:30px;border-radius:4px;border:1px solid #e5e5e5}.que-detail-page .reply-list .reply-item h4{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;line-height:50px;font-size:13px}.que-detail-page .reply-list .reply-item h4 img{width:40px;height:40px;border-radius:50%}.que-detail-page .reply-list .reply-item h4 .time{color:#999}.que-detail-page .reply-list .reply-item h4 .button{-ms-flex-item-align:center;align-self:center}.que-detail-page .reply-list .reply-item h4 .delete{color:#666;cursor:pointer}.que-detail-page .reply-list .reply-item .reply-detail{padding:20px 0 30px}.que-detail-page .more-answer{font-size:18px;color:#666;margin-bottom:5px}.que-detail-page .editor-content table{width:100%;border:1px solid #ddd}.que-detail-page .editor-content table td,.que-detail-page .editor-content table th{border:1px solid #ddd;padding:2px 8px;line-height:2}.que-detail-page .editor-content img{max-width:100%;vertical-align:text-bottom}.que-detail-page .edit-btn{text-align:center;margin-top:20px}.que-detail-page .edit-btn .edit-cancle{background:#aaa}</style><style type="text/css">.head-info{line-height:26px;background:#f5f5f5;font-size:13px;color:#999}.head-info a{margin-left:20px;color:#333}.head-search{margin-top:22px}.head-search img{float:left}.head-search .srh-box{float:left;margin:20px 0 0 20px}.head-search .srh-box .srh-input{float:left;width:260px;height:38px;line-height:38px;border:2px solid #37a508;border-radius:4px 0 0 4px}.head-search .srh-box a,.head-search .srh-box button{float:left;width:80px;height:38px;line-height:36px;font-size:16px;color:#fff}.head-search .srh-box .srh-btn{border-radius:0 4px 4px 0}.head-search .srh-box .ask-btn{margin-left:10px}.head-nav{float:left;margin:20px 0 0 60px;color:#fff;font-size:16px;line-height:36px}.head-nav a{display:block;height:100%;color:#333}.head-nav li{position:relative;float:left;margin-right:35px}.head-nav .active a,.head-nav li:hover a{color:#30b940}.head-nav .active:after,.head-nav li:hover:after{display:block;content:"";height:3px;width:100%;background:#30b940}.head-pic{width:100%;height:90px;margin-top:20px;background:url(/static/banner.png) top no-repeat}</style><style type="text/css">.top-breadcrumb{height:20px;margin-top:10px;margin-bottom:40px;font-size:13px;color:#666}.top-breadcrumb .t-b-icon{color:#30b940}.top-breadcrumb span{margin-left:8px}.top-breadcrumb span .fa{font-size:16px;margin-left:8px}.top-breadcrumb span:first-child{margin-left:0}.top-breadcrumb .now{color:#30b940}</style><script type="text/javascript" src="/static/ueditor/ueditor.config.js?20210319" id="configScriptTag"></script><script type="text/javascript" src="/static/ueditor/ueditor.all.js?20210319" id="editorScriptTag"></script><style type="text/css">.checkbox[data-v-0fe26aab]{word-spacing:4px;vertical-align:bottom;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.checkbox i[data-v-0fe26aab]{display:inline-block;width:14px;height:14px;border-radius:2px;border:1px solid #ccc;background:#fff;position:relative;top:1px}.checkbox input[data-v-0fe26aab]{opacity:0;width:0;height:0;margin:0}.checkbox input:checked+i[data-v-0fe26aab]{background:#30b940;border-color:#30b940}.checkbox input:checked+i[data-v-0fe26aab]:before{content:"";position:absolute;left:2px;top:1px;width:11px;height:6px;border-bottom:2px solid #fff;border-left:2px solid #fff;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}</style><style type="text/css">.popbox{position:fixed;top:50%;left:50%;z-index:1011;background:#fff;border-radius:6px;overflow:hidden}.popbox h3{font-size:16px;line-height:40px;padding:0 20px}.popbox h3 span{float:right;color:#666;font-size:13px;cursor:pointer}.popbox .pb-cnt{padding:0 20px;font-size:13px}.popbox .pb-btn-area{position:absolute;bottom:0;left:0;z-index:1;width:100%;text-align:right;padding:0 20px 20px 0}.popbox .pb-btn-area button{height:32px;line-height:32px;border:0;background:#fff;cursor:pointer;border-radius:6px;margin-left:15px;padding:0 15px}.popbox .pb-btn-area button.pb-btn-ok{background:#daf3cf;color:#279005}.popbox .pb-btn-area button.pb-btn-ok:hover{background:#279005;color:#fff}</style><style type="text/css">.shadow{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1010;background:#000}</style><style type="text/css">.footer{margin-top:70px;margin-bottom:20px;padding-top:20px;text-align:center;color:#999;border-top:1px solid #f5f5f5}.footer span{margin:0 10px}</style><script src="/static/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript" defer="defer"></script><link href="/static/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet"><script src="/static/ueditor/third-party/codemirror/codemirror.js" type="text/javascript" defer="defer"></script><link rel="stylesheet" type="text/css" href="/static/ueditor/third-party/codemirror/codemirror.css"><script src="/static/ueditor/third-party/zeroclipboard/ZeroClipboard.js" type="text/javascript" defer="defer"></script><style type="text/css">.activeUser,.favorite,.hotTag,.rank{margin-bottom:40px}</style><style type="text/css">.rank .rank-page{height:60px;line-height:60px;font-size:18px;margin-bottom:20px;text-align:center;background:#fafafa;border-radius:5px}.rank ul{padding:0 20px}.rank li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;border-bottom:1px dotted #f5f5f5;line-height:50px}.rank li .flag{position:relative;display:inline-block;width:20px;height:30px;background:#476472;vertical-align:middle;margin-right:12px;color:#fff;text-align:center;line-height:20px;font-size:13px;font-style:normal}.rank li .flag:after{content:"";position:absolute;bottom:-4px;left:-4px;border:14px solid transparent;border-bottom-color:#fff}.rank li:first-child .flag{background:#fb6757}.rank li:nth-child(2) .flag{background:#fac525}.rank li:nth-child(3) .flag{background:#00a9fb}.rank li img{width:36px;height:36px;border-radius:50%;margin-right:12px}</style><style type="text/css">.activeUser .activeUser-box a{display:inline-block;width:64px;height:64px;border-radius:50%;overflow:hidden;margin:0 6px 12px}</style><style type="text/css">.to-top{display:none;position:fixed;bottom:150px;z-index:5;width:50px;height:50px;cursor:pointer}</style></head><body style=""><div id="app" style=""><div class="que-detail-page" style=""><div class="wiki-hd"><div class="head-info"><p class="page-cnt">
      您好，欢迎来到591客服知识库
      <a href="https://www.591.com.tw/admin.php?new_url=https://zsk.591.com.tw">[请登入]</a> <!----></p></div> <div class="page-cnt head-search clearfix"><a href="/" class="router-link-active"><img src="/static/logo.png"></a> <ul class="head-nav clearfix"><li class="router-link-active active"><a href="/">591房屋交易网</a></li> <li class=""><a href="/life">客服充电站</a></li> <li class=""><a href="/notification">通知事项</a></li></ul> <div class="srh-box"><input type="text" placeholder="请输入搜索内容" class="srh-input"> <button type="button" class="button srh-btn">搜索</button> <a href="/ask/591" class="button button-orange ask-btn">提問</a></div></div> <div class="head-pic"></div></div> <div class="top-breadcrumb com-width"><span><a href="/collection/591/1326" class="">新建案相關<i class="fa fa-angle-double-right"></i></a></span><span><a href="/collection/591/1326/1620" class="">如何刊登<i class="fa fa-angle-double-right"></i></a></span><span><strong class="now">问题详情</strong></span></div> <div class="com-width clearfix" style=""><div class="index-left" style=""><div class="detail-box"><h3><!---->新建案新刊登/修改審核規範及處理流程</h3> <p class="clearfix d-b-menu"><span class="l">
            黃雪靜&nbsp;&nbsp;&nbsp;浏览383次&nbsp;&nbsp;&nbsp;2019-09-06 16:41:22
          </span> <span class="r"><span class="add-fav"><i class="fa fa-heart"></i>加入最爱</span></span></p> <div class="d-b-cnt editor-content big-img-parent"><div><p>新建案新刊登審核/修改審核規範及處理流程</p></div></div> <div class="d-b-button"><a href="/edit/591/3630" class="button">编辑</a> <button class="button button-orange">关闭提问</button></div></div> <div class="hr-line"></div> <ul class="reply-list big-img-parent"><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">文宇</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2019-09-10 17:17:36</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong>一、<strong style="text-align: -webkit-center; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">新建案後台審核頁展示規則</span></strong></strong></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong><strong style="text-align: -webkit-center; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></strong></strong></span></p><table><tbody><tr class="firstRow"><td height="32" width="216" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">審核類別</span></span></p></td><td width="125" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">編輯狀態</span></span></p></td><td width="132" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">客服狀態</span></span></p></td><td width="665" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">狀態說明</span></span></p></td></tr><tr><td rowspan="4" height="186" width="216" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新建案刊登</span></p><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><br style="box-sizing: border-box;"></p><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">編輯許可權與客服許可權同時通過審核，建案才會開啟</span></p></td><td width="125" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">未處理</span></p></td><td width="132" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #749530; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">已自動審核</span></p></td><td width="665" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="box-sizing: border-box; color: #424242;">該筆建案為編輯刊登，</span><span style="box-sizing: border-box; color: #FE7E00;">無需客服處理</span></span></p></td></tr><tr><td height="39" width="125" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">未處理</span></p></td><td width="132" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">未處理</span></p></td><td width="665" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員自刊建案，需客服處理，當前處於待審核狀態</span></p></td></tr><tr><td height="44" width="125" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">待處理</span></p></td><td width="132" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">未處理</span></p></td><td width="665" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">專員已審核，建案資料有誤，等待客服審核與撥出</span></p></td></tr><tr><td height="59" width="125" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #749530; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">已通過</span></p></td><td width="132" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">未處理</span></p></td><td width="665" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">專員已審核，建案資料正確，若客服審核其他資料無誤，通過審核，則建案開啟，若客服審核資料有誤放入待處理，則進入新建案刊登撥出列表，待跟進</span></p></td></tr><tr><td height="63" width="216" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新建案修改</span></p><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">先通過後審核</span></p></td><td width="125" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">未處理</span></p></td><td width="132" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">無</span></p></td><td width="665" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員修改建案後，需要專員審核，無誤則直接通過，有誤放入待處理則進入新建案修改撥出，待跟進</span></p></td></tr><tr><td rowspan="3" height="143" width="216" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新建案刊登撥出</span></p><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><br style="box-sizing: border-box;"></p><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">編輯許可權與客服許可權同時通過審核，建案才會開啟</span></p></td><td width="125" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">待處理</span></p></td><td width="132" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">待處理</span></p></td><td width="665" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">建案資料與其他資料（圖片等）均需修改</span></p></td></tr><tr><td height="48" width="125" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #749530; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">已通過</span></p></td><td width="132" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">待處理</span></p></td><td width="665" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">建案資料正確，專員已審核通過，其他資料還需修改（或客服還未審核）</span></p></td></tr><tr><td height="48" width="125" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">待處理</span></p></td><td width="132" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #749530; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">已通過</span></p></td><td width="665" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">其他資料正確，客服已審核通過，建案資料還需修改（或專員還未審核）</span></p></td></tr><tr><td rowspan="2" height="91" width="216" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新建案修改撥出</span></p><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><br style="box-sizing: border-box;"></p><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">編輯許可權通過審核即可</span></p></td><td width="125" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">待處理</span></p></td><td width="132" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">待處理</span></p></td><td width="665" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員修改的資料不符合規範，聯絡處理</span></p></td></tr><tr><td height="46" width="125" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">待處理</span></p></td><td width="132" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; text-align: center; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #749530; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">已通過</span></p></td><td width="665" style="padding: 2px 8px; box-sizing: border-box; margin: 0px; line-height: 2;"><p style="box-sizing: border-box; direction: ltr; unicode-bidi: embed;"><span style="box-sizing: border-box; color: #424242; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服檢查會員修改的資料已符合規範，等待專員審核即可</span></p></td></tr></tbody></table><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong><strong style="text-align: -webkit-center; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></strong></strong></span><br></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong>二、新建案新刊登審核及撥出處理流程</strong></span></p><p><br></p><table cellpadding="0" cellspacing="0"><colgroup><col width="153" style=";width:153px"><col width="88" style=";width:88px"><col width="90" style=";width:91px"><col width="263" span="2" style=";width:263px"><col width="321" style=";width:321px"></colgroup><tbody><tr height="21" style="height:21px" class="firstRow"><td rowspan="7" height="168" width="55"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新刊登审核作業流程</span></td><td width="120" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">刊登身份</span></td><td width="91" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">審核狀態</span></td><td width="124" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">審核人</span></td><td width="176" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">審核內容</span></td><td width="354" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">處理流程</span></td></tr><tr height="21" style="height:21px"><td rowspan="2" height="63" width="22"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">编辑刊登</span></td><td width="123" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">編輯狀態</span></td><td width="263" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">由编辑自行审核</span></td><td rowspan="2" width="108" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">物件所有內容</span></td><td rowspan="2" width="221" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">不管是否符合要求均由編輯自行確認</span></td></tr><tr height="42" style="height:42px"><td height="42" width="24"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服狀態</span></td><td width="295" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">系统默认已自動審核（客服無需審核編輯刊登的物件）</span></td></tr><tr height="21" style="height:21px"><td rowspan="4" height="84" width="22"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">会员刊登</span></td><td rowspan="2" width="123" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">編輯狀態</span></td><td rowspan="2" width="263" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">暫由星星審核</span></td><td rowspan="2" width="108" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">建造執照或使用執照</span></td><td width="221" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">符合要求，按審核通過</span></td></tr><tr height="21" style="height:21px"><td height="21" width="153"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">不符合要求，記錄問題點，放入待處理</span></td></tr><tr height="21" style="height:21px"><td rowspan="2" height="42" width="24"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服狀態</span></td><td rowspan="2" width="295" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">由客服審核</span></td><td rowspan="2" width="263" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">身份、真實性、圖片、其他基本資料</span></td><td width="153" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">符合要求，按審核通過</span></td></tr><tr height="21" style="height:21px"><td height="21" width="153"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">不符合要求，記錄問題點，放入待處理</span></td></tr><tr height="21" style="height:21px"><td rowspan="8" height="273" width="55"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">新刊登撥出作業流程</span></td><td width="120" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">刊登身份</span></td><td width="91" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">撥出狀態</span></td><td width="124" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">撥出人</span></td><td width="176" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">撥出內容</span></td><td width="354" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">處理流程</span></td></tr><tr height="21" style="height:21px"><td rowspan="2" height="42" width="23"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">編輯刊登</span></td><td width="123" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">編輯狀態</span></td><td colspan="3" rowspan="2" width="496" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">因均由編輯審核新刊登，故不存在需要客服撥出的內容</span></td></tr><tr height="21" style="height:21px"><td height="21" width="24"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服狀態</span></td></tr><tr height="42" style="height:42px"><td rowspan="5" height="210" width="23"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">会员刊登</span></td><td rowspan="3" width="123" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">編輯狀態</span></td><td rowspan="3" width="263" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服</span></td><td rowspan="3" width="108" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">建造執照或使用執照上待修改的內容</span></td><td width="221" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">直接修改成功，做好記錄，由星星每天檢查後按審核通過</span></td></tr><tr height="42" style="height:42px"><td height="42" width="157"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">需重新核實建照資料，提交給星星重新核實，無誤直接審核通過，有誤重新走待處理流程</span></td></tr><tr height="42" style="height:42px"><td height="42" width="157"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員要求自行修改，則跟會員約定修改時間，逾期則關閉廣告</span></td></tr><tr height="42" style=";height:42px"><td rowspan="2" height="84" width="24"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服狀態</span></td><td rowspan="2" width="295" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服</span></td><td rowspan="2" width="263" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">身份、真實性、圖片、其他基本資料，待修改的內容</span></td><td width="153" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">直接修改成功，做好記錄審核通過</span></td></tr><tr height="42" style="height:42px"><td height="42" width="157"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員要求自行修改，則跟會員約定修改時間，逾期則關閉廣告</span></td></tr></tbody></table><p><span style="color: #FF0000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">PS：以上撥出流程若無人接聽均只跟蹤兩天，兩天無人接聽則發手機簡訊通知關閉廣告</span></p><p><br></p><p><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">三、新建案修改審核及撥出處理流程</span></strong></p><p><br></p><table cellpadding="0" cellspacing="0" data-sort="sortDisabled"><colgroup><col width="153" style=";width:153px"><col width="88" style=";width:88px"><col width="90" style=";width:91px"><col width="263" span="2" style=";width:263px"><col width="321" style=";width:321px"></colgroup><tbody><tr height="21" style="height:21px" class="firstRow"><td rowspan="3" height="63" width="75"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">修改審核作業流程</span></td><td width="77" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">刊登身份</span></td><td width="98" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">審核狀態</span></td><td width="46" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">審核人</span></td><td width="104" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">審核內容</span></td><td width="306" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">處理流程</span></td></tr><tr height="21" style="height:21px"><td height="21" width="91"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">编辑刊登</span></td><td width="80" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">編輯狀態</span></td><td rowspan="2" width="106"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">星星</span></td><td rowspan="2" width="16"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">物件所有內容</span></td><td rowspan="2" width="325" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">符合要求，直接審核通過<br>不符合要求，記錄問題點，放入待處理</span></td></tr><tr height="21" style="height:21px"><td height="21" width="91"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">会员刊登</span></td><td width="80" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服狀態</span></td></tr><tr height="21" style="height:21px"><td rowspan="5" width="75" colspan="1"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">修改撥出作業流程</span></td><td width="77" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">刊登身份</span></td><td width="98" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">撥出狀態</span></td><td width="46" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">撥出人</span></td><td width="104" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">撥出內容</span></td><td width="306" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">處理流程</span></td></tr><tr height="42" style="height:42px"><td height="42" width="91"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">编辑刊登</span></td><td width="87" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">編輯狀態</span></td><td rowspan="4" width="106" colspan="1"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服</span></td><td rowspan="4" width="16" colspan="1"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">物件所有內容</span></td><td width="333" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">直接修改成功，做好記錄，并審核通過，由星星每天檢查後再按最終的審核通過</span></td></tr><tr height="42" style="height:42px"><td rowspan="3" width="91" colspan="1"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">会员刊登</span></td><td rowspan="3" width="87" style="" colspan="1"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服狀態</span></td><td width="382" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">需重新核實建照資料，提交給星星重新核實，無誤直接審核通過，有誤重新走待處理流程</span></td></tr><tr height="42" style="height:42px"><td height="42" width="228"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員要求自行修改，則跟會員約定修改時間，逾期則關閉廣告</span></td></tr><tr><td colspan="1" rowspan="1" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">若為編輯刊登的物件，則做好記錄，并提交給編輯群處理，無需跟蹤</span></td></tr></tbody></table><p><span style="color: #FF0000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">PS：以上撥出流程若無人接聽均只跟蹤兩天，兩天無人接聽則發手機簡訊通知關閉廣告</span></p><p><span style="color: #FF0000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><p><span style="color: #FF0000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><p><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">四、新建案審核規範（新刊登修改通用）</span></strong></p><p><br></p><table cellpadding="0" cellspacing="0"><colgroup><col width="161" style=";width:161px"><col width="594" style=";width:595px"></colgroup><tbody><tr height="21" style="height:21px" class="firstRow"><td height="21" width="162" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">一級分類</span></td><td width="371" style="word-break: break-all;" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">二級分類</span></td><td colspan="1" rowspan="1" valign="middle" width="251" style="word-break: break-all;" align="center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-align: -webkit-center;">三級分類</span></td></tr><tr height="21" style="height:21px"><td rowspan="2" height="42" width="162" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員身份</span></td><td width="371"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">仲介身份不可刊登廣告</span></td><td colspan="1" rowspan="1" valign="null" width="251" style="word-break: break-all;">/</td></tr><tr height="21" style="height:21px"><td height="21" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">註冊的建設及代銷公司與該案場不符不可刊登廣告</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;">/</td></tr><tr height="21" style="height:21px"><td rowspan="7" height="231" width="162" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">建築執照</span></td><td width="371" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">投資建設、建築設計、營造公司名稱要與建使照上查詢到的一致</span></td><td colspan="1" rowspan="1" valign="null" width="251" style="word-break: break-all;">/</td></tr><tr height="21" style="height:21px"><td height="21" width="509" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">若為預售屋建照必填，若為新成屋，建使照均需填寫完整。預推案僅編輯可刊登</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;">/</td></tr><tr height="42" style="height:42px"><td height="42" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">結構工程、棟戶規劃、樓層規劃、用途規劃、土地分區、停車方式、基地面積、建蔽率要核對一致</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;"><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">/</span></td></tr><tr height="42" style="height:42px"><td height="42" width="500" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">當樓棟規劃有店面或商鋪時，用途需為住商用</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;">/</td></tr><tr height="42" style="height:42px"><td height="42" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">當有多張建照或使照時，建蔽率可為60%的標準值，無需計算平均值；</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;">/</td></tr><tr height="42" style="height:42px"><td height="42" width="500" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">當有多張建照或使照時，基地面積需加總後除3.3057計算出坪數，且建使照號需一張一張填寫</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;">/</td></tr><tr height="21" style="height:21px"><td height="21" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">當查到建照或使照未詳細標準車位時，則為前院停車</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;">/</td></tr><tr height="168" style="height:168px"><td rowspan="14" height="630" width="162" style="word-break: break-all;" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">圖片</span></td><td width="371" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #FF0000;">封面：</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br>1、必須為外觀圖，若無外觀圖則按以下優先順序（优先级：成屋外观&gt;示意图外观&gt;现场结构体&gt;接待中心&gt;基地照片）<br></span><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、<span style="font-family: docs-Calibri; font-size: 14px; white-space: pre-wrap; background-color: #FFFFFF;">建案的封面圖片可以放基地的外觀照，因為有的現場會沒有模型，立面圖等等，所以會直接用基地現場的照片當封面照，但這是少數，還是會先以外觀圖、成屋等照片優先</span></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、預推案可以沒有封面</span></p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4、若為3D圖可備註僅供參考字樣，若非3D圖則不可備註任何文字<br></span><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5、封面圖兩邊不能留白（<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #FF0000;">注：若建案首頁查看照片沒有留白可通過</span>）</span></p></td><td colspan="1" rowspan="1" valign="null" width="251" style="word-break: break-all;"><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">放待處理聯絡</span></td></tr><tr height="42" style="height:42px"><td height="42" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">廣告宣傳圖：建案宣傳的圖片尽量要求4張以上，若达不到不强制</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;">/</td></tr><tr height="42" style="height:42px"><td height="42" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3D示意圖：包含模型照、外觀示意圖、室內空間示意圖共尽量5張以上，若达不到不强制</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;">/</td></tr><tr height="21" style="height:21px"><td height="21" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">格局圖：全區的平面圖</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;">/</td></tr><tr height="42" style="height:42px"><td height="42" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #FF0000;">建案實景圖：成屋必须5張以上、預售必须3張以上(不同角度、遠近)</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;"><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">放待處理聯絡</span></td></tr><tr height="42" style="height:42px"><td height="42" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #FF0000;">周邊環境圖：機能、交通、學區、綠地等必须7張照片以上</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;"><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">放待處理聯絡</span></td></tr><tr height="42" style="height:42px"><td height="42" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">樣品屋/實品屋：各空間照片共尽量8張以上，若达不到不强制</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;">/</td></tr><tr height="84" style="height:84px"><td height="84" width="500" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">平面家配圖：<br>1、平面圖需符合建照用途（如規劃住家當成店面使用）<br>2、平面圖不可違法二次施工</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;"><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">放待處理聯絡</span></td></tr><tr height="21" style="height:21px"><td height="21" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">交通位置圖：以清晰易懂為原則</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;">/</td></tr><tr height="21" style="height:21px"><td height="21" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">不可一图多用</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">放待處理聯絡（若圖片數量足夠，可直接處理）</span></td></tr><tr height="21" style="height:21px"><td height="21" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">圖片需真實有效</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;"><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">放待處理聯絡</span></td></tr><tr height="42" style="height:42px"><td height="42" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">需按照正確類別上傳相應的圖片，若有誤需協助移到正確位置</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">放待處理聯絡（若圖片數量足夠，可直接移到正確位置）</span></td></tr><tr height="21" style="height:21px"><td height="21" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">所有圖片上不可備註非保護電話外的聯絡電話</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">放待處理聯絡（邊邊角角可直接幫其馬賽克）</span></td></tr><tr height="21" style="height:21px"><td height="21" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">大小：最大支持10M，比例4:3</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;">/</td></tr><tr height="21" style="height:21px"><td rowspan="9" height="273" width="162" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">資料內容</span></td><td width="371" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">已完銷之建案不可刊登</span></td><td colspan="1" rowspan="1" valign="null" width="251" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">放待處理聯絡</span></td></tr><tr height="21" style="height:21px"><td height="21" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">無效建案名稱不可刊登</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">放待處理聯絡</span></td></tr><tr height="42" style="height:42px"><td height="42" width="500" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">建案名稱內添加其他描述性的文字不可刊登</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">直接編輯掉無關的文字</span></td></tr><tr height="42" style="height:42px"><td height="42" width="500" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">單價填寫錯誤</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">放待處理聯絡（別墅透天因沒有價格區間，無需確認）</span></td></tr><tr height="42" style="height:42px"><td height="42" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">非住宅用地建案廣告資訊內不可備註任何可作為住家用字樣</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">放待處理聯絡</span></td></tr><tr height="21" style="height:21px"><td height="21" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">不可刊登BTO或客制化建案</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">放待處理聯絡</span></td></tr><tr height="21" style="height:21px"><td height="21" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">文字不可備註聯絡電話</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">放待處理聯絡</span></td></tr><tr height="21" style="height:21px"><td height="21" width="500"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">不可重複刊登</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">放待處理聯絡</span></td></tr><tr height="42" style="height:42px"><td height="42" width="500" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">建案官網：不可放競品或非591網站的網址（如房地王、住展等）</span></td><td colspan="1" rowspan="1" valign="null" width="137" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">可直接刪除</span></td></tr></tbody></table><p><span style="font-size: 14px;"><span style="color: #FF0000;">PS：以上規則若未描述具體處理方式的，請參見第一、二點的處理方式</span></span></p><p><br></p><table cellpadding="0" cellspacing="0" width="801"><colgroup><col width="120" style="width: 120px;"><col width="148" style="width: 148px;"><col width="396" style="width: 396px;"><col width="270" style="width: 271px;"></colgroup><tbody><tr height="33" class="firstRow" style="height: 33px;"><td height="33" width="120" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">類型</span></td><td colspan="2" width="544" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">處理流程</span></td><td width="271" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">補充說明</span></td></tr><tr height="37" style="height: 37px;"><td rowspan="2" height="125" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">圖片類</span></td><td align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">聯絡到</span></td><td align="left" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">約定修改時間，未處理直接刪除或關閉廣告</span></td><td rowspan="2" width="271" align="left" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">同筆物件編號已有聯絡說明過，同類型無需再聯絡直接發手機簡訊刪除處理</span></td></tr><tr height="88" style="height: 88px;"><td height="88" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">聯絡不到</span></td><td width="396" align="left" valign="middle"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第一天：做好記</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">錄</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第二天：</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、發手機簡訊+關閉廣告+做好記錄</span></p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、若圖片張數已符合條件則直接刪除圖片，做好記錄</span></td></tr><tr height="55" style="height: 55px;"><td rowspan="2" height="117" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">資訊類</span></td><td align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">聯絡到</span></td><td align="left" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">約定修改時間，未處理直接關閉廣告</span></td><td rowspan="2" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">/</span></td></tr><tr height="62" style="height: 62px;"><td height="62" align="center" valign="middle"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">聯絡不到</span></td><td width="396" align="left" valign="middle"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第一天：做好記</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">錄</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第二天：發手機簡訊+關閉廣告+做好記錄</span></p></td></tr><tr height="41" style="height: 41px;"><td colspan="4" height="41" width="935"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">PS：一筆物件出現多個問題，但聯絡時只說明了一個問題，約定未處理直接關閉，第二天查看發現問題未確認完整且昨天約定的會員還未處理，則需再次電話聯絡確認<br><br></span></td></tr></tbody></table><p><br></p><p><span style="font-size: 14px; color: #000000;">其他建案規則，請參見</span><span style="font-size: 14px; color: #FF0000;"><span style="font-size: 14px; color: #548DD4;">《</span><a href="https://help.591.com.tw/content/53/149/tw/%E5%88%8A%E7%99%BB%E5%BB%BA%E6%A1%88%E8%B3%87%E8%A8%8A%E6%9C%89%E4%BB%80%E9%BA%BC%E8%A6%8F%E5%AE%9A%EF%BC%9F.html" target="_blank" style="color: rgb(84, 141, 212); text-decoration: underline;"><span style="font-size: 14px; color: #548DD4;">刊登建案資訊有什麼規定</span></a><span style="font-size: 14px; color: #548DD4;">》</span></span></p></div></div> <div><span class="button">我要編輯</span></div></li></ul> <h3 class="more-answer">我要補充</h3> <div style=""><div id="editor_99824742700467140" class="edui-default" style=""><div id="edui1" class="edui-editor  edui-default" style="width: 100%; z-index: 999;"><div id="edui1_toolbarbox" class="edui-editor-toolbarbox edui-default"><div id="edui1_toolbarboxouter" class="edui-editor-toolbarboxouter edui-default"><div class="edui-editor-toolbarboxinner edui-default"><div id="edui2" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui2&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui3" class="edui-box edui-button edui-for-fullscreen edui-default"><div id="edui3_state" onmousedown="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui3_body" unselectable="on" title="全屏" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui3&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui3&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui4" class="edui-box edui-button edui-for-source edui-default"><div id="edui4_state" onmousedown="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui4_body" unselectable="on" title="源代码" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui4&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui4&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui5" class="edui-box edui-button edui-for-undo edui-default"><div id="edui5_state" onmousedown="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui5_body" unselectable="on" title="撤销" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui5&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui5&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui6" class="edui-box edui-button edui-for-redo edui-default"><div id="edui6_state" onmousedown="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui6_body" unselectable="on" title="重做" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui6&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui6&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui7" class="edui-box edui-button edui-for-simpleupload edui-default"><div id="edui7_state" onmousedown="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui7_body" unselectable="on" title="单图上传" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui7&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui7&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"><iframe style="display: block; width: 20px; height: 20px; overflow: hidden; border: 0px; margin: 0px; padding: 0px; position: absolute; top: 0px; left: 0px; opacity: 0; cursor: pointer;"></iframe></div></div></div></div></div><div id="edui8" class="edui-box edui-button edui-for-imageleft edui-default"><div id="edui8_state" onmousedown="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui8_body" unselectable="on" title="左浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui8&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui8&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui9" class="edui-box edui-button edui-for-imageright edui-default"><div id="edui9_state" onmousedown="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui9_body" unselectable="on" title="右浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui9&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui9&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui10" class="edui-box edui-button edui-for-imagecenter edui-default"><div id="edui10_state" onmousedown="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui10_body" unselectable="on" title="居中" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui10&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui10&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui11" class="edui-box edui-separator  edui-default"></div><div id="edui12" class="edui-box edui-button edui-for-date edui-default"><div id="edui12_state" onmousedown="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui12_body" unselectable="on" title="日期" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui12&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui12&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui13" class="edui-box edui-button edui-for-time edui-default"><div id="edui13_state" onmousedown="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui13_body" unselectable="on" title="时间" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui13&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui13&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui14" class="edui-box edui-button edui-for-horizontal edui-default"><div id="edui14_state" onmousedown="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui14_body" unselectable="on" title="分隔线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui14&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui14&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui15" class="edui-box edui-separator  edui-default"></div><div id="edui16" class="edui-box edui-splitbutton edui-for-inserttable edui-default"><div title="插入表格" id="edui16_state" onmousedown="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui16_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onArrowClick();"></div></div></div></div><div id="edui19" class="edui-box edui-button edui-for-deletetable edui-default"><div id="edui19_state" onmousedown="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui19_body" unselectable="on" title="删除表格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui19&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui19&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui20" class="edui-box edui-button edui-for-insertrow edui-default"><div id="edui20_state" onmousedown="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui20_body" unselectable="on" title="前插入行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui20&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui20&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui21" class="edui-box edui-button edui-for-insertcol edui-default"><div id="edui21_state" onmousedown="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui21_body" unselectable="on" title="前插入列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui21&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui21&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui22" class="edui-box edui-button edui-for-mergeright edui-default"><div id="edui22_state" onmousedown="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui22_body" unselectable="on" title="右合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui22&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui22&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui23" class="edui-box edui-button edui-for-mergedown edui-default"><div id="edui23_state" onmousedown="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui23_body" unselectable="on" title="下合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui23&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui23&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui24" class="edui-box edui-button edui-for-deleterow edui-default"><div id="edui24_state" onmousedown="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui24_body" unselectable="on" title="删除行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui24&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui24&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui25" class="edui-box edui-button edui-for-deletecol edui-default"><div id="edui25_state" onmousedown="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui25_body" unselectable="on" title="删除列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui25&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui25&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui26" class="edui-box edui-button edui-for-splittorows edui-default"><div id="edui26_state" onmousedown="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui26_body" unselectable="on" title="拆分成行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui26&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui26&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui27" class="edui-box edui-button edui-for-splittocols edui-default"><div id="edui27_state" onmousedown="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui27_body" unselectable="on" title="拆分成列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui27&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui27&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui28" class="edui-box edui-button edui-for-splittocells edui-default"><div id="edui28_state" onmousedown="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui28_body" unselectable="on" title="完全拆分单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui28&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui28&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui29" class="edui-box edui-button edui-for-mergecells edui-default"><div id="edui29_state" onmousedown="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui29_body" unselectable="on" title="合并多个单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui29&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui29&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui36" class="edui-box edui-button edui-for-edittable edui-default"><div id="edui36_state" onmousedown="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui36_body" unselectable="on" title="表格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui36&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui36&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui41" class="edui-box edui-button edui-for-edittd edui-default"><div id="edui41_state" onmousedown="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui41_body" unselectable="on" title="单元格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui41&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui41&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui42" class="edui-box edui-separator  edui-default"></div><div id="edui43" class="edui-box edui-menubutton edui-for-lineheight edui-default"><div title="行间距" id="edui43_state" onmousedown="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui43_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onArrowClick();"></div></div></div></div><div id="edui52" class="edui-box edui-menubutton edui-for-rowspacingtop edui-default"><div title="段前距" id="edui52_state" onmousedown="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui52_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onArrowClick();"></div></div></div></div><div id="edui59" class="edui-box edui-menubutton edui-for-rowspacingbottom edui-default"><div title="段后距" id="edui59_state" onmousedown="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui59_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onArrowClick();"></div></div></div></div></div><div id="edui66" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui66&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui67" class="edui-box edui-combox edui-for-fontfamily edui-default"><div title="字体" id="edui67_state" onmousedown="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui67_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onButtonClick(event, this);">字体</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onArrowClick();"></div></div></div></div><div id="edui80" class="edui-box edui-combox edui-for-customstyle edui-default"><div title="自定义标题" id="edui80_state" onmousedown="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui80_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onButtonClick(event, this);">自定义标题</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onArrowClick();"></div></div></div></div><div id="edui86" class="edui-box edui-combox edui-for-paragraph edui-default"><div title="段落格式" id="edui86_state" onmousedown="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui86_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onButtonClick(event, this);">段落格式</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onArrowClick();"></div></div></div></div><div id="edui95" class="edui-box edui-combox edui-for-fontsize edui-default"><div title="字号" id="edui95_state" onmousedown="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui95_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onButtonClick(event, this);">字号</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onArrowClick();"></div></div></div></div><div id="edui110" class="edui-box edui-button edui-for-link edui-default"><div id="edui110_state" onmousedown="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui110_body" unselectable="on" title="超链接" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui110&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui110&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui111" class="edui-box edui-button edui-for-indent edui-default"><div id="edui111_state" onmousedown="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui111_body" unselectable="on" title="首行缩进" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui111&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui111&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui112" class="edui-box edui-button edui-for-bold edui-default"><div id="edui112_state" onmousedown="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui112_body" unselectable="on" title="加粗" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui112&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui112&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui113" class="edui-box edui-splitbutton edui-for-forecolor edui-default edui-colorbutton"><div title="字体颜色" id="edui113_state" onmousedown="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui113_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui113_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onArrowClick();"></div></div></div></div><div id="edui116" class="edui-box edui-splitbutton edui-for-backcolor edui-default edui-colorbutton"><div title="背景色" id="edui116_state" onmousedown="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui116_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui116_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onArrowClick();"></div></div></div></div><div id="edui119" class="edui-box edui-button edui-for-italic edui-default"><div id="edui119_state" onmousedown="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui119_body" unselectable="on" title="斜体" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui119&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui119&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui120" class="edui-box edui-button edui-for-underline edui-default"><div id="edui120_state" onmousedown="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui120_body" unselectable="on" title="下划线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui120&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui120&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui121" class="edui-box edui-button edui-for-strikethrough edui-default"><div id="edui121_state" onmousedown="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui121_body" unselectable="on" title="删除线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui121&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui121&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui122" class="edui-box edui-button edui-for-superscript edui-default"><div id="edui122_state" onmousedown="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui122_body" unselectable="on" title="上标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui122&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui122&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui123" class="edui-box edui-button edui-for-subscript edui-default"><div id="edui123_state" onmousedown="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui123_body" unselectable="on" title="下标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui123&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui123&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui124" class="edui-box edui-button edui-for-removeformat edui-default"><div id="edui124_state" onmousedown="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui124_body" unselectable="on" title="清除格式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui124&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui124&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui125" class="edui-box edui-button edui-for-blockquote edui-default"><div id="edui125_state" onmousedown="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui125_body" unselectable="on" title="引用" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui125&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui125&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui126" class="edui-box edui-button edui-for-pasteplain edui-default"><div id="edui126_state" onmousedown="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui126_body" unselectable="on" title="纯文本粘贴模式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui126&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui126&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui127" class="edui-box edui-menubutton edui-for-insertorderedlist edui-default"><div title="有序列表" id="edui127_state" onmousedown="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui127_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onArrowClick();"></div></div></div></div><div id="edui140" class="edui-box edui-menubutton edui-for-insertunorderedlist edui-default"><div title="无序列表" id="edui140_state" onmousedown="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui140_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onArrowClick();"></div></div></div></div><div id="edui147" class="edui-box edui-button edui-for-cleardoc edui-default"><div id="edui147_state" onmousedown="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui147_body" unselectable="on" title="清空文档" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui147&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui147&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div></div></div></div><div id="edui1_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;"><div id="edui1_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui1&quot;].showWordImageDialog();">点击上传</div><div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui1&quot;].hideToolbarMsg();">x</div><div id="edui1_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div><div style="height:0;overflow:hidden;clear:both;" class="edui-default"></div></div><div id="edui1_message_holder" class="edui-editor-messageholder edui-default"></div></div><div id="edui1_iframeholder" class="edui-editor-iframeholder edui-default" style="width: 100%; height: 300px; z-index: 999; overflow: hidden;"><iframe id="ueditor_0" width="100%" height="100%" frameborder="0" src="javascript:void(function(){document.open();document.write(&quot;<!DOCTYPE html><html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}
body{margin:8px;font-family:sans-serif;font-size:13px;}p{margin:5px 0;}</style><link rel='stylesheet' type='text/css' href='/static/ueditor/themes/iframe.css'/></head><body class='view' ></body><script type='text/javascript'  id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant0'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);</script></html>&quot;);document.close();}())"></iframe></div><div id="edui1_bottombar" class="edui-editor-bottomContainer edui-default"><table class="edui-default"><tbody class="edui-default"><tr class="edui-default"><td id="edui1_elementpath" class="edui-editor-bottombar edui-default"><div class="edui-editor-breadcrumb">元素路径:</div></td><td id="edui1_wordcount" class="edui-editor-wordcount edui-default">字数统计</td><td id="edui1_scale" class="edui-editor-scale edui-default" style="display: none;"><div class="edui-editor-icon edui-default"></div></td></tr></tbody></table></div><div id="edui1_scalelayer" class="edui-default"></div></div></div> <div class="edit-btn"><button type="button" class="button">提交回答</button> <label data-v-0fe26aab="" for="commonCheckbox" class="checkbox"><input data-v-0fe26aab="" type="checkbox" id="commonCheckbox" name="commonCheckbox"> <i data-v-0fe26aab=""></i> 需要彈窗，請勾選</label></div></div></div> <div class="index-right"><div class="right-bk"><h3 class="com-title"><a href="javascript:;">我的最爱</a></h3> <div class="item-box clearfix"><span>暫無數據</span></div></div> <div class="hotTag right-bk"><h3 class="com-title">热门标签</h3> <div class="hotTag-box item-box clearfix"><p>無數據</p></div></div> <div class="right-bk rank"><h3 class="com-title"><a href="javascript:;">冠軍排行榜</a></h3> <ul><li><div><span class="number">1</span> <img src="/static/timg.jpeg"> <span>王菊芳</span></div> <span class="score">612分</span></li><li><div><span class="number">2</span> <img src="/upload/head/10177.gif?time=1556271256"> <span>黃雪靜</span></div> <span class="score">258分</span></li><li><div><span class="number">3</span> <img src="/static/timg.jpeg"> <span>田霞</span></div> <span class="score">240分</span></li><li><div><span class="number">4</span> <img src="/static/timg.jpeg"> <span>曹超雲</span></div> <span class="score">168分</span></li><li><div><span class="number">5</span> <img src="/static/timg.jpeg"> <span>文宇</span></div> <span class="score">96分</span></li><li><div><span class="number">6</span> <img src="/upload/head/10217.png?time=1548059679"> <span>黃興興</span></div> <span class="score">78分</span></li><li><div><span class="number">7</span> <img src="/static/timg.jpeg"> <span>黃佳麗</span></div> <span class="score">62分</span></li><li><div><span class="number">8</span> <img src="/static/timg.jpeg"> <span>马泽鹏</span></div> <span class="score">46分</span></li><li><div><span class="number">9</span> <img src="/static/timg.jpeg"> <span>陳敏南</span></div> <span class="score">40分</span></li><li><div><span class="number">10</span> <img src="/static/timg.jpeg"> <span>Cs-刘婷</span></div> <span class="score">10分</span></li><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></ul></div> <div class="activeUser right-bk"><h3 class="com-title">活跃用户</h3> <div class="activeUser-box"><a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a></div></div> <img src="data:image/png;base64,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" class="to-top"></div></div> <!----> <!----> <!----> <div class="page-cnt footer"><p>T5客服知识库<span>|</span>zsk.591.com.tw</p></div></div></div><script type="text/javascript" src="/js/manifest.f192b.js"></script><script type="text/javascript" src="/js/vendor.17570.js"></script><script type="text/javascript" src="/js/app.58254.js"></script><div id="edui_fixedlayer" class="edui-default" style="position: fixed; left: 0px; top: 0px; width: 0px; height: 0px;"><div id="edui148" class="edui-popup  edui-bubble edui-default" onmousedown="return false;" style="display: none;"> <div id="edui148_body" class="edui-popup-body edui-default"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank" class="edui-default"></iframe> <div class="edui-shadow edui-default"></div> <div id="edui148_content" class="edui-popup-content edui-default">  </div> </div></div></div></body></html>