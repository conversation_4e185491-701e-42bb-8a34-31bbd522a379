<html class=" "><head><meta charset="utf-8"><title>591客服知識庫</title><link rel="stylesheet" href="/static/font/font-awesome.min.css"><link href="/css/app.e5aa4.css" rel="stylesheet"><script type="text/javascript" charset="utf-8" async="" src="/js/questionDetail.d0a6b.js"></script><style type="text/css">.que-detail-page .index-left .button{width:74px;height:32px;line-height:32px;font-size:13px;margin-right:10px}.que-detail-page .index-left .button:hover{color:#fff}.que-detail-page .detail-box{padding:20px 30px;border:1px solid #e5e5e5;border-radius:4px}.que-detail-page .detail-box h3{font-size:18px;color:#333;margin-bottom:10px}.que-detail-page .detail-box h3 span{color:#f77d24}.que-detail-page .detail-box .add-fav{cursor:pointer}.que-detail-page .d-b-menu{margin:15px 0 25px;font-size:13px;color:#999}.que-detail-page .d-b-menu b{display:inline-block;font-weight:400;margin:0 10px}.que-detail-page .d-b-menu .r{float:right;color:#f77d24}.que-detail-page .d-b-menu .r .fa{margin-right:5px;font-size:16px}.que-detail-page .d-b-cnt{margin-bottom:25px;font-size:13px;color:#666}.que-detail-page .submit-ask{display:block;margin:32px auto 0}.que-detail-page .reply-list{margin:30px 0 10px;overflow:hidden}.que-detail-page .reply-list .reply-item{padding:15px 30px 25px;margin-bottom:30px;border-radius:4px;border:1px solid #e5e5e5}.que-detail-page .reply-list .reply-item h4{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;line-height:50px;font-size:13px}.que-detail-page .reply-list .reply-item h4 img{width:40px;height:40px;border-radius:50%}.que-detail-page .reply-list .reply-item h4 .time{color:#999}.que-detail-page .reply-list .reply-item h4 .button{-ms-flex-item-align:center;align-self:center}.que-detail-page .reply-list .reply-item h4 .delete{color:#666;cursor:pointer}.que-detail-page .reply-list .reply-item .reply-detail{padding:20px 0 30px}.que-detail-page .more-answer{font-size:18px;color:#666;margin-bottom:5px}.que-detail-page .editor-content table{width:100%;border:1px solid #ddd}.que-detail-page .editor-content table td,.que-detail-page .editor-content table th{border:1px solid #ddd;padding:2px 8px;line-height:2}.que-detail-page .editor-content img{max-width:100%;vertical-align:text-bottom}.que-detail-page .edit-btn{text-align:center;margin-top:20px}.que-detail-page .edit-btn .edit-cancle{background:#aaa}</style><style type="text/css">.head-info{line-height:26px;background:#f5f5f5;font-size:13px;color:#999}.head-info a{margin-left:20px;color:#333}.head-search{margin-top:22px}.head-search img{float:left}.head-search .srh-box{float:left;margin:20px 0 0 20px}.head-search .srh-box .srh-input{float:left;width:260px;height:38px;line-height:38px;border:2px solid #37a508;border-radius:4px 0 0 4px}.head-search .srh-box a,.head-search .srh-box button{float:left;width:80px;height:38px;line-height:36px;font-size:16px;color:#fff}.head-search .srh-box .srh-btn{border-radius:0 4px 4px 0}.head-search .srh-box .ask-btn{margin-left:10px}.head-nav{float:left;margin:20px 0 0 60px;color:#fff;font-size:16px;line-height:36px}.head-nav a{display:block;height:100%;color:#333}.head-nav li{position:relative;float:left;margin-right:35px}.head-nav .active a,.head-nav li:hover a{color:#30b940}.head-nav .active:after,.head-nav li:hover:after{display:block;content:"";height:3px;width:100%;background:#30b940}.head-pic{width:100%;height:90px;margin-top:20px;background:url(/static/banner.png) top no-repeat}</style><style type="text/css">.top-breadcrumb{height:20px;margin-top:10px;margin-bottom:40px;font-size:13px;color:#666}.top-breadcrumb .t-b-icon{color:#30b940}.top-breadcrumb span{margin-left:8px}.top-breadcrumb span .fa{font-size:16px;margin-left:8px}.top-breadcrumb span:first-child{margin-left:0}.top-breadcrumb .now{color:#30b940}</style><script type="text/javascript" src="/static/ueditor/ueditor.config.js?20210319" id="configScriptTag"></script><script type="text/javascript" src="/static/ueditor/ueditor.all.js?20210319" id="editorScriptTag"></script><style type="text/css">.checkbox[data-v-0fe26aab]{word-spacing:4px;vertical-align:bottom;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.checkbox i[data-v-0fe26aab]{display:inline-block;width:14px;height:14px;border-radius:2px;border:1px solid #ccc;background:#fff;position:relative;top:1px}.checkbox input[data-v-0fe26aab]{opacity:0;width:0;height:0;margin:0}.checkbox input:checked+i[data-v-0fe26aab]{background:#30b940;border-color:#30b940}.checkbox input:checked+i[data-v-0fe26aab]:before{content:"";position:absolute;left:2px;top:1px;width:11px;height:6px;border-bottom:2px solid #fff;border-left:2px solid #fff;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}</style><style type="text/css">.popbox{position:fixed;top:50%;left:50%;z-index:1011;background:#fff;border-radius:6px;overflow:hidden}.popbox h3{font-size:16px;line-height:40px;padding:0 20px}.popbox h3 span{float:right;color:#666;font-size:13px;cursor:pointer}.popbox .pb-cnt{padding:0 20px;font-size:13px}.popbox .pb-btn-area{position:absolute;bottom:0;left:0;z-index:1;width:100%;text-align:right;padding:0 20px 20px 0}.popbox .pb-btn-area button{height:32px;line-height:32px;border:0;background:#fff;cursor:pointer;border-radius:6px;margin-left:15px;padding:0 15px}.popbox .pb-btn-area button.pb-btn-ok{background:#daf3cf;color:#279005}.popbox .pb-btn-area button.pb-btn-ok:hover{background:#279005;color:#fff}</style><style type="text/css">.shadow{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1010;background:#000}</style><style type="text/css">.footer{margin-top:70px;margin-bottom:20px;padding-top:20px;text-align:center;color:#999;border-top:1px solid #f5f5f5}.footer span{margin:0 10px}</style><script src="/static/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript" defer="defer"></script><link href="/static/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet"><script src="/static/ueditor/third-party/codemirror/codemirror.js" type="text/javascript" defer="defer"></script><link rel="stylesheet" type="text/css" href="/static/ueditor/third-party/codemirror/codemirror.css"><script src="/static/ueditor/third-party/zeroclipboard/ZeroClipboard.js" type="text/javascript" defer="defer"></script><style type="text/css">.activeUser,.favorite,.hotTag,.rank{margin-bottom:40px}</style><style type="text/css">.rank .rank-page{height:60px;line-height:60px;font-size:18px;margin-bottom:20px;text-align:center;background:#fafafa;border-radius:5px}.rank ul{padding:0 20px}.rank li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;border-bottom:1px dotted #f5f5f5;line-height:50px}.rank li .flag{position:relative;display:inline-block;width:20px;height:30px;background:#476472;vertical-align:middle;margin-right:12px;color:#fff;text-align:center;line-height:20px;font-size:13px;font-style:normal}.rank li .flag:after{content:"";position:absolute;bottom:-4px;left:-4px;border:14px solid transparent;border-bottom-color:#fff}.rank li:first-child .flag{background:#fb6757}.rank li:nth-child(2) .flag{background:#fac525}.rank li:nth-child(3) .flag{background:#00a9fb}.rank li img{width:36px;height:36px;border-radius:50%;margin-right:12px}</style><style type="text/css">.activeUser .activeUser-box a{display:inline-block;width:64px;height:64px;border-radius:50%;overflow:hidden;margin:0 6px 12px}</style><style type="text/css">.to-top{display:none;position:fixed;bottom:150px;z-index:5;width:50px;height:50px;cursor:pointer}</style></head><body style=""><div id="app" style=""><div class="que-detail-page" style=""><div class="wiki-hd"><div class="head-info"><p class="page-cnt">
      您好，欢迎来到591客服知识库
      <a href="https://www.591.com.tw/admin.php?new_url=https://zsk.591.com.tw">[请登入]</a> <!----></p></div> <div class="page-cnt head-search clearfix"><a href="/" class="router-link-active"><img src="/static/logo.png"></a> <ul class="head-nav clearfix"><li class="router-link-active active"><a href="/">591房屋交易网</a></li> <li class=""><a href="/life">客服充电站</a></li> <li class=""><a href="/notification">通知事项</a></li></ul> <div class="srh-box"><input type="text" placeholder="请输入搜索内容" class="srh-input"> <button type="button" class="button srh-btn">搜索</button> <a href="/ask/591" class="button button-orange ask-btn">提問</a></div></div> <div class="head-pic"></div></div> <div class="top-breadcrumb com-width"><span><a href="/collection/591/1302" class="">客服質檢相關<i class="fa fa-angle-double-right"></i></a></span><span><a href="/collection/591/1302/1661" class="">星級練習題<i class="fa fa-angle-double-right"></i></a></span><span><strong class="now">问题详情</strong></span></div> <div class="com-width clearfix" style=""><div class="index-left" style=""><div class="detail-box"><h3><!---->T5-二星筆試考核練習題</h3> <p class="clearfix d-b-menu"><span class="l">
            黃雪靜&nbsp;&nbsp;&nbsp;浏览151次&nbsp;&nbsp;&nbsp;2020-08-11 10:18:19
          </span> <span class="r"><span class="add-fav"><i class="fa fa-heart"></i>加入最爱</span></span></p> <div class="d-b-cnt editor-content big-img-parent"><div><p>如題</p></div></div> <div class="d-b-button"><a href="/edit/591/4199" class="button">编辑</a> <button class="button button-orange">关闭提问</button></div></div> <div class="hr-line"></div> <ul class="reply-list big-img-parent"><li class="reply-item"><h4><div><img src="/upload/head/10177.gif?time=1556271256">&nbsp;&nbsp;&nbsp;
              <span class="user">黃雪靜</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-08-11 10:40:05</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="text-indent: 2em; text-align: center;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"></span></strong></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:center;text-indent:32px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><span style="font-size: 24px; color: black;">填空類練習題</span></strong></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>廣告一經開啟，_</span><span style="font-size: 15px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;</span><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">_小時後會員無法自行操作修改_&nbsp;</span><span style="font-size: 15px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">_、_&nbsp;</span><span style="font-size: 15px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">_、_&nbsp;</span><span style="font-size: 15px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">_</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>多筆出售黃金套餐售價為：__元，_&nbsp;_筆；季約黃金套餐售價為：_&nbsp;_元，_&nbsp;_筆</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>客服電話是：__；客服信箱是：_&nbsp;_</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">4.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>網站提供哪些免費刊登的頻道：_ _、_ _、_ _、_ _</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">5.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>網站使用國泰世華信用卡儲值最少金額為：__元；最高：_ _</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">6.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>591發票品名是：_ _；網站發票是否含稅_ _</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">7.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>新刊登坪數規範為：整層住家__、獨立套房_ _、分租套房_ _、雅房_ _</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">8.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>發票二聯改三聯需要讓會員提供：_ _</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">9.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>網站禁止刊登的廣告或商品有什麼，最少寫4種：_ _、_ _、__、_ _</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">10.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>新刊登或修改廣告備註已出租聯絡兩天均無人接聽該如何處理：_ _</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">11.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>會員將廣告暫時關閉我們需要提醒什麼：_ _；廣告做成交下架需提醒什麼：_ _</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">12.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>什麼情況下需要建回撥，最少寫兩點：_ _</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">13.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>非會員_ _進行留言；留言什麼情況會曝光在網路上：_ _</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">14.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>什麼是設備驗證：____________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">15.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>修改什麼情況下需要電話聯絡確認，最少寫四點：______、______、______、______</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">16.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>網站全稱是：____________；客服信箱是：____________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">17.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>搜尋頁面的廣告未點擊標題進去查看則標題呈現黑色，點擊廣告詳情頁查看之後再退出來標題呈現灰色，若會員要將標題重新變為黑色的狀態該如何處理___________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">18.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>ATM跨行轉帳需要收取__________元手續費，銀行臨櫃非國泰世華儲值需收取________元手續費</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">19.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>發票二聯改三聯需要讓會員提供：_____________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">20.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>出租商用套餐方案刊登廣告，將欄位元升級為超級曝光需要________元，升級為黃金曝光需要________元</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">21.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>什麼是保護電話：_____，啟用保護電話之後網站使用_____電話顯示</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">22.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>子帳號為_____作為登入帳號，子帳號E-mail中途_____修改</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">23.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>出租住宅台南_____、_____、_____、_____可備註日租或民宿</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">24.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>網站刊登規則廣告上傳什麼樣的照片是不可通過的_____、_____、_____、_____（請寫四種）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">25.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>修改審核獨立套房修改_____可審核通過</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">26.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>修改備註已成交，第一天聯絡會員不配合該如何處理：___________；第二天仍不配合該如何處理：___________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">27.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>檢舉者來電檢舉代理人冒充屋主刊登廣告，已聯絡表示是幫親戚刊登的，不願意將廣告聯絡人身份修改為代理人，我們該如何處理___________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">28.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>使用蘋果手機APP儲值需收取_____手續費，發票由_____開立</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">29.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>即時問答內容可以保存多久_____；即時問答內容可以刪除麼（iOS/Android）：___________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">30.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>網站出售套餐更新規則是：_________________；若會員手動操作更新一次會減少哪個時間段的自動更新次數：______________________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">31.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>客服需早上______________整準時在位，此時電腦已打開、有度處於登錄狀態</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">32.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>檢查______________、話機、______________是否可用，準備筆、筆記本用於記錄；</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">33.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>電話響______________聲後接聽，第______________聲響前接起</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">34.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>工作時，接聽超過1分鐘的私人電話，請移步至_____________或__________接聽；</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">35.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>接聽電話過程中，請教其他客服問題要先按______________，然後再詢問</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">36.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>一筆物件僅能操作續刊_______次，套餐方案_______操作續刊</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">37.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>即時問答和留言管理有什麼區別：_______</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">38.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>註冊資料中分別有提供_______、_______、_______、_______、發票處理方式</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">39.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>便利超商繳費每次儲值最低不能少於_______不得大於_______</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">40.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>會員ATM儲值帳號除了8928還有_______和_______</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">41.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>廣告一經開啟_______無法會員自行修改什麼_______、_______、_______</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">42.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>出租住宅類黃金套餐售價為_______、筆數為_______、刊登時間為_______、</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">43.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>欄位元元升級VIP需要_______元</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">44.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>檢舉者來電檢舉仲介冒充屋主、代理人刊登廣告，已聯絡表示自己仲介但不想讓別人知道，不願意配合修改，我們該如何處理_______</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">45.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>會員來電修改物件資料最少需核實_______項資料，修改帳號資料最少核實_______項資料。</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">46.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>什麼是即時問答_______</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">47.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>APP如何取消收藏物件_______</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">48.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>公司證明是_______，折讓單是_______</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">49.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>補寄發票需提供_____________和_____________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">50.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>如何進入觸屏版：_____________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">51.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>刊登建案的流程：_____________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">52.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>網站提供哪些免費刊登項目：_______________、_______________、_______________、_______________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">53.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>忘記密碼使用信箱操作取回密碼的完整步驟是：_______________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">54.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>出租住宅類超級套餐售價為：____________；多少筆：_________；商用類新手套餐售價為：_________；多少筆：_________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">55.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>出租黃金曝光方案的排序規則是：_______________；VIP系統自動設定的更新排序規則是：_______________</span></p><p class="MsoListParagraph" style="margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">56.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>7-11儲值每次繳款金額不能少於</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">：___________，不得大於</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">：___________，手續費為：___________，入帳時間是：___________</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">57.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>開啟中的物件，哪些資料可修改，哪些不可修改：____________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">58.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>屋主來電檢舉自己的房屋並未委託給仲介刊登，已提供物件編號，客服聯絡兩天刊登者兩天均無人接聽，該如何處理：__________________________________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">59.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>新刊登坪數規範為整層住家：______________&nbsp;、獨立套房：______________、分租套房：______________、雅房：______________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">60.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>新刊登審廣告格局上下不一致，第一天聯絡不配合該如何處理：______________；第二天聯絡仍然不願配合該如何處理：______________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">61.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>審核修改廣告主要的判斷條件方法有哪些，最少寫四點：__________________________________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">62.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>哪幾種情況下的會員身份是不可刊登建案廣告的：__________________________________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">63.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>修改備註已成交，第一天聯絡會員不配合該如何處理：______________；第二天仍不配合該如何處理：______________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">64.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>新刊登法案待處理物件會員已上傳房屋藤本，客服查看無法匹配，聯絡會員一天無人接聽該如何處理：______________；第二天仍然無人接聽該如何處理：______________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">65.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>會員使用ATM繳費，儲值帳號除了8928還有_________和_________可作為備用帳號</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">66.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>如何修改APP頭像：________________________；此頭像會顯示在哪些頁面上呢：________________________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">67.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>會員來電回饋自己的廣告在搜尋頁面標題等都呈現灰色字體，請問是為什麼：________________________；要如何恢復黑色字體狀態：________________________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">68.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>新建案提供哪幾種刊登類型：________________________；分別由誰（會員或編輯）可以刊登：________________________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">69.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>哪些縣市不可購買出售2500元黃金推薦服務：________________________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">70.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>銀行臨櫃儲值需要填寫哪些資料（請完整作答）:&nbsp;________________________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">71.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>網站禁止刊登的廣告類型或商品有哪些，最少寫四個：________________________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">72.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>會員使用超級VIP方案刊登出租住家廣告，系統每天可自動更新排序四次，會表示不想設定更新時間，想自己當天什麼時間更新就手動去操作，該如何處理：________________________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">73.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>591客服電話是：________________________；客服傳真號碼是：________________________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">74.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>使用手機APP可搜尋哪幾種類型的廣告：________________________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">75.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>會員詢問自己是仲介身份需要上傳大頭照有什麼規定麼：________________________；圖片格式有什麼要求麼：________________________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">76.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>頂讓刊登時間為：_____；出租住宅單筆刊登時間為：_____；新建案刊登時間為：_____；求購廣告刊登時間為：_____</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">77.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>修改審核到該物件重複更換，聯絡兩次無人接聽已還原資訊，今日審核到該筆廣告仍重複更換，該如何處理：_____；同筆物件重複更換已聯絡過，再次更換如何處理：________________________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">78.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>必看好屋的三個硬性條件是：________________________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">79.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>網友檢舉物件R123已成交但還曝光在網路上，要求下架，客服聯絡刊登者承認該如何處理：________________________；不承認該如何處理：________________________；不確定：________________________；無人接聽：________________________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">80.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>廣告開啟之後地圖顯示太平洋，請問為什麼：________________________ ，該如何處理：________________________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">81.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>什麼是即時問答：________________________；即時問答內容可保存多久_______________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">82.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>新刊登出租住宅哪個縣市和區域是可以備註日租或民宿的：</span><span style="font-size: 15px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">_____________；_____________</span></span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">83.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>新建案每條動態僅可修改：</span><span style="font-size: 15px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">____</span><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">次；建案活動規則資訊描述不能超過：</span><span style="font-size: 15px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">______</span><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">字</span></span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">84.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>什麼情況下剛刊登的物件不會出現“新”的標籤：</span><span style="font-size: 15px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">_____________</span></span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">85.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>刊登廣告產生的增值稅由誰負責繳納：</span><span style="font-size: 15px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">_____________</span></span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">86.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>什麼是標租、標售：</span><span style="font-size: 15px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">_____________</span></span></p><p style="margin-left:28px"><span style="font-size: 15px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">87.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>VIP系統自動設定的更新排序規則是：_____________；行動版精選推薦曝光排序規則為：_____________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">88.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span></span><span style="font-size: 15px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">出租住宅單筆廣告刊登時間為：____；廣告到期下架時間為：____；出租住宅套餐到期時間為：____；櫥窗有效期是：____</span></span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">89.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>ATM跨行轉帳需收取</span><span style="font-size: 15px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">____</span><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">手續費；便利超商需收取：</span><span style="font-size: 15px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">____</span><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">手續費；銀行臨櫃其它銀行需收取：</span><span style="font-size: 15px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">____</span><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">手續費；信用卡儲值需收取：</span><span style="font-size: 15px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">____</span><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">手續費</span></span></p><p style="margin-left:28px"><span style="font-size: 15px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">90.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>租、售、新建案、求租、求購、二手傢俱的物件首字母分別是：____</span></p><p style="margin-left:28px"><span style="font-size: 15px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">91.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>廣告上傳照片支持：_____________格式，單張照片大小控制在：_____________以內</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">92.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>什麼情況下需要建回撥：_____________</span><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">；當日所建回撥需要在幾天內處理好：</span><span style="font-size: 15px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">_____________</span></span></p><p style="margin-left:28px"><span style="font-size: 15px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">93.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>網站出售套餐更新規則是：_____________；若會員手動操作更新一次會減少哪個時間段的自動更新次數：_____________</span></p><p style="margin-left:28px"><span style="font-size: 15px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">94.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>什麼是保護電話：_____________，啟用保護電話之後網站使用_____________電話顯示</span></p><p style="margin-left:28px"><span style="font-size: 15px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">95.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>修改備註已成交，第一天聯絡會員不配合該如何處理：_____________；第二天仍不配合該如何處理：_____________</span></p><p style="margin-left:28px"><span style="font-size: 15px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">96.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>網友檢舉仲介冒充屋主/代理人，網站處理流程是聯絡兩天均無人接聽：_____________；若此帳號多次被人檢舉（大於等於3次）該如何處理：_____________</span></p><p style="text-indent: 2em; text-align: center;"><br></p><p style="text-indent: 2em;"><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/upload/head/10177.gif?time=1556271256">&nbsp;&nbsp;&nbsp;
              <span class="user">黃雪靜</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-08-11 10:45:34</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><span style="font-size: 24px; color: black;">判斷</span></strong><strong><span style="font-size: 24px; color: #333333;">類練習題</span></strong></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>除出租商用套餐外，其他套餐方案均不可直接升級為：超級曝光或黃金曝光（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：_______</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>會員中心帳號管理-討論區-昵稱一旦設定客服及會員均無法操作修改（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：_______</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>子帳號可以制添加6個；子帳號為郵箱作為登入帳號（郵箱不可在其他註冊的帳號中使用）且中途不可修改只能停用（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：_______</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">4.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>出租商用、車位及出售廣告可備註民宿、旅館業、日租、周租、按天計算（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：____________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">5.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>修改規則：重複更換聯絡第一天無人接聽發手機簡訊通知，第二天仍無人接聽直接還原資訊，若下次審核到仍更換則直接還原資訊處理（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：___________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">6.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>網友檢舉仲介冒充屋主/代理人，網站處理流程是聯絡兩天均無人接聽，發手機簡訊通知+做好記錄，不再跟進；若此帳號多次被人檢舉（大於等於3次）需將帳號暫時停權，等會員來電確認（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：___________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">7.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>蘋果app可以操作開啟頂讓廣告，安卓app不可以操作開啟頂讓廣告（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：___________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">8.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>廣告有效期到6月28號，會員在6月25號有操作續刊，點數已扣除，但房屋在6月26號已出租並簽約成功會員將其成交下架，續刊時間未使用點數仍已扣除，系統不會自動退還（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：___________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">9.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>廣告上傳照片支援jpg、gif、pig格式，單張照片大小控制在25M以內（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：___________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">10.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>檢舉者來電檢舉法拍屋刊登在中古屋頻道時，客服的處理流程是：無需至司法院核實，也無需電話聯絡刊登者，直接發手機簡訊通知被檢舉者，起到提醒作用就好，不再繼續跟蹤（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：___________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">11.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>接聽電話過程中，要保持端正的坐姿，可以東張西望，可以趴在桌子上（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">12.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>會員要求客服提供私人資料時，只能提供自己的客服編號與姓氏（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">13.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>接聽電話過程中很急著的話，可以說“喂”（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">14.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>會員不出聲，冷場，客服可以直接掛斷電話 (&nbsp; )</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">15.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>若會員一直謾罵不停…客服不可將電話機擱置一旁（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">16.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>開頭語：“客服部您好，敝姓X，很高興為您服務！”（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">17.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>若記錄回撥需在3天之內回覆處理（&nbsp; ）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">18.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>接聽電話過程中，有私人電話響起，趕緊接完私人電話，再服務會員（&nbsp; ）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">19.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>下班時，請關閉電腦主機和顯示器，斷絕電源後方可離開（&nbsp; ）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">20.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>碰到突發狀況自己無法處理時，要及時將該訊息回饋給主管助理(切記勿推諉或隱瞞)（&nbsp; ）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">21.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>網站上傳圖片格式的要求為JPG、PNG、GIF、PDF（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">22.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>出租住宅/出售套餐無法使用蘋果手機APP操作開啟廣告，出租商用套餐可以使用APP操作開啟（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">23.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>客服接聽電話的流程是：接起—報問候語—查詢—溝通互動—傾聽—結束語—掛機（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">24.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>註冊會員資料時，姓名可以填寫中文或英文名字及數位（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">25.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>房屋已租出並簽約成功但是廣告時間還未到期，會員可直接將廣告做關閉而不下架（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">26.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>發票二聯改三聯需要讓會員提供：折讓單+公司證明+原發票編號+折讓單寄送地址（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">27.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>公司名稱是數位科技股份有限公司，公司電話：02-29995691、客服電話：02-55722000、客服傳真：02-55793400（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:28px;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">28.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>會員昵稱可以至會員中心帳號管理-討論區-討論區資料自行設定並隨時修改（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:28px;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">29.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>帳號身份為仲介目前有刊登2筆物件為：仲介，不須服務費，來電需將帳號身份變更為屋主/代理人，可以幫其變更對嗎（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:28px;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">30.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>會員物件刊登5天后自行修改廣告金額、坪數、格局，新刊登修改審核人員可直接審核通過（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">31.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>0950、公司節費電話、網路電話等均可註冊成為會員（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：_______________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:28px;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">32.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>會員已用電腦操作填寫並保存頂讓廣告資料，可使用安卓或蘋果手機APP將頂讓廣告資訊開啟（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：_______________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">33.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>使用信箱操作取回密碼的步驟是：輸入註冊時填寫的E-mail和會員帳號，系統會發送找回密碼的申請至信箱，並從專屬連結進入設置新的密碼即可（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：________________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">34.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>591月臺免費刊登的頻道有：新建案、二手傢俱、求租求購（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：________________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">35.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>591發票品名是：網路服務收入，網站發票不含稅（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：_________________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">36.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>網站發票寄送方式為平信寄送，若會員強烈要求掛號寄送需回饋至專員申請，若同意則需告知會員郵費自行承擔，會員同意即可幫其特殊申請掛號寄送（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：___________________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">37.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>蘋果app不可以用出售套餐刊登廣告，不可以用出租住宅套餐刊登廣告；安卓app不可以用出租住宅套餐刊登廣告（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：____________________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">38.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>出售套餐到期前七天系統會下發手機簡訊通知，而套餐筆數刊登中的物件到期前七天系統不會再下發站內簡訊通知（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：____________________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">39.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>網站廣告流覽人數統計規則為：同一設備同一天內流覽同一筆廣告多次，均計算1次流覽人數（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：____________________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">40.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>會員刊登住家出租廣告使用單筆或套餐方案刊登，若刊登中途暫時不想將廣告曝光在網路上，可自行在會員中心將廣告暫時關閉，暫時關閉時間會照常計算，在有效期之內均可自行開啟（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：____________________</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">41.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>封面：必須為外觀圖，若無外觀圖則按以下優先順序（優先順序：成屋外觀&gt;示意圖外觀&gt;現場結構體&gt;接待中心&gt;基地照片（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:28px;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">42.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>封面圖兩邊可以留白（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">43.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>建案特色說明可以備註聯絡電話（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">44.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>建案官網：不可放競品或非591網站的網址（如房地王、住展等），不可直接刪除 (&nbsp; )</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">45.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>不可刊登BTO或客制化建案（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">46.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>建案相冊可以重複使用（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">47.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>廣告宣傳圖：建案宣傳的圖片儘量要求4張以上，若達不到不強制（&nbsp; ）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">48.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>建案照片不符合要求，直接放入待處理（&nbsp; ）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">49.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>若新建案刊登聯絡兩天無人接聽則發站內簡訊通知關閉廣告（&nbsp; ）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">50.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>每條建案動態一天可修改多次（&nbsp; ）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">51.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>店面不可備註攤位（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:28px;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">52.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>整棟住家出租可備註分租每一間房間的金額（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">53.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>廣告欄位為套房，備註住家格局為2房或以上（注：挑高、樓中樓等除外）可以通過（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">54.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>總樓層填寫6F（實際是B1-5F）不需確認（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">55.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>坪數相差1坪含以內可以通過（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">56.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>坪數上下不一致，放待處理（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">57.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>金額備註***元起，直接將“起”編輯（納入違規次數）（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">58.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>可以備註政府官方網址（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">59.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>社會住宅欄位填寫最低金額（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">60.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>“<strong>搜尋</strong></span><strong><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">FB</span></strong><strong><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">中信房屋內壢</span></strong><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">”可以審核通過（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">61.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>廣告備註：https://www.hc591.com.tw/ 可以通過（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">62.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>台南地區（北區、東區、西區、安平區）可以備註日租、周租、按天計算或民宿等（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">63.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>可以上傳QRCode圖片（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">64.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>一張照片可以標識文字二處或以上（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">65.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>前三次違規發手機簡訊通知（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">66.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>土地是以土地為主，無地上物或地上物為輔（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">67.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>出租住家的坪數是10坪以上（不含10坪）（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">68.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>廣告樓層透天與別墅不可相互備註（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">69.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>出租樓層為+1(頂樓加蓋)，總樓高為5F，標題或者特色備註6F/6F，放待確認樓層（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:0;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">70.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>辦公出租1坪、備註借址營登可以審核通過（</span> <span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤的原因：</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">b)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span></span><strong><span style="font-size: 15px; color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br> <br> </span></strong></span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">71.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>店鋪流覽人數從開通日起開始計算，中途關閉店鋪流覽人數會清空，店鋪再次開啟，流覽人數則重新開始計算（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：_________________________________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:28px;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">72.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>新建動態內容包含：建案的銷售狀況，可售戶數格局，建案活動，廣告等；每條動態僅可修改2次（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：__________________________________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:28px;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">73.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>出租商用廣告及車位廣告可備註民宿、旅館業、日租、周租、按天計算的文字描述（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：_____________________________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:28px;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">74.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>會員在6個月前使用1200元購買出租商用新手套餐，但未曾使用刊登廣告，現套餐過期系統會自動退還1200元（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：_____________________________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:28px;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">75.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>手機APP均無法填寫頂讓廣告資訊，但蘋果手機可以開啟頂讓廣告，安卓手機可以開啟已成交的頂讓廣告資訊物件（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：_____________________________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:28px;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">76.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>新刊登法案待處理物件會員已上傳房屋藤本，客服查看無法匹配，聯絡會員一天無人接聽則：做好記錄，跟蹤至第二天；第二天仍然無人接聽則做好記錄審核通過（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：_____________________________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:28px;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">77.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>出售多筆套餐到期前七天系統會下發手機簡訊通知，套餐內筆數刊登的物件到期前系統不會下發站內簡訊通知（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：_____________________________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:28px;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">78.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>會員刊登住家出租房屋廣告需轉為社會住宅廣告，可直接來電591客服幫其修改為社會住宅廣告（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：_____________________________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:28px;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">79.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>手機APP流覽人數統計規則是：每兩個小時統計一次cookie，不同設備為不同cookic，管理員流覽不計入（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：_____________________________</span></p><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left:28px;text-align:left;text-indent:4px"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:28px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">80.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>新刊登資訊類撥出流程包含但不僅限於以下三種：（）</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>第一種：聯絡到並配合：修改為正確資訊，審核通過；</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">b)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>第二種：聯絡到不配合：發不配合簡訊並做記錄，第二次聯絡仍不配合，發簡訊+信箱關閉廣告；</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">c)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>第三種：無人接聽：發無人接聽簡訊並做記錄，第二次聯絡仍無人接聽，發簡訊+信箱關閉廣告</span></p><p class="MsoListParagraph" style="margin-top:5px;margin-right: 0;margin-bottom:5px;margin-left:56px;text-align:left"><span style="font-size: 15px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">d)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：_____________________________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">81.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>會員來電表示自己有開通店鋪，但刊登中的3筆物件都沒有辦法推薦到店鋪，是因為網站出現了bug導致的，接聽客服需將問題回饋至專員提交工程師進行修復，待修復好再回電告知會員（）</span></p><p style="margin-left:56px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：__________________________________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">82.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>新刊登廣告會員備註專營關鍵字描述，網站對於備註專營的審核判斷規則是：專營不同房屋特區/街道或專營不同房屋用途/形態，通過，具體備註多間資訊則按違規次數處理或編輯（）</span></p><p style="margin-left:56px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：__________________________________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">83.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>新建封面圖必須為外觀圖，封面圖兩邊不可以留白，圖片上不可備註聯絡電話但可在建案特色備註說明公司聯絡電話或個人行動電話（）</span></p><p style="margin-left:56px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：__________________________________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">84.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>社會住宅租金填寫規則是：純數位,且最少3位元，最多8位；租金顯示兩個空白欄，左邊填寫的金額代表：第三類租戶或第二類租戶租金金額；右邊填寫的金額代表：一般戶租金金額（）</span></p><p style="margin-left:56px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：__________________________________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">85.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>仲介店鋪一周活躍度計算方法是：1-5次/周，偶爾來一次0次，未曾來過≥5次，每天都來PS.一天內多次登入，只計一次（）</span></p><p style="margin-left:56px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：_____________________________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">86.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>會員很生氣的來電投訴對網站新刊登規則不滿，客服的基本處理流程是：1.告知網站規則；2.明確會員的訴求；3.記錄會員的建議提交專員（）</span></p><p style="margin-left:56px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：__________________________________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">87.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>兩個手機同時APP登入會員帳號，兩隻手機都可以收到問答簡訊，兩隻手機都可以操作回復（）</span></p><p style="margin-left:56px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：___________________________________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">88.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>會員購買了出租商用套餐，並使用欄位開啟了一筆物件，第二天已將物件下架，第三天會員使用安卓或蘋果的手機APP重新使用這個欄位將廣告開啟（）</span></p><p style="margin-left:56px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：__________________________________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">89.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>子帳號添加規則：1、子帳號可以無限制添加；2、子帳號為郵箱作為登入帳號，郵箱可在其他註冊的帳號中使用，且中途可修改；3、行動電話為聯絡電話，在其他帳號有註冊亦可添加成功（）</span></p><p style="margin-left:56px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：__________________________________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">90.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>新刊登修改重複更換撥出的處理流程是：同一筆廣告，第一次更換發違規站內簡訊還原，第二次更換則放待處理確認，第三次更換，查看上次已聯絡同意還原，第三次更換則直接按違規還原（）</span></p><p style="margin-left:56px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：__________________________________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">91.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>註冊會員資料時，姓名可以填寫中文或英文名字及數位（）</span></p><p style="margin-left:56px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：__________________________________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">92.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>行動版精選推薦曝光排序規則為：在列表頁頂端，共六個精選推薦展示欄位，有精選標籤，依照更新排序時間，輪播展示，每15分鐘更換一批物件象（）</span></p><p style="margin-left:56px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：__________________________________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">93.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>會員討論區頭像及昵稱可以至會員中心帳號管理-討論區-討論區資料自行設定並隨時修改（）</span></p><p style="margin-left:56px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：__________________________________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-left:28px;text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">94.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp; </span>會員來電表示使用國泰信用卡儲值未入帳，客服操作步驟為：第一步：接聽客服至【客服相關】-【儲值管理】-【國泰信用卡】-【錯誤儲值清單】；第二步：使用會員ID選擇【會員編號】進行查詢；第三步：接聽客服查看訂單狀態是未儲值，可直接跟會員說明即可（）</span></p><p class="MsoListParagraph" style="margin-left:84px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>i.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：__________________________________</span></p><p class="MsoListParagraph" style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">95.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>會員使用600元方案開啟出租住家廣告，第二天使用600元操作續刊，刊登至第25天時房屋已租並成交下架，續刊未使用到點數系統會自動退還，無需來電說明申請退還（）</span></p><p style="margin-left:56px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：__________________________________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">96.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>出租住宅套餐方案廣告可每天手動操作更新一次，若會員想要增加更新次數可額外使用150元/15天購買定時間更新排序功能，多增加一次更新時間（）</span></p><p style="margin-left:56px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：__________________________________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">97.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>出售套餐到期前七天系統會下發手機簡訊通知，而套餐筆數刊登中的物件到期前七天系統不會再下發站內簡訊通知（）</span></p><p style="margin-left:56px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：__________________________________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">98.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>若會員同一天修改多次，後臺出現多條未審核，客服操作審核時應優先審最新一個時間的資訊，這樣操作的目的是為了確保審核到的是最新修改資訊（）</span></p><p style="margin-left:56px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：_____________________________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">99.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>新刊登有法案待處理的處理流程是：第一步：從【待處理】列表，把每一頁有上傳權狀書的物件，移至【已上傳】列表；第二步：針對已上傳權狀書的進行核實及撥出（處理到當天6點）；第三步：下午五點時，把前一天6點前未上傳權狀的物件一鍵審核通過（）</span></p><p style="margin-left:56px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：__________________________________</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left:28px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">100.<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>新建案實景圖：新成屋必須5張以上、預售屋必須3張以上；周邊環境圖：機能、交通、學區、綠地等必須7張照片以上；樣品屋/實品屋：各空間照片必須7張以上（）</span></p><p style="margin-left:56px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">a)<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>錯誤原因是：_____________________________</span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li></ul> <h3 class="more-answer">我要補充</h3> <div style=""><div id="editor_31959214706630988" class="edui-default" style=""><div id="edui1" class="edui-editor  edui-default" style="width: 100%; z-index: 999;"><div id="edui1_toolbarbox" class="edui-editor-toolbarbox edui-default"><div id="edui1_toolbarboxouter" class="edui-editor-toolbarboxouter edui-default"><div class="edui-editor-toolbarboxinner edui-default"><div id="edui2" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui2&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui3" class="edui-box edui-button edui-for-fullscreen edui-default"><div id="edui3_state" onmousedown="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui3_body" unselectable="on" title="全屏" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui3&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui3&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui4" class="edui-box edui-button edui-for-source edui-default"><div id="edui4_state" onmousedown="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui4_body" unselectable="on" title="源代码" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui4&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui4&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui5" class="edui-box edui-button edui-for-undo edui-default"><div id="edui5_state" onmousedown="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui5_body" unselectable="on" title="撤销" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui5&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui5&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui6" class="edui-box edui-button edui-for-redo edui-default"><div id="edui6_state" onmousedown="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui6_body" unselectable="on" title="重做" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui6&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui6&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui7" class="edui-box edui-button edui-for-simpleupload edui-default"><div id="edui7_state" onmousedown="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui7_body" unselectable="on" title="单图上传" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui7&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui7&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"><iframe style="display: block; width: 20px; height: 20px; overflow: hidden; border: 0px; margin: 0px; padding: 0px; position: absolute; top: 0px; left: 0px; opacity: 0; cursor: pointer;"></iframe></div></div></div></div></div><div id="edui8" class="edui-box edui-button edui-for-imageleft edui-default"><div id="edui8_state" onmousedown="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui8_body" unselectable="on" title="左浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui8&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui8&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui9" class="edui-box edui-button edui-for-imageright edui-default"><div id="edui9_state" onmousedown="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui9_body" unselectable="on" title="右浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui9&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui9&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui10" class="edui-box edui-button edui-for-imagecenter edui-default"><div id="edui10_state" onmousedown="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui10_body" unselectable="on" title="居中" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui10&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui10&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui11" class="edui-box edui-separator  edui-default"></div><div id="edui12" class="edui-box edui-button edui-for-date edui-default"><div id="edui12_state" onmousedown="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui12_body" unselectable="on" title="日期" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui12&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui12&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui13" class="edui-box edui-button edui-for-time edui-default"><div id="edui13_state" onmousedown="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui13_body" unselectable="on" title="时间" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui13&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui13&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui14" class="edui-box edui-button edui-for-horizontal edui-default"><div id="edui14_state" onmousedown="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui14_body" unselectable="on" title="分隔线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui14&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui14&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui15" class="edui-box edui-separator  edui-default"></div><div id="edui16" class="edui-box edui-splitbutton edui-for-inserttable edui-default"><div title="插入表格" id="edui16_state" onmousedown="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui16_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onArrowClick();"></div></div></div></div><div id="edui19" class="edui-box edui-button edui-for-deletetable edui-default"><div id="edui19_state" onmousedown="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui19_body" unselectable="on" title="删除表格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui19&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui19&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui20" class="edui-box edui-button edui-for-insertrow edui-default"><div id="edui20_state" onmousedown="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui20_body" unselectable="on" title="前插入行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui20&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui20&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui21" class="edui-box edui-button edui-for-insertcol edui-default"><div id="edui21_state" onmousedown="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui21_body" unselectable="on" title="前插入列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui21&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui21&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui22" class="edui-box edui-button edui-for-mergeright edui-default"><div id="edui22_state" onmousedown="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui22_body" unselectable="on" title="右合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui22&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui22&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui23" class="edui-box edui-button edui-for-mergedown edui-default"><div id="edui23_state" onmousedown="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui23_body" unselectable="on" title="下合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui23&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui23&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui24" class="edui-box edui-button edui-for-deleterow edui-default"><div id="edui24_state" onmousedown="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui24_body" unselectable="on" title="删除行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui24&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui24&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui25" class="edui-box edui-button edui-for-deletecol edui-default"><div id="edui25_state" onmousedown="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui25_body" unselectable="on" title="删除列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui25&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui25&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui26" class="edui-box edui-button edui-for-splittorows edui-default"><div id="edui26_state" onmousedown="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui26_body" unselectable="on" title="拆分成行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui26&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui26&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui27" class="edui-box edui-button edui-for-splittocols edui-default"><div id="edui27_state" onmousedown="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui27_body" unselectable="on" title="拆分成列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui27&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui27&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui28" class="edui-box edui-button edui-for-splittocells edui-default"><div id="edui28_state" onmousedown="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui28_body" unselectable="on" title="完全拆分单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui28&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui28&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui29" class="edui-box edui-button edui-for-mergecells edui-default"><div id="edui29_state" onmousedown="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui29_body" unselectable="on" title="合并多个单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui29&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui29&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui36" class="edui-box edui-button edui-for-edittable edui-default"><div id="edui36_state" onmousedown="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui36_body" unselectable="on" title="表格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui36&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui36&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui41" class="edui-box edui-button edui-for-edittd edui-default"><div id="edui41_state" onmousedown="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui41_body" unselectable="on" title="单元格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui41&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui41&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui42" class="edui-box edui-separator  edui-default"></div><div id="edui43" class="edui-box edui-menubutton edui-for-lineheight edui-default"><div title="行间距" id="edui43_state" onmousedown="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui43_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onArrowClick();"></div></div></div></div><div id="edui52" class="edui-box edui-menubutton edui-for-rowspacingtop edui-default"><div title="段前距" id="edui52_state" onmousedown="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui52_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onArrowClick();"></div></div></div></div><div id="edui59" class="edui-box edui-menubutton edui-for-rowspacingbottom edui-default"><div title="段后距" id="edui59_state" onmousedown="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui59_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onArrowClick();"></div></div></div></div></div><div id="edui66" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui66&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui67" class="edui-box edui-combox edui-for-fontfamily edui-default"><div title="字体" id="edui67_state" onmousedown="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui67_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onButtonClick(event, this);">字体</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onArrowClick();"></div></div></div></div><div id="edui80" class="edui-box edui-combox edui-for-customstyle edui-default"><div title="自定义标题" id="edui80_state" onmousedown="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui80_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onButtonClick(event, this);">自定义标题</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onArrowClick();"></div></div></div></div><div id="edui86" class="edui-box edui-combox edui-for-paragraph edui-default"><div title="段落格式" id="edui86_state" onmousedown="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui86_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onButtonClick(event, this);">段落格式</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onArrowClick();"></div></div></div></div><div id="edui95" class="edui-box edui-combox edui-for-fontsize edui-default"><div title="字号" id="edui95_state" onmousedown="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui95_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onButtonClick(event, this);">字号</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onArrowClick();"></div></div></div></div><div id="edui110" class="edui-box edui-button edui-for-link edui-default"><div id="edui110_state" onmousedown="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui110_body" unselectable="on" title="超链接" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui110&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui110&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui111" class="edui-box edui-button edui-for-indent edui-default"><div id="edui111_state" onmousedown="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui111_body" unselectable="on" title="首行缩进" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui111&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui111&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui112" class="edui-box edui-button edui-for-bold edui-default"><div id="edui112_state" onmousedown="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui112_body" unselectable="on" title="加粗" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui112&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui112&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui113" class="edui-box edui-splitbutton edui-for-forecolor edui-default edui-colorbutton"><div title="字体颜色" id="edui113_state" onmousedown="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui113_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui113_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onArrowClick();"></div></div></div></div><div id="edui116" class="edui-box edui-splitbutton edui-for-backcolor edui-default edui-colorbutton"><div title="背景色" id="edui116_state" onmousedown="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui116_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui116_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onArrowClick();"></div></div></div></div><div id="edui119" class="edui-box edui-button edui-for-italic edui-default"><div id="edui119_state" onmousedown="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui119_body" unselectable="on" title="斜体" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui119&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui119&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui120" class="edui-box edui-button edui-for-underline edui-default"><div id="edui120_state" onmousedown="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui120_body" unselectable="on" title="下划线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui120&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui120&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui121" class="edui-box edui-button edui-for-strikethrough edui-default"><div id="edui121_state" onmousedown="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui121_body" unselectable="on" title="删除线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui121&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui121&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui122" class="edui-box edui-button edui-for-superscript edui-default"><div id="edui122_state" onmousedown="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui122_body" unselectable="on" title="上标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui122&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui122&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui123" class="edui-box edui-button edui-for-subscript edui-default"><div id="edui123_state" onmousedown="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui123_body" unselectable="on" title="下标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui123&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui123&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui124" class="edui-box edui-button edui-for-removeformat edui-default"><div id="edui124_state" onmousedown="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui124_body" unselectable="on" title="清除格式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui124&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui124&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui125" class="edui-box edui-button edui-for-blockquote edui-default"><div id="edui125_state" onmousedown="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui125_body" unselectable="on" title="引用" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui125&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui125&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui126" class="edui-box edui-button edui-for-pasteplain edui-default"><div id="edui126_state" onmousedown="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui126_body" unselectable="on" title="纯文本粘贴模式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui126&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui126&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui127" class="edui-box edui-menubutton edui-for-insertorderedlist edui-default"><div title="有序列表" id="edui127_state" onmousedown="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui127_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onArrowClick();"></div></div></div></div><div id="edui140" class="edui-box edui-menubutton edui-for-insertunorderedlist edui-default"><div title="无序列表" id="edui140_state" onmousedown="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui140_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onArrowClick();"></div></div></div></div><div id="edui147" class="edui-box edui-button edui-for-cleardoc edui-default"><div id="edui147_state" onmousedown="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui147_body" unselectable="on" title="清空文档" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui147&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui147&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div></div></div></div><div id="edui1_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;"><div id="edui1_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui1&quot;].showWordImageDialog();">点击上传</div><div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui1&quot;].hideToolbarMsg();">x</div><div id="edui1_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div><div style="height:0;overflow:hidden;clear:both;" class="edui-default"></div></div><div id="edui1_message_holder" class="edui-editor-messageholder edui-default"></div></div><div id="edui1_iframeholder" class="edui-editor-iframeholder edui-default" style="width: 100%; height: 300px; z-index: 999; overflow: hidden;"><iframe id="ueditor_0" width="100%" height="100%" frameborder="0" src="javascript:void(function(){document.open();document.write(&quot;<!DOCTYPE html><html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}
body{margin:8px;font-family:sans-serif;font-size:13px;}p{margin:5px 0;}</style><link rel='stylesheet' type='text/css' href='/static/ueditor/themes/iframe.css'/></head><body class='view' ></body><script type='text/javascript'  id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant0'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);</script></html>&quot;);document.close();}())"></iframe></div><div id="edui1_bottombar" class="edui-editor-bottomContainer edui-default"><table class="edui-default"><tbody class="edui-default"><tr class="edui-default"><td id="edui1_elementpath" class="edui-editor-bottombar edui-default"><div class="edui-editor-breadcrumb">元素路径:</div></td><td id="edui1_wordcount" class="edui-editor-wordcount edui-default">字数统计</td><td id="edui1_scale" class="edui-editor-scale edui-default" style="display: none;"><div class="edui-editor-icon edui-default"></div></td></tr></tbody></table></div><div id="edui1_scalelayer" class="edui-default"></div></div></div> <div class="edit-btn"><button type="button" class="button">提交回答</button> <label data-v-0fe26aab="" for="commonCheckbox" class="checkbox"><input data-v-0fe26aab="" type="checkbox" id="commonCheckbox" name="commonCheckbox"> <i data-v-0fe26aab=""></i> 需要彈窗，請勾選</label></div></div></div> <div class="index-right"><div class="right-bk"><h3 class="com-title"><a href="javascript:;">我的最爱</a></h3> <div class="item-box clearfix"><span>錯誤，請您登入後再操作！</span></div></div> <div class="hotTag right-bk"><h3 class="com-title">热门标签</h3> <div class="hotTag-box item-box clearfix"><p>無數據</p></div></div> <div class="right-bk rank"><h3 class="com-title"><a href="javascript:;">冠軍排行榜</a></h3> <ul><li><div><span class="number">1</span> <img src="/static/timg.jpeg"> <span>王菊芳</span></div> <span class="score">612分</span></li><li><div><span class="number">2</span> <img src="/upload/head/10177.gif?time=1556271256"> <span>黃雪靜</span></div> <span class="score">258分</span></li><li><div><span class="number">3</span> <img src="/static/timg.jpeg"> <span>田霞</span></div> <span class="score">240分</span></li><li><div><span class="number">4</span> <img src="/static/timg.jpeg"> <span>曹超雲</span></div> <span class="score">168分</span></li><li><div><span class="number">5</span> <img src="/static/timg.jpeg"> <span>文宇</span></div> <span class="score">96分</span></li><li><div><span class="number">6</span> <img src="/upload/head/10217.png?time=1548059679"> <span>黃興興</span></div> <span class="score">78分</span></li><li><div><span class="number">7</span> <img src="/static/timg.jpeg"> <span>黃佳麗</span></div> <span class="score">62分</span></li><li><div><span class="number">8</span> <img src="/static/timg.jpeg"> <span>马泽鹏</span></div> <span class="score">46分</span></li><li><div><span class="number">9</span> <img src="/static/timg.jpeg"> <span>陳敏南</span></div> <span class="score">40分</span></li><li><div><span class="number">10</span> <img src="/static/timg.jpeg"> <span>Cs-刘婷</span></div> <span class="score">10分</span></li><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></ul></div> <div class="activeUser right-bk"><h3 class="com-title">活跃用户</h3> <div class="activeUser-box"><a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a></div></div> <img src="data:image/png;base64,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" class="to-top"></div></div> <!----> <!----> <!----> <div class="page-cnt footer"><p>T5客服知识库<span>|</span>zsk.591.com.tw</p></div></div></div><script type="text/javascript" src="/js/manifest.f192b.js"></script><script type="text/javascript" src="/js/vendor.17570.js"></script><script type="text/javascript" src="/js/app.58254.js"></script><div id="edui_fixedlayer" class="edui-default" style="position: fixed; left: 0px; top: 0px; width: 0px; height: 0px;"><div id="edui148" class="edui-popup  edui-bubble edui-default" onmousedown="return false;" style="display: none;"> <div id="edui148_body" class="edui-popup-body edui-default"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank" class="edui-default"></iframe> <div class="edui-shadow edui-default"></div> <div id="edui148_content" class="edui-popup-content edui-default">  </div> </div></div></div></body></html>