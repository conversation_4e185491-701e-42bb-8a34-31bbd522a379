<html class=" "><head><meta charset="utf-8"><title>591客服知識庫</title><link rel="stylesheet" href="/static/font/font-awesome.min.css"><link href="/css/app.e5aa4.css" rel="stylesheet"><script type="text/javascript" charset="utf-8" async="" src="/js/questionDetail.d0a6b.js"></script><style type="text/css">.que-detail-page .index-left .button{width:74px;height:32px;line-height:32px;font-size:13px;margin-right:10px}.que-detail-page .index-left .button:hover{color:#fff}.que-detail-page .detail-box{padding:20px 30px;border:1px solid #e5e5e5;border-radius:4px}.que-detail-page .detail-box h3{font-size:18px;color:#333;margin-bottom:10px}.que-detail-page .detail-box h3 span{color:#f77d24}.que-detail-page .detail-box .add-fav{cursor:pointer}.que-detail-page .d-b-menu{margin:15px 0 25px;font-size:13px;color:#999}.que-detail-page .d-b-menu b{display:inline-block;font-weight:400;margin:0 10px}.que-detail-page .d-b-menu .r{float:right;color:#f77d24}.que-detail-page .d-b-menu .r .fa{margin-right:5px;font-size:16px}.que-detail-page .d-b-cnt{margin-bottom:25px;font-size:13px;color:#666}.que-detail-page .submit-ask{display:block;margin:32px auto 0}.que-detail-page .reply-list{margin:30px 0 10px;overflow:hidden}.que-detail-page .reply-list .reply-item{padding:15px 30px 25px;margin-bottom:30px;border-radius:4px;border:1px solid #e5e5e5}.que-detail-page .reply-list .reply-item h4{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;line-height:50px;font-size:13px}.que-detail-page .reply-list .reply-item h4 img{width:40px;height:40px;border-radius:50%}.que-detail-page .reply-list .reply-item h4 .time{color:#999}.que-detail-page .reply-list .reply-item h4 .button{-ms-flex-item-align:center;align-self:center}.que-detail-page .reply-list .reply-item h4 .delete{color:#666;cursor:pointer}.que-detail-page .reply-list .reply-item .reply-detail{padding:20px 0 30px}.que-detail-page .more-answer{font-size:18px;color:#666;margin-bottom:5px}.que-detail-page .editor-content table{width:100%;border:1px solid #ddd}.que-detail-page .editor-content table td,.que-detail-page .editor-content table th{border:1px solid #ddd;padding:2px 8px;line-height:2}.que-detail-page .editor-content img{max-width:100%;vertical-align:text-bottom}.que-detail-page .edit-btn{text-align:center;margin-top:20px}.que-detail-page .edit-btn .edit-cancle{background:#aaa}</style><style type="text/css">.head-info{line-height:26px;background:#f5f5f5;font-size:13px;color:#999}.head-info a{margin-left:20px;color:#333}.head-search{margin-top:22px}.head-search img{float:left}.head-search .srh-box{float:left;margin:20px 0 0 20px}.head-search .srh-box .srh-input{float:left;width:260px;height:38px;line-height:38px;border:2px solid #37a508;border-radius:4px 0 0 4px}.head-search .srh-box a,.head-search .srh-box button{float:left;width:80px;height:38px;line-height:36px;font-size:16px;color:#fff}.head-search .srh-box .srh-btn{border-radius:0 4px 4px 0}.head-search .srh-box .ask-btn{margin-left:10px}.head-nav{float:left;margin:20px 0 0 60px;color:#fff;font-size:16px;line-height:36px}.head-nav a{display:block;height:100%;color:#333}.head-nav li{position:relative;float:left;margin-right:35px}.head-nav .active a,.head-nav li:hover a{color:#30b940}.head-nav .active:after,.head-nav li:hover:after{display:block;content:"";height:3px;width:100%;background:#30b940}.head-pic{width:100%;height:90px;margin-top:20px;background:url(/static/banner.png) top no-repeat}</style><style type="text/css">.top-breadcrumb{height:20px;margin-top:10px;margin-bottom:40px;font-size:13px;color:#666}.top-breadcrumb .t-b-icon{color:#30b940}.top-breadcrumb span{margin-left:8px}.top-breadcrumb span .fa{font-size:16px;margin-left:8px}.top-breadcrumb span:first-child{margin-left:0}.top-breadcrumb .now{color:#30b940}</style><script type="text/javascript" src="/static/ueditor/ueditor.config.js?20210319" id="configScriptTag"></script><script type="text/javascript" src="/static/ueditor/ueditor.all.js?20210319" id="editorScriptTag"></script><style type="text/css">.checkbox[data-v-0fe26aab]{word-spacing:4px;vertical-align:bottom;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.checkbox i[data-v-0fe26aab]{display:inline-block;width:14px;height:14px;border-radius:2px;border:1px solid #ccc;background:#fff;position:relative;top:1px}.checkbox input[data-v-0fe26aab]{opacity:0;width:0;height:0;margin:0}.checkbox input:checked+i[data-v-0fe26aab]{background:#30b940;border-color:#30b940}.checkbox input:checked+i[data-v-0fe26aab]:before{content:"";position:absolute;left:2px;top:1px;width:11px;height:6px;border-bottom:2px solid #fff;border-left:2px solid #fff;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}</style><style type="text/css">.popbox{position:fixed;top:50%;left:50%;z-index:1011;background:#fff;border-radius:6px;overflow:hidden}.popbox h3{font-size:16px;line-height:40px;padding:0 20px}.popbox h3 span{float:right;color:#666;font-size:13px;cursor:pointer}.popbox .pb-cnt{padding:0 20px;font-size:13px}.popbox .pb-btn-area{position:absolute;bottom:0;left:0;z-index:1;width:100%;text-align:right;padding:0 20px 20px 0}.popbox .pb-btn-area button{height:32px;line-height:32px;border:0;background:#fff;cursor:pointer;border-radius:6px;margin-left:15px;padding:0 15px}.popbox .pb-btn-area button.pb-btn-ok{background:#daf3cf;color:#279005}.popbox .pb-btn-area button.pb-btn-ok:hover{background:#279005;color:#fff}</style><style type="text/css">.shadow{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1010;background:#000}</style><style type="text/css">.footer{margin-top:70px;margin-bottom:20px;padding-top:20px;text-align:center;color:#999;border-top:1px solid #f5f5f5}.footer span{margin:0 10px}</style><script src="/static/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript" defer="defer"></script><link href="/static/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet"><script src="/static/ueditor/third-party/codemirror/codemirror.js" type="text/javascript" defer="defer"></script><link rel="stylesheet" type="text/css" href="/static/ueditor/third-party/codemirror/codemirror.css"><script src="/static/ueditor/third-party/zeroclipboard/ZeroClipboard.js" type="text/javascript" defer="defer"></script><style type="text/css">.activeUser,.favorite,.hotTag,.rank{margin-bottom:40px}</style><style type="text/css">.rank .rank-page{height:60px;line-height:60px;font-size:18px;margin-bottom:20px;text-align:center;background:#fafafa;border-radius:5px}.rank ul{padding:0 20px}.rank li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;border-bottom:1px dotted #f5f5f5;line-height:50px}.rank li .flag{position:relative;display:inline-block;width:20px;height:30px;background:#476472;vertical-align:middle;margin-right:12px;color:#fff;text-align:center;line-height:20px;font-size:13px;font-style:normal}.rank li .flag:after{content:"";position:absolute;bottom:-4px;left:-4px;border:14px solid transparent;border-bottom-color:#fff}.rank li:first-child .flag{background:#fb6757}.rank li:nth-child(2) .flag{background:#fac525}.rank li:nth-child(3) .flag{background:#00a9fb}.rank li img{width:36px;height:36px;border-radius:50%;margin-right:12px}</style><style type="text/css">.activeUser .activeUser-box a{display:inline-block;width:64px;height:64px;border-radius:50%;overflow:hidden;margin:0 6px 12px}</style><style type="text/css">.to-top{display:none;position:fixed;bottom:150px;z-index:5;width:50px;height:50px;cursor:pointer}</style></head><body style=""><div id="app" style=""><div class="que-detail-page" style=""><div class="wiki-hd"><div class="head-info"><p class="page-cnt">
      您好，欢迎来到591客服知识库
      <a href="https://www.591.com.tw/admin.php?new_url=https://zsk.591.com.tw">[请登入]</a> <!----></p></div> <div class="page-cnt head-search clearfix"><a href="/" class="router-link-active"><img src="/static/logo.png"></a> <ul class="head-nav clearfix"><li class="router-link-active active"><a href="/">591房屋交易网</a></li> <li class=""><a href="/life">客服充电站</a></li> <li class=""><a href="/notification">通知事项</a></li></ul> <div class="srh-box"><input type="text" placeholder="请输入搜索内容" class="srh-input"> <button type="button" class="button srh-btn">搜索</button> <a href="/ask/591" class="button button-orange ask-btn">提問</a></div></div> <div class="head-pic"></div></div> <div class="top-breadcrumb com-width"><span><a href="/collection/591/1302" class="">客服質檢相關<i class="fa fa-angle-double-right"></i></a></span><span><a href="/collection/591/1302/1386" class="">資料審核<i class="fa fa-angle-double-right"></i></a></span><span><strong class="now">问题详情</strong></span></div> <div class="com-width clearfix" style=""><div class="index-left" style=""><div class="detail-box"><h3><!---->中古屋影片審核規範及處理流程</h3> <p class="clearfix d-b-menu"><span class="l">
            黃雪靜&nbsp;&nbsp;&nbsp;浏览226次&nbsp;&nbsp;&nbsp;2020-06-04 17:20:55
          </span> <span class="r"><span class="add-fav"><i class="fa fa-heart"></i>加入最爱</span></span></p> <div class="d-b-cnt editor-content big-img-parent"><div><p>如題</p></div></div> <div class="d-b-button"><a href="/edit/591/4145" class="button">编辑</a> <button class="button button-orange">关闭提问</button></div></div> <div class="hr-line"></div> <ul class="reply-list big-img-parent"><li class="reply-item"><h4><div><img src="/upload/head/10177.gif?time=1556271256">&nbsp;&nbsp;&nbsp;
              <span class="user">黃雪靜</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-06-30 10:26:02</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><h3><span style="font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-size: 16px; line-height: 172%;">作業目的</span></strong>：為了讓網站視頻品質更好，給客戶良好的找屋體驗及能清楚的瞭解房屋實況</span></h3><h3><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><br></span></h3><h3><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">审核流程：系統自動審核（2021年11月9日执行）</span></strong><br><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="line-height: 172%; font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;"></span></strong></span></h3><p><span style="font-size: 16px;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></strong></span></p><p><span style="font-size: 16px;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">自動審核規則：</span></strong></span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #FF0000;">1、影片時長低於5秒</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #FF0000;">2、影片無法播放</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #FF0000;">以上兩種自動審核會審核不通過，其餘情況均會自動通過</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><h3><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="line-height: 172%; font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;"><br></span></strong></span></h3><h3><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="line-height: 172%; font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;">一、視頻審核</span></strong></span></h3><h3><span style="font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-size: 16px; line-height: 172%; font-family: 微软雅黑, Microsoft YaHei;">1、審核規範</span></strong></span></h3><p><span style="font-size: 15px; color: #444444; border: 1px none windowtext; padding: 0px; background: white none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;">1、不得發佈侵犯他人合法權利之影片（包括智慧財產權、隱私權、肖像權等其他合法權利）<br>2、不得上傳含有危害平臺及平臺用戶利益、危害社會利益之影片<br>3、不得上傳與刊登房屋資訊、圖片、文字描述無關之影片<br>4、不得上傳與實際房屋情況不符之影片<br>5、不得上傳新建案宣傳圖剪輯之影片<br>6、不得上傳拍攝無室內結構之影片，且室內介紹內容不可少於時長的1/2<br>7、不得上傳花屏、無畫面、模糊不清、搖晃嚴重之影片 <br></span></p><p><span style="font-size: 15px; color: #444444; border: 1px none windowtext; padding: 0px; background: white none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;">8、不得上傳淫秽、嚴重影響觀感的背景音樂</span></p><p><span style="font-size: 15px; color: #444444; border: 1px none windowtext; padding: 0px; background: white none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;"><br></span></p><p><span style="font-size: 15px; border: 1px none windowtext; padding: 0px; background: white none repeat scroll 0% 0%; font-family: 微软雅黑,Microsoft YaHei; color: #FF0000;">PS：客服審核時需結合影片內容+背景音樂或語音介紹來靈活判斷是否可通過</span></p><p><span style="font-size: 15px; color: #444444; border: 1px none windowtext; padding: 0px; background: white none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;"></span></p><h3><br><span style="font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-size: 16px; line-height: 172%; font-family: 微软雅黑, Microsoft YaHei;"></span></strong></span></h3><h3><span style="font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-size: 16px; line-height: 172%; font-family: 微软雅黑, Microsoft YaHei;">2、標準影片和優秀影片審核規範</span></strong></span></h3><p>PS：需滿足以下全部條件</p><p>標準影片案例參考：https://sale.591.com.tw/home/<USER>/detail/2/9589297.html</p><p>優秀影片案例參考：https://www.youtube.com/watch?v=H-Uod-Yijbw</p><table width="584" cellspacing="0" cellpadding="0"><colgroup><col style="width:72px" width="72"><col style=";width:203px" width="202"><col style=";width:131px" width="130"><col style=";width:180px" width="180"></colgroup><tbody><tr style="height:20px" class="firstRow" height="20"><td colspan="2" style="border-right: 1px solid rgb(183, 183, 183);" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;影片标准&quot;}" width="275" valign="middle" height="20" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">影片標準</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;标准&quot;}" width="131" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">標準影片</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;优秀&quot;}" width="180" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">優秀影片</span></td></tr><tr style="height:20px" height="20"><td colspan="2" style="border-right: 1px solid rgb(183, 183, 183);" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;拍摄形式&quot;}" width="275" valign="middle" height="20" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">拍攝形式</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;一镜到底&quot;}" width="131" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">一鏡到底</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;真人带看&quot;}" width="180" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px; color: #FF0000;">真人帶看</span></td></tr><tr style="height:20px" height="20"><td colspan="2" style="border-right: 1px solid rgb(183, 183, 183);" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;影片时长&quot;}" width="275" valign="middle" height="20" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">影片時長</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;无限制&quot;}" width="131" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">無限制</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;无限制&quot;}" width="180" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">無限制</span></td></tr><tr style="height:20px" height="20"><td colspan="2" style="border-right: 1px solid rgb(183, 183, 183);" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;屏幕方向&quot;}" width="275" valign="middle" height="20" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">螢幕方向</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;横屏&quot;}" width="131" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">橫屏</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;横屏&quot;}" width="180" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">橫屏</span></td></tr><tr style="height:20px" height="20"><td colspan="2" style="border-right: 1px solid rgb(183, 183, 183);" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;拍摄时段&quot;}" width="275" valign="middle" height="20" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">拍攝時段</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;白天拍摄&quot;}" width="131" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">白天拍攝</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;白天拍摄&quot;}" width="180" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">白天拍攝</span></td></tr><tr style="height:20px" height="20"><td colspan="2" style="border-right: 1px solid rgb(183, 183, 183);" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;室内采光&quot;}" width="275" valign="middle" height="20" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">室內採光</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;开灯开窗帘&quot;}" width="131" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">開燈開窗簾</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;开灯开窗帘&quot;}" width="180" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">開燈開窗簾</span></td></tr><tr style="height:20px" height="20"><td colspan="2" style="border-right: 1px solid rgb(183, 183, 183);" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;拍摄动线&quot;}" width="275" valign="middle" height="20" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">拍攝動線</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;从房屋门口开始&quot;}" width="131" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">從房屋門口開始</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;从房屋门口开始&quot;}" width="180" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">從房屋門口開始</span></td></tr><tr style="height:35px" height="35"><td colspan="2" style="border-right: 1px solid rgb(183, 183, 183);" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;走动速度&quot;}" width="275" valign="middle" height="35" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">走動速度</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;均速前进后退，不会出现忽快情况&quot;}" width="131" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">均速前進後退，不會出現忽快情況</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;均速前进后退，不会出现忽快情况&quot;}" width="180" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">均速前進後退，不會出現忽快情況</span></td></tr><tr style="height:35px" height="35"><td colspan="2" style="border-right: 1px solid rgb(183, 183, 183);" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;画面稳定性&quot;}" width="275" valign="middle" height="35" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">畫面穩定性</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;没有出现剧烈抖动、卡顿、黑屏画面&quot;}" width="131" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">沒有出現劇烈抖動、卡頓、黑屏畫面</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;没有出现剧烈抖动、卡顿、黑屏画面&quot;}" width="180" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">沒有出現劇烈抖動、卡頓、黑屏畫面</span></td></tr><tr style="height:20px" height="20"><td rowspan="3" style="border-bottom: 1px solid rgb(183, 183, 183); border-top: medium none;" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;内容完整性&quot;}" width="72" valign="middle" height="60" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">內容完整性</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;周边配套说明&quot;}" width="203" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">周邊配套說明</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;X&quot;}" width="131" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">X</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;√&quot;}" width="180" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">√</span></td></tr><tr style="height:20px" height="20"><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;社区信息说明&quot;}" width="203" valign="middle" height="20" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">社區資訊說明</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;X&quot;}" width="131" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">X</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;√&quot;}" width="180" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">√</span></td></tr><tr style="height:20px" height="20"><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;房屋内部介绍&quot;}" width="203" valign="middle" height="20" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">房屋內部介紹</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;匹配房屋格局信息&quot;}" width="131" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">匹配房屋格局資訊</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;匹配房屋格局信息&quot;}" width="180" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">匹配房屋格局資訊</span></td></tr><tr style="height:20px" height="20"><td rowspan="3" style="border-bottom: 1px solid rgb(183, 183, 183); border-top: medium none;" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;后期制作&quot;}" width="72" valign="middle" height="60" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">後期製作</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;配字幕&quot;}" width="203" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">配字幕</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;X&quot;}" width="131" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">X</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;√&quot;}" width="180" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">√</span></td></tr><tr style="height:20px" height="20"><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;配房屋简介&quot;}" width="203" valign="middle" height="20" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">配房屋簡介</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;X&quot;}" width="131" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">X</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;√&quot;}" width="180" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">√</span></td></tr><tr style="height:20px" height="20"><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;配格局图&quot;}" width="203" valign="middle" height="20" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">配格局圖</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;X&quot;}" width="131" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">X</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;√&quot;}" width="180" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">√</span></td></tr><tr style="height:20px" height="20"><td rowspan="2" style="border-bottom: 1px solid rgb(183, 183, 183); border-top: medium none;" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;其他要求&quot;}" width="72" valign="middle" height="40" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">其他要求</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;上传图片合成影片&quot;}" width="203" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">上傳圖片合成影片</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;不允许&quot;}" width="131" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">不允許</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;不允许&quot;}" width="180" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">不允許</span></td></tr><tr style="height:20px" height="20"><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;水印标示大于画面1/5&quot;}" width="203" valign="middle" height="20" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">浮水印標示大於畫面1/5</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;不允许&quot;}" width="131" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">不允許</span></td><td style="" data-sheets-value="{&quot;1&quot;:2,&quot;2&quot;:&quot;不允许&quot;}" width="180" valign="middle" align="center"><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">不允許</span></td></tr></tbody></table><p><span style="font-size: 15px; color: #444444; border: 1px none windowtext; padding: 0px; background: white none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;"></span><br></p><h3><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="line-height: 172%; font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;">二、操作上傳、審核流程</span></strong></span></h3><h3><span style="line-height: 172%; font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;">1、上傳流程</span></h3><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑,&quot;Microsoft YaHei&quot;; font-size: 14px;">1)<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal;">&nbsp;</span>至廣告修改畫面，點擊房屋影片，可選<label for="videoSource1"></label>【<span style="font-family: 微软雅黑,&quot;Microsoft YaHei&quot;; font-size: 14px;"><label for="videoSource1">從本機中選擇</label></span>】和【選擇填寫Youtube連結】進行上傳</span></p><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑,&quot;Microsoft YaHei&quot;; font-size: 14px;"><img src="/upload/question/20210727/1627383344415702.png" alt="图片.png"><br></span></p><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑,&quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑,&quot;Microsoft YaHei&quot;; font-size: 14px;"></span></p><h3><span style="line-height: 172%; font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;">2、審核流程</span></h3><p><span style="font-family: 微软雅黑,&quot;Microsoft YaHei&quot;; font-size: 14px;">1)<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal;"> </span>位置：至後台點擊新版後台----客服---中古屋影片審核</span></p><p><img src="/upload/question/20210727/1627383479241465.png" alt="图片.png"></p><p><br></p><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑,&quot;Microsoft YaHei&quot;; font-size: 14px;"></span><img src="/upload/question/20210902/1630567303921251.png" alt="图片.png" style="width: 817px; height: 348px;" width="817" height="348"></p><p class="MsoListParagraph" style="margin-left:0"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #FF0000;">PS：在已通過列表內，審核人為0表示此影片已被會員刪除；</span></strong></p><p class="MsoListParagraph" style="margin-left:0"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #FF0000;">在未通過列表內：審核人為10001表示此影片無法播放，系統自動發簡訊刪除</span></strong></p><p class="MsoListParagraph" style="margin-left:0"><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;"></span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px"><br></span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">2）操作 ：</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">a.審核通過：待審核列表&gt;操作：觀看<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&gt;</span><span style="font-family: 微软雅黑,&quot;Microsoft YaHei&quot;; font-size: 14px; color: #FF0000;">標準/優秀（或不選）</span><span style="font-family: 微软雅黑,&quot;Microsoft YaHei&quot;; font-size: 14px;">&gt;</span>通過 &nbsp; <br></span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"> <span style="font-family: 微软雅黑,&quot;Microsoft YaHei&quot;; font-size: 14px; color: #7F7F7F;">注：1、一定先審核沒有問題才點通過；2、觀看後符合標準和優秀的規範需點擊對應標簽，若未滿足則不用點擊</span></span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><img src="/upload/question/20210902/1630567570616221.png" alt="图片.png" style="width: 823px; height: 197px;" width="823" height="197"><br></span></p><p><span style="font-family: 微软雅黑,&quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">b.審核不通過：待審核列表&gt;操作：觀看<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&gt;</span>不通過&nbsp;&nbsp;&nbsp; <span style="font-family: 微软雅黑,&quot;Microsoft YaHei&quot;; font-size: 14px; color: #7F7F7F;">注：審核不過需選擇原因，若1條影片有多處違規可複選</span><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><img src="/upload/question/20210727/1627384134708289.png" alt="图片.png" style="width: 820px; height: 556px;" width="820" height="556"></span></p><p class="MsoListParagraph" style="margin-left:0"><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;"><br></span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">c.審核不通過觸法的簡訊說明：</span></p><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span></p><p><span style="font-size:15px;font-family:'微软雅黑',sans-serif">【591】很抱歉，因您SXXXXXXX物件，影片內容不符合網站規則，已刪除影片，詳見：</span><a href="http://591.com.tw/fYOWM"><span style="font-size:15px;font-family:'微软雅黑',sans-serif">http://591.com.tw/fYOWM</span></a></p><p><span style="font-size:15px;font-family:'微软雅黑',sans-serif">&nbsp;</span><span style="font-size:15px;font-family:'微软雅黑',sans-serif;color:gray">----對應：1和2， &nbsp;&nbsp;PS：一则影片违规多条内容，则触发送该简讯</span></p><p><span style="font-size:15px;font-family:'微软雅黑',sans-serif">【591】很抱歉，因您SXXXXXXX物件，影片內容與刊登資訊無關，已刪除影片，詳見：</span><a href="http://591.com.tw/fYOWM"><span style="font-size:15px;font-family:'微软雅黑',sans-serif">http://591.com.tw/fYOWM</span></a></p><p><span style="font-size:15px;font-family:'微软雅黑',sans-serif;color:gray">----對應：3</span></p><p><span style="font-size: 15px; font-family: &quot;微软雅黑&quot;,sans-serif; color: #000000;">【591】很抱歉，因您SXXXXXXX物件，影片內容與實際房屋資訊不符，已刪除影片，詳見：<a href="http://591.com.tw/fYOWM">http://591.com.tw/fYOWM</a><br></span></p><p><span style="font-size:15px;font-family:'微软雅黑',sans-serif;color:gray">----對應：4</span></p><p>【591】因您SXXXXXXX物件，影片內容為建案宣傳圖，不符合刊登規則，已刪除影片，詳見：<a href="http://591.com.tw/fYOWM" _src="http://591.com.tw/fYOWM">http://591.com.tw/fYOWM</a><br></p><p><span style="font-size:15px;font-family:'微软雅黑',sans-serif;color:gray">----對應：5</span></p><p><span style="font-size: 15px; font-family: 微软雅黑, sans-serif; color: gray;"></span></p><p><span style="font-size:15px;font-family:'微软雅黑',sans-serif">【591】因您SXXXXXXX物件，影片內容缺少室內結構，不符合刊登規則，已刪除影片，詳見：</span><a href="http://591.com.tw/fYOWM"><span style="font-size:15px;font-family:'微软雅黑',sans-serif">http://591.com.tw/fYOWM</span></a></p><p><span style="font-size:15px;font-family:'微软雅黑',sans-serif;color:gray">----對應：6</span></p><p><span style="font-size:15px;font-family:'微软雅黑',sans-serif">【591】很抱歉，因您SXXXXXXX物件，影片內容畫質不清晰，已刪除影片，詳見：</span><a href="http://591.com.tw/fYOWM"><span style="font-size:15px;font-family:'微软雅黑',sans-serif">http://591.com.tw/fYOWM</span></a></p><p><span style="font-size:15px;font-family:'微软雅黑',sans-serif;color:gray">----對應：7</span></p><p><span style="font-size:15px;font-family:'微软雅黑',sans-serif">【591】很抱歉，因您SXXXXXXX物件，影片內容無法正常播放，已影片刪除，請檢查影片連結是否正確或將瀏覽許可權設為【公開】後重新上傳</span></p><p><span style="font-size:15px;font-family:'微软雅黑',sans-serif;color:gray">----對應：8</span></p><p><span style="font-size:15px;font-family:'微软雅黑',sans-serif;color:gray"><br></span><br></p><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span><br></p><p class="MsoListParagraph" style="margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span><br><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span></p><p><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">記錄表格（舊資料）</span></p><p><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">Ø<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">&nbsp;&nbsp; </span>入口：<a href="https://docs.google.com/spreadsheets/d/1HIuqTpNz9kqDFcu8ZMIXUSosJ2wHF0qquw_XQQYG81Y/edit#gid=1937532892" _src="https://docs.google.com/spreadsheets/d/1HIuqTpNz9kqDFcu8ZMIXUSosJ2wHF0qquw_XQQYG81Y/edit#gid=1937532892">https://docs.google.com/spreadsheets/d/1HIuqTpNz9kqDFcu8ZMIXUSosJ2wHF0qquw_XQQYG81Y/edit#gid=1937532892</a> </span></p><p><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;"><img src="/upload/question/20200630/1593483959924712.png" alt="image.png"></span></p><p><br></p><p><br></p><p><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">&nbsp;</span></p><p><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;"><br></span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li></ul> <h3 class="more-answer">我要補充</h3> <div style=""><div id="editor_8014361959223237" class="edui-default" style=""><div id="edui1" class="edui-editor  edui-default" style="width: 100%; z-index: 999;"><div id="edui1_toolbarbox" class="edui-editor-toolbarbox edui-default"><div id="edui1_toolbarboxouter" class="edui-editor-toolbarboxouter edui-default"><div class="edui-editor-toolbarboxinner edui-default"><div id="edui2" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui2&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui3" class="edui-box edui-button edui-for-fullscreen edui-default"><div id="edui3_state" onmousedown="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui3_body" unselectable="on" title="全屏" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui3&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui3&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui4" class="edui-box edui-button edui-for-source edui-default"><div id="edui4_state" onmousedown="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui4_body" unselectable="on" title="源代码" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui4&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui4&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui5" class="edui-box edui-button edui-for-undo edui-default"><div id="edui5_state" onmousedown="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui5_body" unselectable="on" title="撤销" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui5&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui5&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui6" class="edui-box edui-button edui-for-redo edui-default"><div id="edui6_state" onmousedown="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui6_body" unselectable="on" title="重做" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui6&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui6&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui7" class="edui-box edui-button edui-for-simpleupload edui-default"><div id="edui7_state" onmousedown="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui7_body" unselectable="on" title="单图上传" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui7&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui7&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"><iframe style="display: block; width: 20px; height: 20px; overflow: hidden; border: 0px; margin: 0px; padding: 0px; position: absolute; top: 0px; left: 0px; opacity: 0; cursor: pointer;"></iframe></div></div></div></div></div><div id="edui8" class="edui-box edui-button edui-for-imageleft edui-default"><div id="edui8_state" onmousedown="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui8_body" unselectable="on" title="左浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui8&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui8&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui9" class="edui-box edui-button edui-for-imageright edui-default"><div id="edui9_state" onmousedown="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui9_body" unselectable="on" title="右浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui9&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui9&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui10" class="edui-box edui-button edui-for-imagecenter edui-default"><div id="edui10_state" onmousedown="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui10_body" unselectable="on" title="居中" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui10&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui10&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui11" class="edui-box edui-separator  edui-default"></div><div id="edui12" class="edui-box edui-button edui-for-date edui-default"><div id="edui12_state" onmousedown="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui12_body" unselectable="on" title="日期" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui12&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui12&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui13" class="edui-box edui-button edui-for-time edui-default"><div id="edui13_state" onmousedown="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui13_body" unselectable="on" title="时间" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui13&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui13&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui14" class="edui-box edui-button edui-for-horizontal edui-default"><div id="edui14_state" onmousedown="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui14_body" unselectable="on" title="分隔线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui14&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui14&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui15" class="edui-box edui-separator  edui-default"></div><div id="edui16" class="edui-box edui-splitbutton edui-for-inserttable edui-default"><div title="插入表格" id="edui16_state" onmousedown="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui16_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onArrowClick();"></div></div></div></div><div id="edui19" class="edui-box edui-button edui-for-deletetable edui-default"><div id="edui19_state" onmousedown="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui19_body" unselectable="on" title="删除表格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui19&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui19&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui20" class="edui-box edui-button edui-for-insertrow edui-default"><div id="edui20_state" onmousedown="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui20_body" unselectable="on" title="前插入行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui20&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui20&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui21" class="edui-box edui-button edui-for-insertcol edui-default"><div id="edui21_state" onmousedown="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui21_body" unselectable="on" title="前插入列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui21&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui21&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui22" class="edui-box edui-button edui-for-mergeright edui-default"><div id="edui22_state" onmousedown="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui22_body" unselectable="on" title="右合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui22&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui22&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui23" class="edui-box edui-button edui-for-mergedown edui-default"><div id="edui23_state" onmousedown="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui23_body" unselectable="on" title="下合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui23&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui23&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui24" class="edui-box edui-button edui-for-deleterow edui-default"><div id="edui24_state" onmousedown="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui24_body" unselectable="on" title="删除行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui24&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui24&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui25" class="edui-box edui-button edui-for-deletecol edui-default"><div id="edui25_state" onmousedown="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui25_body" unselectable="on" title="删除列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui25&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui25&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui26" class="edui-box edui-button edui-for-splittorows edui-default"><div id="edui26_state" onmousedown="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui26_body" unselectable="on" title="拆分成行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui26&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui26&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui27" class="edui-box edui-button edui-for-splittocols edui-default"><div id="edui27_state" onmousedown="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui27_body" unselectable="on" title="拆分成列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui27&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui27&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui28" class="edui-box edui-button edui-for-splittocells edui-default"><div id="edui28_state" onmousedown="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui28_body" unselectable="on" title="完全拆分单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui28&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui28&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui29" class="edui-box edui-button edui-for-mergecells edui-default"><div id="edui29_state" onmousedown="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui29_body" unselectable="on" title="合并多个单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui29&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui29&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui36" class="edui-box edui-button edui-for-edittable edui-default"><div id="edui36_state" onmousedown="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui36_body" unselectable="on" title="表格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui36&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui36&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui41" class="edui-box edui-button edui-for-edittd edui-default"><div id="edui41_state" onmousedown="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui41_body" unselectable="on" title="单元格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui41&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui41&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui42" class="edui-box edui-separator  edui-default"></div><div id="edui43" class="edui-box edui-menubutton edui-for-lineheight edui-default"><div title="行间距" id="edui43_state" onmousedown="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui43_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onArrowClick();"></div></div></div></div><div id="edui52" class="edui-box edui-menubutton edui-for-rowspacingtop edui-default"><div title="段前距" id="edui52_state" onmousedown="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui52_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onArrowClick();"></div></div></div></div><div id="edui59" class="edui-box edui-menubutton edui-for-rowspacingbottom edui-default"><div title="段后距" id="edui59_state" onmousedown="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui59_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onArrowClick();"></div></div></div></div></div><div id="edui66" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui66&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui67" class="edui-box edui-combox edui-for-fontfamily edui-default"><div title="字体" id="edui67_state" onmousedown="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui67_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onButtonClick(event, this);">字体</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onArrowClick();"></div></div></div></div><div id="edui80" class="edui-box edui-combox edui-for-customstyle edui-default"><div title="自定义标题" id="edui80_state" onmousedown="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui80_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onButtonClick(event, this);">自定义标题</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onArrowClick();"></div></div></div></div><div id="edui86" class="edui-box edui-combox edui-for-paragraph edui-default"><div title="段落格式" id="edui86_state" onmousedown="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui86_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onButtonClick(event, this);">段落格式</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onArrowClick();"></div></div></div></div><div id="edui95" class="edui-box edui-combox edui-for-fontsize edui-default"><div title="字号" id="edui95_state" onmousedown="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui95_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onButtonClick(event, this);">字号</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onArrowClick();"></div></div></div></div><div id="edui110" class="edui-box edui-button edui-for-link edui-default"><div id="edui110_state" onmousedown="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui110_body" unselectable="on" title="超链接" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui110&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui110&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui111" class="edui-box edui-button edui-for-indent edui-default"><div id="edui111_state" onmousedown="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui111_body" unselectable="on" title="首行缩进" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui111&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui111&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui112" class="edui-box edui-button edui-for-bold edui-default"><div id="edui112_state" onmousedown="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui112_body" unselectable="on" title="加粗" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui112&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui112&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui113" class="edui-box edui-splitbutton edui-for-forecolor edui-default edui-colorbutton"><div title="字体颜色" id="edui113_state" onmousedown="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui113_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui113_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onArrowClick();"></div></div></div></div><div id="edui116" class="edui-box edui-splitbutton edui-for-backcolor edui-default edui-colorbutton"><div title="背景色" id="edui116_state" onmousedown="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui116_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui116_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onArrowClick();"></div></div></div></div><div id="edui119" class="edui-box edui-button edui-for-italic edui-default"><div id="edui119_state" onmousedown="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui119_body" unselectable="on" title="斜体" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui119&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui119&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui120" class="edui-box edui-button edui-for-underline edui-default"><div id="edui120_state" onmousedown="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui120_body" unselectable="on" title="下划线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui120&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui120&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui121" class="edui-box edui-button edui-for-strikethrough edui-default"><div id="edui121_state" onmousedown="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui121_body" unselectable="on" title="删除线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui121&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui121&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui122" class="edui-box edui-button edui-for-superscript edui-default"><div id="edui122_state" onmousedown="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui122_body" unselectable="on" title="上标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui122&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui122&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui123" class="edui-box edui-button edui-for-subscript edui-default"><div id="edui123_state" onmousedown="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui123_body" unselectable="on" title="下标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui123&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui123&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui124" class="edui-box edui-button edui-for-removeformat edui-default"><div id="edui124_state" onmousedown="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui124_body" unselectable="on" title="清除格式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui124&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui124&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui125" class="edui-box edui-button edui-for-blockquote edui-default"><div id="edui125_state" onmousedown="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui125_body" unselectable="on" title="引用" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui125&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui125&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui126" class="edui-box edui-button edui-for-pasteplain edui-default"><div id="edui126_state" onmousedown="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui126_body" unselectable="on" title="纯文本粘贴模式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui126&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui126&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui127" class="edui-box edui-menubutton edui-for-insertorderedlist edui-default"><div title="有序列表" id="edui127_state" onmousedown="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui127_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onArrowClick();"></div></div></div></div><div id="edui140" class="edui-box edui-menubutton edui-for-insertunorderedlist edui-default"><div title="无序列表" id="edui140_state" onmousedown="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui140_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onArrowClick();"></div></div></div></div><div id="edui147" class="edui-box edui-button edui-for-cleardoc edui-default"><div id="edui147_state" onmousedown="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui147_body" unselectable="on" title="清空文档" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui147&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui147&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div></div></div></div><div id="edui1_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;"><div id="edui1_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui1&quot;].showWordImageDialog();">点击上传</div><div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui1&quot;].hideToolbarMsg();">x</div><div id="edui1_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div><div style="height:0;overflow:hidden;clear:both;" class="edui-default"></div></div><div id="edui1_message_holder" class="edui-editor-messageholder edui-default"></div></div><div id="edui1_iframeholder" class="edui-editor-iframeholder edui-default" style="width: 100%; height: 300px; z-index: 999; overflow: hidden;"><iframe id="ueditor_0" width="100%" height="100%" frameborder="0" src="javascript:void(function(){document.open();document.write(&quot;<!DOCTYPE html><html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}
body{margin:8px;font-family:sans-serif;font-size:13px;}p{margin:5px 0;}</style><link rel='stylesheet' type='text/css' href='/static/ueditor/themes/iframe.css'/></head><body class='view' ></body><script type='text/javascript'  id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant0'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);</script></html>&quot;);document.close();}())"></iframe></div><div id="edui1_bottombar" class="edui-editor-bottomContainer edui-default"><table class="edui-default"><tbody class="edui-default"><tr class="edui-default"><td id="edui1_elementpath" class="edui-editor-bottombar edui-default"><div class="edui-editor-breadcrumb">元素路径:</div></td><td id="edui1_wordcount" class="edui-editor-wordcount edui-default">字数统计</td><td id="edui1_scale" class="edui-editor-scale edui-default" style="display: none;"><div class="edui-editor-icon edui-default"></div></td></tr></tbody></table></div><div id="edui1_scalelayer" class="edui-default"></div></div></div> <div class="edit-btn"><button type="button" class="button">提交回答</button> <label data-v-0fe26aab="" for="commonCheckbox" class="checkbox"><input data-v-0fe26aab="" type="checkbox" id="commonCheckbox" name="commonCheckbox"> <i data-v-0fe26aab=""></i> 需要彈窗，請勾選</label></div></div></div> <div class="index-right"><div class="right-bk"><h3 class="com-title"><a href="javascript:;">我的最爱</a></h3> <div class="item-box clearfix"><span>錯誤，請您登入後再操作！</span></div></div> <div class="hotTag right-bk"><h3 class="com-title">热门标签</h3> <div class="hotTag-box item-box clearfix"><p>無數據</p></div></div> <div class="right-bk rank"><h3 class="com-title"><a href="javascript:;">冠軍排行榜</a></h3> <ul><li><div><span class="number">1</span> <img src="/static/timg.jpeg"> <span>王菊芳</span></div> <span class="score">612分</span></li><li><div><span class="number">2</span> <img src="/upload/head/10177.gif?time=1556271256"> <span>黃雪靜</span></div> <span class="score">258分</span></li><li><div><span class="number">3</span> <img src="/static/timg.jpeg"> <span>田霞</span></div> <span class="score">240分</span></li><li><div><span class="number">4</span> <img src="/static/timg.jpeg"> <span>曹超雲</span></div> <span class="score">168分</span></li><li><div><span class="number">5</span> <img src="/static/timg.jpeg"> <span>文宇</span></div> <span class="score">96分</span></li><li><div><span class="number">6</span> <img src="/upload/head/10217.png?time=1548059679"> <span>黃興興</span></div> <span class="score">78分</span></li><li><div><span class="number">7</span> <img src="/static/timg.jpeg"> <span>黃佳麗</span></div> <span class="score">62分</span></li><li><div><span class="number">8</span> <img src="/static/timg.jpeg"> <span>马泽鹏</span></div> <span class="score">46分</span></li><li><div><span class="number">9</span> <img src="/static/timg.jpeg"> <span>陳敏南</span></div> <span class="score">40分</span></li><li><div><span class="number">10</span> <img src="/static/timg.jpeg"> <span>Cs-刘婷</span></div> <span class="score">10分</span></li><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></ul></div> <div class="activeUser right-bk"><h3 class="com-title">活跃用户</h3> <div class="activeUser-box"><a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a></div></div> <img src="data:image/png;base64,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" class="to-top"></div></div> <!----> <!----> <!----> <div class="page-cnt footer"><p>T5客服知识库<span>|</span>zsk.591.com.tw</p></div></div></div><script type="text/javascript" src="/js/manifest.f192b.js"></script><script type="text/javascript" src="/js/vendor.17570.js"></script><script type="text/javascript" src="/js/app.58254.js"></script><div id="edui_fixedlayer" class="edui-default" style="position: fixed; left: 0px; top: 0px; width: 0px; height: 0px;"><div id="edui148" class="edui-popup  edui-bubble edui-default" onmousedown="return false;" style="display: none;"> <div id="edui148_body" class="edui-popup-body edui-default"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank" class="edui-default"></iframe> <div class="edui-shadow edui-default"></div> <div id="edui148_content" class="edui-popup-content edui-default">  </div> </div></div></div></body></html>