<html class=" "><head><meta charset="utf-8"><title>591客服知識庫</title><link rel="stylesheet" href="/static/font/font-awesome.min.css"><link href="/css/app.e5aa4.css" rel="stylesheet"><script type="text/javascript" charset="utf-8" async="" src="/js/questionDetail.d0a6b.js"></script><style type="text/css">.que-detail-page .index-left .button{width:74px;height:32px;line-height:32px;font-size:13px;margin-right:10px}.que-detail-page .index-left .button:hover{color:#fff}.que-detail-page .detail-box{padding:20px 30px;border:1px solid #e5e5e5;border-radius:4px}.que-detail-page .detail-box h3{font-size:18px;color:#333;margin-bottom:10px}.que-detail-page .detail-box h3 span{color:#f77d24}.que-detail-page .detail-box .add-fav{cursor:pointer}.que-detail-page .d-b-menu{margin:15px 0 25px;font-size:13px;color:#999}.que-detail-page .d-b-menu b{display:inline-block;font-weight:400;margin:0 10px}.que-detail-page .d-b-menu .r{float:right;color:#f77d24}.que-detail-page .d-b-menu .r .fa{margin-right:5px;font-size:16px}.que-detail-page .d-b-cnt{margin-bottom:25px;font-size:13px;color:#666}.que-detail-page .submit-ask{display:block;margin:32px auto 0}.que-detail-page .reply-list{margin:30px 0 10px;overflow:hidden}.que-detail-page .reply-list .reply-item{padding:15px 30px 25px;margin-bottom:30px;border-radius:4px;border:1px solid #e5e5e5}.que-detail-page .reply-list .reply-item h4{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;line-height:50px;font-size:13px}.que-detail-page .reply-list .reply-item h4 img{width:40px;height:40px;border-radius:50%}.que-detail-page .reply-list .reply-item h4 .time{color:#999}.que-detail-page .reply-list .reply-item h4 .button{-ms-flex-item-align:center;align-self:center}.que-detail-page .reply-list .reply-item h4 .delete{color:#666;cursor:pointer}.que-detail-page .reply-list .reply-item .reply-detail{padding:20px 0 30px}.que-detail-page .more-answer{font-size:18px;color:#666;margin-bottom:5px}.que-detail-page .editor-content table{width:100%;border:1px solid #ddd}.que-detail-page .editor-content table td,.que-detail-page .editor-content table th{border:1px solid #ddd;padding:2px 8px;line-height:2}.que-detail-page .editor-content img{max-width:100%;vertical-align:text-bottom}.que-detail-page .edit-btn{text-align:center;margin-top:20px}.que-detail-page .edit-btn .edit-cancle{background:#aaa}</style><style type="text/css">.head-info{line-height:26px;background:#f5f5f5;font-size:13px;color:#999}.head-info a{margin-left:20px;color:#333}.head-search{margin-top:22px}.head-search img{float:left}.head-search .srh-box{float:left;margin:20px 0 0 20px}.head-search .srh-box .srh-input{float:left;width:260px;height:38px;line-height:38px;border:2px solid #37a508;border-radius:4px 0 0 4px}.head-search .srh-box a,.head-search .srh-box button{float:left;width:80px;height:38px;line-height:36px;font-size:16px;color:#fff}.head-search .srh-box .srh-btn{border-radius:0 4px 4px 0}.head-search .srh-box .ask-btn{margin-left:10px}.head-nav{float:left;margin:20px 0 0 60px;color:#fff;font-size:16px;line-height:36px}.head-nav a{display:block;height:100%;color:#333}.head-nav li{position:relative;float:left;margin-right:35px}.head-nav .active a,.head-nav li:hover a{color:#30b940}.head-nav .active:after,.head-nav li:hover:after{display:block;content:"";height:3px;width:100%;background:#30b940}.head-pic{width:100%;height:90px;margin-top:20px;background:url(/static/banner.png) top no-repeat}</style><style type="text/css">.top-breadcrumb{height:20px;margin-top:10px;margin-bottom:40px;font-size:13px;color:#666}.top-breadcrumb .t-b-icon{color:#30b940}.top-breadcrumb span{margin-left:8px}.top-breadcrumb span .fa{font-size:16px;margin-left:8px}.top-breadcrumb span:first-child{margin-left:0}.top-breadcrumb .now{color:#30b940}</style><script type="text/javascript" src="/static/ueditor/ueditor.config.js?20210319" id="configScriptTag"></script><script type="text/javascript" src="/static/ueditor/ueditor.all.js?20210319" id="editorScriptTag"></script><style type="text/css">.checkbox[data-v-0fe26aab]{word-spacing:4px;vertical-align:bottom;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.checkbox i[data-v-0fe26aab]{display:inline-block;width:14px;height:14px;border-radius:2px;border:1px solid #ccc;background:#fff;position:relative;top:1px}.checkbox input[data-v-0fe26aab]{opacity:0;width:0;height:0;margin:0}.checkbox input:checked+i[data-v-0fe26aab]{background:#30b940;border-color:#30b940}.checkbox input:checked+i[data-v-0fe26aab]:before{content:"";position:absolute;left:2px;top:1px;width:11px;height:6px;border-bottom:2px solid #fff;border-left:2px solid #fff;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}</style><style type="text/css">.popbox{position:fixed;top:50%;left:50%;z-index:1011;background:#fff;border-radius:6px;overflow:hidden}.popbox h3{font-size:16px;line-height:40px;padding:0 20px}.popbox h3 span{float:right;color:#666;font-size:13px;cursor:pointer}.popbox .pb-cnt{padding:0 20px;font-size:13px}.popbox .pb-btn-area{position:absolute;bottom:0;left:0;z-index:1;width:100%;text-align:right;padding:0 20px 20px 0}.popbox .pb-btn-area button{height:32px;line-height:32px;border:0;background:#fff;cursor:pointer;border-radius:6px;margin-left:15px;padding:0 15px}.popbox .pb-btn-area button.pb-btn-ok{background:#daf3cf;color:#279005}.popbox .pb-btn-area button.pb-btn-ok:hover{background:#279005;color:#fff}</style><style type="text/css">.shadow{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1010;background:#000}</style><style type="text/css">.footer{margin-top:70px;margin-bottom:20px;padding-top:20px;text-align:center;color:#999;border-top:1px solid #f5f5f5}.footer span{margin:0 10px}</style><script src="/static/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript" defer="defer"></script><link href="/static/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet"><script src="/static/ueditor/third-party/codemirror/codemirror.js" type="text/javascript" defer="defer"></script><link rel="stylesheet" type="text/css" href="/static/ueditor/third-party/codemirror/codemirror.css"><script src="/static/ueditor/third-party/zeroclipboard/ZeroClipboard.js" type="text/javascript" defer="defer"></script><style type="text/css">.activeUser,.favorite,.hotTag,.rank{margin-bottom:40px}</style><style type="text/css">.rank .rank-page{height:60px;line-height:60px;font-size:18px;margin-bottom:20px;text-align:center;background:#fafafa;border-radius:5px}.rank ul{padding:0 20px}.rank li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;border-bottom:1px dotted #f5f5f5;line-height:50px}.rank li .flag{position:relative;display:inline-block;width:20px;height:30px;background:#476472;vertical-align:middle;margin-right:12px;color:#fff;text-align:center;line-height:20px;font-size:13px;font-style:normal}.rank li .flag:after{content:"";position:absolute;bottom:-4px;left:-4px;border:14px solid transparent;border-bottom-color:#fff}.rank li:first-child .flag{background:#fb6757}.rank li:nth-child(2) .flag{background:#fac525}.rank li:nth-child(3) .flag{background:#00a9fb}.rank li img{width:36px;height:36px;border-radius:50%;margin-right:12px}</style><style type="text/css">.activeUser .activeUser-box a{display:inline-block;width:64px;height:64px;border-radius:50%;overflow:hidden;margin:0 6px 12px}</style><style type="text/css">.to-top{display:none;position:fixed;bottom:150px;z-index:5;width:50px;height:50px;cursor:pointer}</style></head><body style=""><div id="app" style=""><div class="que-detail-page" style=""><div class="wiki-hd"><div class="head-info"><p class="page-cnt">
      您好，欢迎来到591客服知识库
      <a href="https://www.591.com.tw/admin.php?new_url=https://zsk.591.com.tw">[请登入]</a> <!----></p></div> <div class="page-cnt head-search clearfix"><a href="/" class="router-link-active"><img src="/static/logo.png"></a> <ul class="head-nav clearfix"><li class="router-link-active active"><a href="/">591房屋交易网</a></li> <li class=""><a href="/life">客服充电站</a></li> <li class=""><a href="/notification">通知事项</a></li></ul> <div class="srh-box"><input type="text" placeholder="请输入搜索内容" class="srh-input"> <button type="button" class="button srh-btn">搜索</button> <a href="/ask/591" class="button button-orange ask-btn">提問</a></div></div> <div class="head-pic"></div></div> <div class="top-breadcrumb com-width"><span><a href="/collection/591/1305" class="">現場管理相關<i class="fa fa-angle-double-right"></i></a></span><span><a href="/collection/591/1305/1646" class="">作業指導書<i class="fa fa-angle-double-right"></i></a></span><span><strong class="now">问题详情</strong></span></div> <div class="com-width clearfix" style=""><div class="index-left" style=""><div class="detail-box"><h3><!---->團隊管理</h3> <p class="clearfix d-b-menu"><span class="l">
            田霞&nbsp;&nbsp;&nbsp;浏览66次&nbsp;&nbsp;&nbsp;2020-05-20 13:10:17
          </span> <span class="r"><span class="add-fav"><i class="fa fa-heart"></i>加入最爱</span></span></p> <div class="d-b-cnt editor-content big-img-parent"><div><p>1、人員培養<br></p><p>2、團隊文化</p><p>3、人員管理</p><p>4、質量管理</p><p>5、流程管理</p><p>6、知識管理</p><p>7、目標管理</p><p><br></p></div></div> <div class="d-b-button"><a href="/edit/591/4115" class="button">编辑</a> <button class="button button-orange">关闭提问</button></div></div> <div class="hr-line"></div> <ul class="reply-list big-img-parent"><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">田霞</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-05-20 13:48:10</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px;">一、人員培養</span></strong><br></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p><span style="font-size: 14px;"><span style="font-family: &quot;微软雅黑&quot;, sans-serif;">1.1</span><span style="font-family: PMingLiU;">星級考核</span></span></p><p style="margin-left:48px"><span style="font-family: PMingLiU; font-size: 14px;">什麼是星級考核</span></p><p><span style="font-family: PMingLiU; font-size: 14px;">星級考核是指通過培訓、考核、評定的過程，培養客服工作技能，確定星級客服和薪資等級。</span></p><p><span style="font-size: 14px;">&nbsp;</span></p><p style="margin-left:48px"><span style="font-size: 14px;"><span style="font: 14px &quot;Times New Roman&quot;;">&nbsp; </span><span style="font-family: PMingLiU; font-size: 14px;">星級考核的基本流程</span></span></p><p style="margin-left:48px"><span style="font-size: 14px;"><span style="font-family: &quot;微软雅黑&quot;, sans-serif; font-size: 14px;">1</span><span style="font-family: PMingLiU; font-size: 14px;">）</span><span style="font-family: &quot;微软雅黑&quot;, sans-serif; font-size: 14px;">&nbsp;&nbsp; </span><span style="font-family: PMingLiU; font-size: 14px;">客服主任依年度星級客服人員評定計劃和培訓能力，執行星級培養；</span></span></p><p style="margin-left:48px"><span style="font-size: 14px;"><span style="font-family: &quot;微软雅黑&quot;, sans-serif; font-size: 14px;">2</span><span style="font-family: PMingLiU; font-size: 14px;">）</span><span style="font-family: &quot;微软雅黑&quot;, sans-serif; font-size: 14px;">&nbsp;&nbsp; </span><span style="font-family: PMingLiU; font-size: 14px;">制定老帶新培養計劃並跟蹤指導至學習完成；</span></span></p><p style="margin-left:48px"><span style="font-size: 14px;"><span style="font-family: &quot;微软雅黑&quot;, sans-serif; font-size: 14px;">3</span><span style="font-family: PMingLiU; font-size: 14px;">）</span><span style="font-family: &quot;微软雅黑&quot;, sans-serif; font-size: 14px;">&nbsp;&nbsp; </span><span style="font-family: PMingLiU; font-size: 14px;">提前半個月向客服質檢提出星級考核需求；</span></span></p><p style="margin-left:48px"><span style="font-size: 14px;"><span style="font-family: &quot;微软雅黑&quot;, sans-serif; font-size: 14px;">4</span><span style="font-family: PMingLiU; font-size: 14px;">）</span><span style="font-family: &quot;微软雅黑&quot;, sans-serif; font-size: 14px;">&nbsp;&nbsp; </span><span style="font-family: PMingLiU; font-size: 14px;">考核完成當月，客服質檢根據對應星級客服的薪資標準協助職員完成薪資調整手續。</span></span></p><ul class=" list-paddingleft-2"><li><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:Symbol"></span><span style="font-size:16px;font-family:宋体"></span><span style="font-size: 16px;font-family:PMingLiU">星級客服評定資格和薪資標準</span></p></li></ul><p><br></p><p><br></p><table cellspacing="0" cellpadding="0"><tbody><tr style=";height:54px" class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" width="110" height="54"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">星級<br></span></strong></span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: initial; padding: 0px 7px; word-break: break-all;" width="304" height="54"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">星級客服評定資格</span></strong></span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: initial; padding: 0px 7px; word-break: break-all;" width="162" height="54"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">薪資標準</span></strong></span></p></td></tr><tr style=";height:22px"><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="110" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">★</span></p><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1星客服</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="304" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">試用期滿並通過轉正考核；</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" width="162" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">6200元/月</span></p></td></tr><tr style=";height:22px"><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="110" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">★★<br> &nbsp; 2星客服</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="304" height="22"><p style="margin-bottom: 0px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 通過1星客服評定滿半年及以上；</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" width="162" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">6500元/月</span></p></td></tr><tr style=";height:22px"><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="110" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">★★★<br> &nbsp; 3星客服</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="304" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">通過2星客服評定滿半年及以上；</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" width="162" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">6900元/月</span></p></td></tr><tr style=";height:22px"><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="110" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">★★★★<br> &nbsp; 4星客服</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="304" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">通過3星客服評定滿半年及以上；</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" width="162" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">7400元/月</span></p></td></tr></tbody></table><p><br><span style="font-family: SimSun;"></span></p><ul class=" list-paddingleft-2" style="list-style-type: disc;"><li><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">星級客服配置（以10人團隊為例）</span></p></li></ul><table cellspacing="0" cellpadding="0"><tbody><tr style=";height:54px" class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px;" width="110" height="54"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">星级</span></strong></span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="139" height="54"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">星级客服配置</span></strong></span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="350" height="54"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">人員需求說明</span></strong></span></p></td></tr><tr style=";height:22px"><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="110" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">★</span></p><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1星客服</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="139" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1-2名</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="350" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">負責基礎業務的進行</span></p></td></tr><tr style=";height:22px"><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="110" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">★★<br> &nbsp; 2星客服</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="139" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1-2名</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="350" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">負責基礎業務的進行</span></p></td></tr><tr style=";height:22px"><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="110" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">★★★<br> &nbsp; 3星客服</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="139" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3-4名</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="350" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">負責核心業務的進行，補充當人員休假時的技能不足</span></p></td></tr><tr style=";height:22px"><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="110" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">★★★★<br> &nbsp; 4星客服</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="139" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3-4名</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="350" height="22"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">負責核心業務的進行，補充當人員休假時的技能不足</span></p></td></tr></tbody></table><p><span style="font-family: SimSun;"></span></p><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">註明：</span></p><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">A、1-2星人數不可過多，避免因技能不足無法滿足現場工作，當發現現場人員出現這樣的情況時，需及時時間培養技能學習。</span></p><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">B、當大家都到了3星時，需考慮4星人員飽和的情況，可根據情況安排不同進度的學習</span></p><ul class=" list-paddingleft-2" style="list-style-type: disc;"><li><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">星級規劃</span></p></li></ul><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><table width="662" cellspacing="0" cellpadding="0"><tbody><tr style=";height:42px" class="firstRow"><td style="border: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="37" height="42"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">班级</span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="66" height="42"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">规划星級</span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="37" height="42"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">人數</span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="65" height="42"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">姓名</span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="76" height="42"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">入职日期</span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="39" height="42"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">现状星級</span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="85" height="42"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">上次星级评定日期</span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="94" height="42"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">二星評定日期</span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="94" height="42"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">三星評定日期</span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="67" height="42"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">四星評定日期</span></p></td></tr><tr style=";height:22px"><td rowspan="7" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="37" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">B班</span></p></td><td rowspan="4" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="66" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">四星</span></p></td><td rowspan="4" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="37" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">4人</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="65" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">田霞</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="76" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">2013/3/8</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="39" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">四</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="85" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">2017.12.1</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="94" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">/</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="94" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">/</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="67" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">/</span></p></td></tr><tr style=";height:22px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="65" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">罗妙珠</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="76" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">2014/7/30</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="39" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">四</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="85" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">2018年10月</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="94" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">/</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="94" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">/</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="67" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">/</span></p></td></tr><tr style=";height:22px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="65" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">熊江元</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="76" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">2012.10.23</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="39" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">三</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="85" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">2018.1.1</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="94" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">/</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="94" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">/</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="67" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">2019.12（小田）</span></p></td></tr><tr style=";height:22px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="65" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">林丽红</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="76" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">2018/5/14</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="39" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">二</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="85" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">2018.12</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="94" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">/</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="94" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">2019.4（小珠）</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="67" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">2019.11（小田）</span></p></td></tr><tr style=";height:22px"><td rowspan="2" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="66" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">三星</span></p></td><td rowspan="2" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="37" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">2人</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="65" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">李少鹏</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="76" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">2015.12.03</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="39" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">三</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="85" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">2018.7</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="94" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">/</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="94" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">/</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="67" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">/</span></p></td></tr><tr style=";height:22px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="65" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">陶红</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="76" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">2018/4/2</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="39" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">二</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="85" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">2018.12</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="94" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">/</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="94" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">2019.7（少鵬）</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="67" height="22"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">/</span></p></td></tr><tr style=";height:23px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="66" height="23"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">二星</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="37" height="23"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">1人</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="65" height="23"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">谢晓娴</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="76" height="23"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">2018/5/23</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="39" height="23"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">一</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="85" height="23"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">2018.7</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="94" height="23"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">2019.6（麗紅）</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="94" height="23"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">/</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(234, 239, 247); padding: 1px 1px 0px;" width="67" height="23"><p style="text-align:left;text-autospace:none"><span style=";font-family:SimSun;color:black">/</span></p></td></tr></tbody></table><ul class=" list-paddingleft-2" style="list-style-type: disc;"><li><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">星級培養的注意事項</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1）<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 9px; line-height: normal;">&nbsp;&nbsp; </span>星級人數規劃需根據班級人數合理分配，切勿出現過剩或缺失；</span></p></li></ul><p style="margin-left:32px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2）<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>對班級人員進行年度目標溝通後並制定合理的學習及考核計劃；</span></p><p style="margin-left:32px;text-align:left;text-autospace:none"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3）<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 9px; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>需嚴格遵守公司的星級評定資格，切勿違規操作；</span></p><p style="margin-left:32px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">4）<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>老帶新一般採用高星級帶低星級，且需以周、月、季度的方式對學習進度進行跟蹤指導，確保學習計劃順利進行，不耽誤班級人員技能的補充；</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1.2梯隊建設</span></p><ul class=" list-paddingleft-2" style="list-style-type: disc;"><li><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">梯隊建設的概念</span></p></li></ul><p><br></p><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p><span style="color: black; font-family: 微软雅黑, Microsoft YaHei;">企業建立人才資源庫，為某崗位/通道層級準備後備人才，以避免企業經營的人才斷層。通常分為：關鍵崗位繼任人才、一般崗位繼任人才、通道層級儲備人才。</span></p><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><ul class=" list-paddingleft-2" style="list-style-type: disc;"><li><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">梯隊建設的意義</span></p></li></ul><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">對於公司：源源不斷的產生公司需要的人才，強人才培養的針對性和效率，勵人才、減少人才流失，少外部引進人才的磨合，保公司的永續發展</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">對於個人：明確個人職業發展與規劃，加提升個人能力的機會，少價值不大的輪崗，升個人關鍵能力</span></p><p><br></p><ul class=" list-paddingleft-2" style="list-style-type: disc;"><li><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">理想的人才梯隊體系特征</span></p></li></ul><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-left:32px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;margin-left:32px;text-align:left;text-autospace: none"><span style="font-family: 微软雅黑, Microsoft YaHei;"><span style="font-size: 16px; color: black;">1）</span><span style="font-size: 9px; color: black;">&nbsp;&nbsp; </span><span style="font-size: 16px; color: black;">未來幾年內公司的人才需求有清晰的認識</span></span></p><p style="margin-top:auto;margin-bottom: auto;margin-left:32px;text-align:left;text-autospace: none"><span style="font-family: 微软雅黑, Microsoft YaHei;"><span style="font-size: 16px; color: black; font-family: 微软雅黑, Microsoft YaHei;">2）</span><span style="font-size: 9px; color: black; font-family: 微软雅黑, Microsoft YaHei;">&nbsp;&nbsp; </span><span style="font-size: 16px; color: black; font-family: 微软雅黑, Microsoft YaHei;">有明確的培訓路線，可以充分發掘人的潛力</span></span></p><p style="margin-top:auto;margin-bottom: auto;margin-left:32px;text-align:left;text-autospace: none"><span style="font-family: 微软雅黑, Microsoft YaHei;"><span style="font-size: 16px; color: black; font-family: 微软雅黑, Microsoft YaHei;">3）</span><span style="font-size: 9px; color: black; font-family: 微软雅黑, Microsoft YaHei;">&nbsp;&nbsp; </span><span style="font-size: 16px; color: black; font-family: 微软雅黑, Microsoft YaHei;">有明確的流程、計畫和機制來保證人才的培養</span></span></p><p style="margin-top:auto;margin-bottom: auto;margin-left:32px;text-align:left;text-autospace: none"><span style="font-family: 微软雅黑, Microsoft YaHei;"><span style="font-size: 16px; color: black; font-family: 微软雅黑, Microsoft YaHei;">4）</span><span style="font-size: 9px; color: black; font-family: 微软雅黑, Microsoft YaHei;">&nbsp;&nbsp; </span><span style="font-size: 16px; color: black; font-family: 微软雅黑, Microsoft YaHei;">各相關者知道做什麼、會做、並願意做</span></span></p><p style="margin-top:auto;margin-bottom: auto;margin-left:32px;text-align:left;text-autospace: none"><span style="font-family: 微软雅黑, Microsoft YaHei;"><span style="font-size: 16px; color: black; font-family: 微软雅黑, Microsoft YaHei;">5）</span><span style="font-size: 9px; color: black; font-family: 微软雅黑, Microsoft YaHei;">&nbsp;&nbsp; </span><span style="font-size: 16px; color: black; font-family: 微软雅黑, Microsoft YaHei;">公司需求的人才不斷層</span></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><ul class=" list-paddingleft-2" style="list-style-type: disc;"><li><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">人才梯隊建設的原則</span></p></li></ul><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-left:32px;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;margin-left:32px;text-align:left;text-autospace: none"><span style="font-family: 微软雅黑, Microsoft YaHei;"><span style="font-size: 16px; color: black;">1）</span><span style="font-size: 9px; color: black;">&nbsp;&nbsp; </span><span style="font-size: 16px; color: black;">突出重點崗位：對中高層管理崗位、市場緊缺的專業崗位重點培養</span></span></p><p style="margin-top:auto;margin-bottom: auto;margin-left:32px;text-align:left;text-autospace: none"><span style="font-family: 微软雅黑, Microsoft YaHei;"><span style="font-size: 16px; color: black; font-family: 微软雅黑, Microsoft YaHei;">2）</span><span style="font-size: 9px; color: black; font-family: 微软雅黑, Microsoft YaHei;">&nbsp;&nbsp; </span><span style="font-size: 16px; color: black; font-family: 微软雅黑, Microsoft YaHei;">關注短板能力：教練式輔導、人員激勵、持續改進、客戶導向等關鍵能力</span></span></p><p style="margin-top:auto;margin-bottom: auto;margin-left:32px;text-align:left;text-autospace: none"><span style="font-family: 微软雅黑, Microsoft YaHei;"><span style="font-size: 16px; color: black; font-family: 微软雅黑, Microsoft YaHei;">3）</span><span style="font-size: 9px; color: black; font-family: 微软雅黑, Microsoft YaHei;">&nbsp;&nbsp; </span><span style="font-size: 16px; color: black; font-family: 微软雅黑, Microsoft YaHei;">選苗重于育才：注重對人才標準建設和梯隊人才個人的判斷</span></span></p><p style="margin-top:auto;margin-bottom: auto;margin-left:32px;text-align:left;text-autospace: none"><span style="font-family: 微软雅黑, Microsoft YaHei;"><span style="font-size: 16px; color: black; font-family: 微软雅黑, Microsoft YaHei;">4）</span><span style="font-size: 9px; color: black; font-family: 微软雅黑, Microsoft YaHei;">&nbsp;&nbsp; </span><span style="font-size: 16px; color: black; font-family: 微软雅黑, Microsoft YaHei;">注重早期發展：注重新人入職一年內的評估和培養，逐步從學生開始培養</span></span></p><p style="margin-top:auto;margin-bottom: auto;margin-left:32px;text-align:left;text-autospace: none"><span style="font-family: 微软雅黑, Microsoft YaHei;"><span style="font-size: 16px; color: black; font-family: 微软雅黑, Microsoft YaHei;">5）</span><span style="font-size: 9px; color: black; font-family: 微软雅黑, Microsoft YaHei;">&nbsp;&nbsp; </span><span style="font-size: 16px; color: black; font-family: 微软雅黑, Microsoft YaHei;">內部培養為主：對於關鍵崗位，今後優先考慮內部人才</span></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><ul class=" list-paddingleft-2" style="list-style-type: disc;"><li><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">人才梯隊建設流程</span></p></li></ul><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><img src="/upload/question/20200520/1589953377651755.png" alt="image.png"></span></p><ul class=" list-paddingleft-2" style="list-style-type: disc;"><li><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">人員選拔的兩種思路</span></p></li></ul><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><table cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" width="70" valign="top"><p style="text-align:center;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">思路</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: initial; padding: 0px 7px; word-break: break-all;" width="200" valign="top"><p style="text-align:center;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">優點</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: initial; padding: 0px 7px; word-break: break-all;" width="275" valign="top"><p style="text-align:center;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">缺點</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="70"><p style="text-align:center;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">相馬<br></span></p><br></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="200" valign="top"><p style="margin-left:48px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">•<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>成本低，主要通過主觀判斷得出結論</span></p><p style="margin-left:48px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">•<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>不受時間、空間限制，對人的能力素質可以全面了解</span></p><p style="margin-left:48px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">•<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>可以挖掘人潛在的，深層次的甚至隱形素質</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="275" valign="top"><p style="margin-left:48px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">•<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>靠管理者個人經驗判斷，有局限性</span></p><p style="margin-left:48px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">•<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>觀察範圍有限，難以在廣大的空間範圍發現人才</span></p><p style="margin-left:48px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">•<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>“馬”可以投其所好，難以公平公正的選拔人才</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="70"><p style="text-align:center;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">賽馬<br></span></p><br></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="200" valign="top"><p style="margin-left:48px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">•<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>平等，以結果定勝負，受到主觀因素的干預較少</span></p><p style="margin-left:48px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">•<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>公正公開，有監督</span></p><p style="margin-left:48px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">•<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>不容易埋沒人才，有意願的人可以不受空間範圍的限制參加選拔</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="275" valign="top"><p style="margin-left:48px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">•<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>成本高，想要真正的賽出好馬，必須建立一套完整的制度（標準、指標、規範程序）</span></p><p style="margin-left:48px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">•<span style="font: 9px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>資訊受限，可能存在信息部隊稱的情況</span></p></td></tr></tbody></table><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">相馬、賽馬哪個更重要，不能一概而論，不同規模、不同行業、不同時期的企業人力資源管理模式都不同，對於創業期的企業，需要的是一小批能與企業同甘共苦、共同成長的人才，所以相馬比賽馬更重要；而當企業發展到一定規模，崗位分析、規則制度、績效考核、薪酬福利等均很完善後，賽馬比相馬更有意義。</span></p><p style="margin-left:32px;text-align:left;text-autospace:none"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></strong></span></p><ul class=" list-paddingleft-2" style="list-style-type: disc;"><li><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">人才培養的方式</span></p></li></ul><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;">&nbsp;&nbsp;&nbsp;&nbsp; 1）參加相關培訓</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;">&nbsp; &nbsp; &nbsp;2）安排內外部教練溝通輔導</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;">&nbsp; &nbsp; &nbsp;3）工作職責調整</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;">&nbsp; &nbsp; &nbsp;4）工作崗位調整</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;">&nbsp; &nbsp; &nbsp;5）指定閱讀相關書籍並書面提交心得或小組分享</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;">&nbsp; &nbsp; &nbsp;6）外派參觀參考</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;">&nbsp; &nbsp; &nbsp;7）外派參加各種交流</span></p><p>&nbsp;</p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span><br></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><ul class=" list-paddingleft-2" style="list-style-type: disc;"><li><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">人才梯隊建設的考核指標</span></p></li></ul><p><span style="font-family: SimSun;"></span></p><table width="663" cellspacing="0" cellpadding="0"><tbody><tr style=";height:27px" class="firstRow"><td style="border: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="113" height="27"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">後備庫人才數量</span></p></td><td style="border-top: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); border-bottom: 1px solid rgb(127, 127, 127); border-image: initial; border-left: none; padding: 5px 10px;" width="550" height="27"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size: 16px; color: black; font-family: 微软雅黑, Microsoft YaHei;">2018年：關鍵崗位繼任人才x人、一般崗位繼任人才x人、通道層級儲備人才x人</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size: 16px; color: black; font-family: 微软雅黑, Microsoft YaHei;">2019年：關鍵崗位繼任人才x人、一般崗位繼任人才x人、通道層級儲備人才x人</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size: 16px; color: black; font-family: 微软雅黑, Microsoft YaHei;">2020年：關鍵崗位繼任人才x人、一般崗位繼任人才x人、通道層級儲備人才x人</span></p></td></tr><tr style=";height:27px"><td style="border-right: 1px solid rgb(127, 127, 127); border-bottom: 1px solid rgb(127, 127, 127); border-left: 1px solid rgb(127, 127, 127); border-image: initial; border-top: none; padding: 5px 10px;" width="113" height="27"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">能力提升指標</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="550" height="27"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">個人評估能力得分整體同比增長</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">x%</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">關鍵崗位繼任人才激勵能力、</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">xx</span><span style="font-size:16px;font-family:PMingLiU;color:black">能力得分整體超過</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">x%</span></p></td></tr><tr style=";height:27px"><td style="border-right: 1px solid rgb(127, 127, 127); border-bottom: 1px solid rgb(127, 127, 127); border-left: 1px solid rgb(127, 127, 127); border-image: initial; border-top: none; padding: 5px 10px;" width="113" height="27"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">相關人員滿意度</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="550" height="27"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">培養物件對培養發展工作的滿意度調查超過</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">x</span><span style="font-size:16px;font-family:PMingLiU;color:black">分</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">梯隊人員流失率低於</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">x%</span></p></td></tr><tr style=";height:27px"><td style="border-right: 1px solid rgb(127, 127, 127); border-bottom: 1px solid rgb(127, 127, 127); border-left: 1px solid rgb(127, 127, 127); border-image: initial; border-top: none; padding: 5px 10px;" width="113" height="27"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">計劃覆蓋率</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="550" height="27"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p><span style="font-family:PMingLiU;color:black">關鍵崗位、一般崗位繼任人才個人工作計畫在</span><span style="font-family:'微软雅黑',sans-serif;color:black">x</span><span style="font-family:PMingLiU;color:black">月前全部完成，通道層級儲備人才計畫在</span><span style="font-family:'微软雅黑',sans-serif;color:black">x</span><span style="font-family:PMingLiU;color:black">月前完成</span><span style="font-family:'微软雅黑',sans-serif;color:black">xx%</span></p></td></tr><tr style=";height:27px"><td style="border-right: 1px solid rgb(127, 127, 127); border-bottom: 1px solid rgb(127, 127, 127); border-left: 1px solid rgb(127, 127, 127); border-image: initial; border-top: none; padding: 5px 10px;" width="113" height="27"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">計劃實施率</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="550" height="27"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p><span style="font-family:PMingLiU;color:black">後備人員中列入個人工作發展計畫的專案（培訓、教練、輪崗等）實施率超過</span><span style="font-family:'微软雅黑',sans-serif;color:black">x%</span></p></td></tr></tbody></table><p><br><span style="font-family: SimSun;"></span></p><p><span style="font-family: SimSun;"></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1.3核心團隊</span></p><ul class=" list-paddingleft-2" style="list-style-type: disc;"><li><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">什麼是核心團隊</span></p></li></ul><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p><span style="font-family:PMingLiU;color:black">即與團隊領導者志同道合的人，這群人對一個方向的共識，有不約而同的價值觀和心智模式。核心團隊精神的火苗一旦燃起，就自然會不斷擴散、感染周圍的一切。同時核心團隊是一個公司最後的堡壘和護城河，員工會有流失，模式會被抄襲，但唯有核心團隊穩固如初，他們是團隊跨過刀山火海的有力保障。</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><ul class=" list-paddingleft-2" style="list-style-type: disc;"><li><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">選择核心人才的5個標準</span></p></li></ul><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-left:32px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;margin-left:32px;text-align:left;text-autospace: none"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">1</span><span style="font-size:16px;font-family:PMingLiU;color:black">）</span><span style="font-size:9px;font-family:'微软雅黑',sans-serif;color:black">&nbsp;&nbsp; </span><span style="font-size:16px;font-family:PMingLiU;color:black">他們對其他人是否有很大的影響</span></p><p style="margin-top:auto;margin-bottom: auto;margin-left:32px;text-align:left;text-autospace: none"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">2</span><span style="font-size:16px;font-family:PMingLiU;color:black">）</span><span style="font-size:9px;font-family:'微软雅黑',sans-serif;color:black">&nbsp;&nbsp; </span><span style="font-size:16px;font-family:PMingLiU;color:black">他們能否給團隊帶來互補性的才能</span></p><p style="margin-top:auto;margin-bottom: auto;margin-left:32px;text-align:left;text-autospace: none"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">3</span><span style="font-size:16px;font-family:PMingLiU;color:black">）</span><span style="font-size:9px;font-family:'微软雅黑',sans-serif;color:black">&nbsp;&nbsp; </span><span style="font-size:16px;font-family:PMingLiU;color:black">他們在團隊裡是否擔任要職</span></p><p style="margin-top:auto;margin-bottom: auto;margin-left:32px;text-align:left;text-autospace: none"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">4</span><span style="font-size:16px;font-family:PMingLiU;color:black">）</span><span style="font-size:9px;font-family:'微软雅黑',sans-serif;color:black">&nbsp;&nbsp; </span><span style="font-size:16px;font-family:PMingLiU;color:black">他們能否增加我和團隊的價值</span></p><p style="margin-top:auto;margin-bottom: auto;margin-left:32px;text-align:left;text-autospace: none"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">5</span><span style="font-size:16px;font-family:PMingLiU;color:black">）</span><span style="font-size:9px;font-family:'微软雅黑',sans-serif;color:black">&nbsp;&nbsp; </span><span style="font-size:16px;font-family:PMingLiU;color:black">他們能否給核心圈的其他成員帶來積極影響</span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">田霞</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-05-20 13:55:34</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><span style="font-size: 18px;">二、團隊文化</span></strong></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1、什麼是團隊文化<br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">定義：</span></p><p><span style="font-size: 14px;"><span style="font-family: PMingLiU;">團隊文化是</span><strong><span style="font-family: PMingLiU;">團隊人員基本理念、價值觀念與行為模式的總和</span></strong><span style="font-family: PMingLiU;">，它被大多數成員所接受與擁有，深入人心，對人的言行舉止產生有形或無形的約制力量。一個團隊只有在優秀團隊文化的指引下，才有了前行的力量，每個人才能找到在團中的地位和價值，團隊成員才能齊心協力，才能共同達成團隊的目標。團隊文化通過什麼來樹立？</span><strong><span style="font-family: PMingLiU;">使命、願景、價值觀</span></strong><span style="font-family: PMingLiU;">、價值主張、行為規範、能力模型、團隊精神。然後是，戰略規劃、策略、組織結構、業務流程、人力資源、經營運作。</span></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">作用：</span></p><table cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" width="130"><p style="text-align:center;text-autospace:none"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">指導作用</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></strong></span></p><p style="margin-left:32px;text-align:center;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: initial; padding: 0px 7px; word-break: break-all;" width="538" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">經營（思維方式、處理問題的方式）和價值觀（共同的價值目標）指導</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">企業目標的指導</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="130"><p style="text-align:center;text-autospace:none"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">約束作用</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></strong></span></p><p style="text-align:center;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="538" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">完善的管理度是文化形成的必要條件</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">員工違反了制度，除了受到制度的處罰，還要受到其他同事道德輿論的譴責</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="130"><p style="text-align:center;text-autospace:none"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">凝聚作用</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></strong></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="538" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">家文化<br></span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="130"><p style="text-align:center;text-autospace:none"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">激勵作用</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></strong></span></p><p style="text-align:center;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="538" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">自我價值得到體現（滿足感、自豪感）</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">領導對員工的關心和鼓勵（感動）</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">企業的精神和形象（榮譽感、自豪感）</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="130"><p style="text-align:center;text-autospace:none"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">調整和適應作用</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></strong></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="538" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">用文化調解矛盾</span></p></td></tr></tbody></table><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2、如何建立團隊文化</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif">1</span><span style="font-size:16px;font-family:PMingLiU">）確立建設團隊文化的核心組織</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif">2</span><span style="font-size:16px;font-family:PMingLiU">）明確使命、願景、價值觀</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif">:</span><span style="font-size:16px;font-family:PMingLiU">使命是團隊存在的理由，與所追求的價值；願景是團隊的階段性目標；價值觀決定制度建設</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif">3</span><span style="font-size:16px;font-family:PMingLiU">）通過制度體系的建立表達團隊的價值取向；反對什麼，支持什麼？</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif">4</span><span style="font-size:16px;font-family:PMingLiU">）招收有共同價值觀的人，血型不同，輸血可能會害了你</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif">5</span><span style="font-size:16px;font-family:PMingLiU">）堅定信念，堅持</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif">100</span><span style="font-size:16px;font-family:PMingLiU">年不動搖</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3、我們需要什麼樣的團隊文化</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1）</span><span style="font-family:PMingLiU">我們的使命：<strong><span style="font-family:PMingLiU">耐心且專業的解決會員的每一個問題</span></strong></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span><br></p><table width="441" cellspacing="0" cellpadding="0"><tbody><tr style=";height:17px" class="firstRow"><td style="border: 1px solid windowtext; background: rgb(225, 144, 85); padding: 5px 10px;" width="123" height="17"><p style="margin-left:32px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">名詞</span></span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(225, 144, 85); padding: 5px 10px;" width="319" height="17"><p style="margin-left:32px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">解釋<br></span></p></td></tr><tr style=";height:34px"><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 5px 10px;" width="123" height="34"><p style="margin-left:32px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">耐心</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 5px 10px;" width="319" height="34"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1、服務態度</span></p><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2、語音語調</span></p></td></tr><tr style=";height:39px"><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 5px 10px;" width="123" height="39"><p style="margin-left:32px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">專業<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 5px 10px;" width="319" height="39"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1、專業的業務知識</span></p><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2、專業的表達（台灣口音）</span></p></td></tr><tr style=";height:41px"><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 5px 10px;" width="123" height="41"><p style="margin-left:32px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">解決問題</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 5px 10px;" width="319" height="41"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1、解決眼前的問題</span></p><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2、解決根本問題</span></p></td></tr></tbody></table><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2）</span><span style="font-family:PMingLiU">我們的願景：<strong><span style="font-family:PMingLiU">擁有最專業的人才，成為最有凝聚力的團隊</span></strong></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span><br></p><table width="491" cellspacing="0" cellpadding="0"><tbody><tr style=";height:13px" class="firstRow"><td style="border: 1px solid windowtext; background: rgb(225, 144, 85); padding: 5px 10px;" width="141" height="13"><p style="margin-left:32px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">名詞</span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(225, 144, 85); padding: 5px 10px;" width="350" height="13"><p style="margin-left:32px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">解釋</span></span></p></td></tr><tr style=";height:27px"><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 5px 10px;" width="141" height="27"><p style="margin-left:32px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">專業的人才</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 5px 10px;" width="350" height="27"><p style="margin-left:32px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;margin-left:32px;text-align:left;text-autospace: none"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">1</span><span style="font-size:16px;font-family:PMingLiU;color:black">、客戶服務層面整理達到專業水準</span></p><p style="margin-top:auto;margin-bottom: auto;margin-left:32px;text-align:left;text-autospace: none"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">2</span><span style="font-size:16px;font-family:PMingLiU;color:black">、成員綜合實力最強、最專業</span></p></td></tr><tr style=";height:22px"><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 5px 10px;" width="141" height="22"><p style="margin-left:32px;text-align:left;text-autospace:none">凝聚的團隊</p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 5px 10px;" width="350" height="22"><p style="margin-left:32px;text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p><span style="font-family:PMingLiU;color:black">團隊成員有歸屬感、集體榮譽感、主人翁意識</span></p></td></tr></tbody></table><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3）我們的價值觀</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><table width="646" cellspacing="0" cellpadding="0"><tbody><tr style=";height:20px" class="firstRow"><td rowspan="3" style="border: 1px solid windowtext; background: rgb(237, 125, 49); padding: 1px 7px 0px;" width="66" height="20"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">骨幹<br></span></p><br></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 1px 7px 0px;" width="142" height="20"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">以團隊為先<br></span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 1px 7px 0px;" width="439" height="20"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p><span style="font-family:PMingLiU;color:black">以身作則，做人做事應不影響團隊的穩定、和諧、聲譽</span></p></td></tr><tr style=";height:20px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 1px 7px 0px;" width="142" height="20"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">主動、自動、行動</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 1px 7px 0px;" width="439" height="20"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">主動不推諉、自主自發、快速行動</span></p></td></tr><tr style=";height:20px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 1px 7px 0px;" width="142" height="20"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">盡全力做到最好</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 1px 7px 0px;" width="439" height="20"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p><span style="font-family:PMingLiU;color:black">沒有解決不了的問題，只有想不到的方法，同一件事，下次一定會比上次做的更好</span></p></td></tr><tr style=";height:20px"><td rowspan="3" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; background: rgb(237, 125, 49); padding: 1px 7px 0px;" width="66" height="20"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">客服</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 1px 7px 0px;" width="142" height="20"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">積極向上</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 1px 7px 0px;" width="439" height="20"><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">保持上進心、正面看待問題</span></p></td></tr><tr style=";height:20px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 1px 7px 0px;" width="142" height="20"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">遵守規則<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 1px 7px 0px;" width="439" height="20"><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">不要嘗試打破規則（如規則不合理，應提出改進建議）</span></p></td></tr><tr style=";height:20px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 1px 7px 0px;" width="142" height="20"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">盡職盡責<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 1px 7px 0px;" width="439" height="20"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">對自己職責範圍內的工作負責</span></p></td></tr><tr style=";height:20px"><td rowspan="3" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; background: rgb(237, 125, 49); padding: 1px 7px 0px;" width="66" height="20"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">團隊</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 1px 7px 0px;" width="142" height="20"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">信任<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 1px 7px 0px;" width="439" height="20"><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">信任自己的夥伴，對任何事情有疑問，需及時溝通回饋，保持資訊公開透明</span></p></td></tr><tr style=";height:20px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 1px 7px 0px;" width="142" height="20"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">尊重<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 1px 7px 0px;" width="439" height="20"><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">重視團隊成員的感受與意見，可以不採納意見，但是要尊重對方說出意見的權力</span></p></td></tr><tr style=";height:20px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 1px 7px 0px;" width="142" height="20"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">包容<br></span></p><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 1px 7px 0px;" width="439" height="20"><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">包容他人的不完美，幫助新同事融入團隊，不排擠，不傳訛</span></p></td></tr></tbody></table><p><br></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">4、如何建立我們的團隊文化<br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1）梳理良好氛圍</span></p><p><span style="font-family: SimSun;"></span></p><table cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" width="121"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">領導“大氣”</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: initial; padding: 0px 7px; word-break: break-all;" width="387" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">願景宏偉，創造機會</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">給組織寬鬆的環境</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">給下屬嘗試的機會</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">對目標毫不動搖</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">言而有信，勇於承擔</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="121"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">樹立“正氣”</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="387" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">樹立基本法則，基本信仰</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">懲戒壞的</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">宣揚好的</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">鼓勵差的</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">以身作則</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="121"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">激勵“士氣”</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="387" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">目標激勵</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">榮譽激勵</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">成就激勵</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">物質激勵</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">即時激勵</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">危機激勵</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="121"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">學習“風氣”</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="387" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">培訓機制的建立</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">學習機會的提供</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">能力與任職機會的對接</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">學習心態的樹立</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="121"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">團隊“義氣”</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="387" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">提升團隊的凝聚力</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">提升團隊的執行力、戰鬥力</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">促進團隊成員的相互信任</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">促進團隊成員間的互助文化</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="121"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">組織“和氣”</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="387" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">形成包容的氛圍</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">建立即時溝通的管道</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">形成情緒釋放的通道</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">促進部門間的日常交流</span></p></td></tr></tbody></table><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2）具體的方法</span></p><p><br></p><table cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" width="121"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">開會</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: initial; padding: 0px 7px; word-break: break-all;" width="548" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">小組會議、周會、月會、表彰會</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="121"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">樹立榜樣<br></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="548" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">標杆隊員、好人好事、能力優秀標注、形式不限、角度不限</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="121"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">團隊活動<br></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="548" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">增加粘度與瞭解的重要手段，團隊形式的各種活動、聚會、聚餐、親子等</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="121"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">溝通分享<br></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="548" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">建立縱向和橫向的溝通管道</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">開發形式多樣的溝通分享平臺</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="121"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">員工關懷<br></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="548" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">生活</span><span style="font-size: 16px;font-family:'微软雅黑',sans-serif;color:black">/</span><span style="font-size:16px;font-family:PMingLiU;color:black">工作環境</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">把握動機與動力、瞭解困惑與困難、增進交流與交往、個人提升與發展</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="121"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">競賽<br></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="548" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">激勵士氣的最有效辦法</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">小隊、個人、形式不限</span></p></td></tr></tbody></table><p><span style="font-family: SimSun;"></span><br></p><p><span style="font-family: SimSun;"></span><br></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">田霞</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-05-20 15:51:34</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong>三、人員管理</strong></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">1、人員基礎情況瞭解<strong><br></strong></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">瞭解員工可思考以下幾個問題</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">1）<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp;&nbsp; </span>為什麼開心，為什麼不開心？</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">2）<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp;&nbsp; </span>怎樣的成長背景和家庭環境成就了她現在的性格？</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">3）<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp;&nbsp; </span>這樣的性格怎樣得到快樂？</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">4）<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp;&nbsp; </span>用什麼樣的方式來梳理她的情緒？</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">5）<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp;&nbsp; </span>她最近的生活是什麼樣的狀況？發生了怎樣的變化？</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">6）<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp;&nbsp; </span>哪些因素會使得她不開心？</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">7）<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp;&nbsp; </span>面對這些因素，如何調整心態？如何激勵她前進？</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">8）<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp;&nbsp; </span>什麼樣的工作前景和生活前景對她構成吸引力？</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">2、從員工進班開始，就要給他制定專屬員工檔案。員工檔案表參考如下</span></p><p><br></p><table cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px;" width="44" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">工號</span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="45" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">姓名</span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="71" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">入職時間</span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="52" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">生日</span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="54" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">星級</span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="79" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">家庭背景</span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="96" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">性格</span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; padding: 0px 7px;" width="240" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">適用溝通與激勵方式</span></p></td></tr><tr><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="44" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="45" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="71" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="52" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="54" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="79" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="96" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="240" valign="top"><br></td></tr><tr><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="44" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="45" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="71" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="52" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="54" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="79" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="96" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="240" valign="top"><br></td></tr><tr><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="44" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="45" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="71" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="52" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="54" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="79" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="96" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="240" valign="top"><br></td></tr><tr><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="44" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="45" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="71" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="52" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="54" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="79" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="96" valign="top"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="240" valign="top"><br></td></tr></tbody></table><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">3、人員工作状态、工作規範、工作情緒管理</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">3.1&nbsp;<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp;</span>巡場走動式管理</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">1）<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp;&nbsp; </span>發現人員情緒激動，無法順利解決當下的問題，需立即協助掛斷電話，並當下立刻找到會議室進行溝通</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">2）<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp;&nbsp; </span>發現人員一段時間工作狀態出現問題或違反工作規範或不服從抵觸心理等， 也需當下立刻找到會議室進行溝通</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">3.2&nbsp;人員溝通</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">1）<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp;&nbsp; </span>有效溝通的步驟</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">2）<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp;&nbsp; </span>與員工溝通的要點</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">3）&nbsp; 如何妥善處理員工的抱怨</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">3.3 工作紀律制度</span></p><p><br></p><table width="667" cellspacing="0" cellpadding="0"><tbody><tr style=";height:27px" class="firstRow"><td colspan="2" style="border: 1px solid windowtext; background: rgb(242, 220, 219); padding: 0px 2px;" width="667" height="27"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">現場制度細則</span></strong><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"> &nbsp;</span></strong></span></p></td></tr><tr style=";height:32px"><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; background: rgb(242, 220, 219); padding: 0px 2px;" width="71" nowrap="" height="32"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">類別</span></strong></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(242, 220, 219); padding: 0px 2px;" width="596" nowrap="" height="32"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">具&nbsp; 体&nbsp; 内&nbsp; 容</span></strong></span></p></td></tr><tr style=";height:29px"><td rowspan="13" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; background: white; padding: 0px 2px;" width="71" nowrap="" height="29"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">电话接听</span></strong></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" nowrap="" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">電話響2聲後接聽（第3聲響前接起） </span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" nowrap="" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">接聽電話過程中，要保持端正的坐姿，不能東張西望，更不能趴在桌子上</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" nowrap="" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">接聽電話過程中，有私人電話響起或短信提醒，請接聽完該通電話後再進行處理</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" nowrap="" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">接聽電話過程中，不得與同事閒談，玩指甲，或是做其他非工作事情</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" nowrap="" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">接聽電話過程中，請教其他客服問題要先按靜音鍵，然後再詢問</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" nowrap="" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">挂机后，不得大聲抱怨辱罵會員、摔鍵盤耳機</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" nowrap="" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">電話客服若要臨時離位，需請主管助理（或其他高級專員）設置取消接聽後方可離開</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" nowrap="" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">不得與會員閒談和討論與業務無關的問題</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" nowrap="" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">會員要求客服提供私人資料時，只能提供自己的客服編號與姓氏</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" nowrap="" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">與會員溝通時，若需承諾處理時間，回答應比預定時間稍長</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" nowrap="" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">碰到突發狀況自己無法處理時，要及時將該訊息回饋給主管助理(切記勿推諉给其他高级专员或隱瞞)</span></p></td></tr><tr style=";height:29px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; background: white none repeat scroll 0% 0%; padding: 0px 2px; word-break: break-all;" width="596" nowrap="" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">不可出现电话挂断、静音、拔线、响断不接等故意不接电话的行為</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" nowrap="" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">對經常打來的詢問電話，客服回答應力求統一，嚴格執行電話客服作業指導書</span></p></td></tr><tr style=";height:29px"><td rowspan="10" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; background: white; padding: 0px 2px;" width="71" nowrap="" height="29"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">现场要求</span></strong></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" nowrap="" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">客服需早上9：00整準時在位，此時電腦已打開、RTX處於登錄狀態；遲到請與現場報備</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" nowrap="" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">工作時，接聽超過1分鐘的私人電話，請移步至衛生間或陽台接聽</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" nowrap="" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">午休時，在工作區內用餐切勿大聲喧嘩（避免影響其他客服電話接聽）</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" nowrap="" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">下班時，請關閉電腦主機和顯示器，斷絕電源後方可離開</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" nowrap="" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">檢查電腦、話機、耳麥是否可用，準備筆、筆記本用於記錄</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">工作期间需积极配合服从工作安排、調休安排及管理</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">不可使用工作電腦上外網及其他通訊軟體</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">在辦公區域大聲撥打或接聽私人电话未移步、頻繁发短信、看微博、看朋友圈、玩游戏等玩手機現象</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">现场需设好电话后方可离开座位热饭或吃饭，避免影响现场正常运作</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">積極參與部門及公司活動</span></p></td></tr><tr style=";height:29px"><td rowspan="4" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; background: white; padding: 0px 2px;" width="71" height="29"><p style="text-align:center"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">調休</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; background: white none repeat scroll 0% 0%; padding: 0px 2px; word-break: break-all;" width="596" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">排班需求每月5日前即可開始提，每5天會進行班表確認可再次提需求，直至每月20號確定最終版本，不可再改</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">法定节假日值班：原则按人员轮流，但人员有权放弃加班，同时需自行找好代上班人员</span></p></td></tr><tr style=";height:29px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" height="29"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">周未加班可依现场人力需求每人每月安排连休或雙休</span></p></td></tr><tr style=";height:39px"><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: white; padding: 0px 2px;" width="596" height="39"><p style="text-align:left"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">假日或週末若需临时特殊事情调换，则需考虑不能連上7天的情况，及需要找好技能相等人员并向主任说明，主任适情况批准</span></p></td></tr></tbody></table><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">田霞</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-05-20 15:57:17</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><span style="font-size: 18px;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">四、質量管理</span></strong></span></p><p><span style="font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;">1、服務質量的概念</span></p><p><span style="font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;"></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">服務能夠滿足規定和潛在需求的特徵和特性的總和，是指服務工作能夠滿足被服務者需求的程度。是企業為使目標顧客滿意而提供的最低服務水準，也是企業保持這一預定服務水準的連貫性程度。</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">2、服務的價值</span></p><p><img src="/upload/question/20200520/1589961287613862.png" alt="image.png"></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif">3</span><span style="font-size:16px;font-family:PMingLiU">、服務品質的界定</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU">對服務而言﹐服務品質必須在服務提供過程中評估﹐且通常是在顧客與接洽的員工進行服務接觸時。顧客對服務品質的滿意度是以其實際認知的服務與對服務的期望二者做比較而來。</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"></span><img src="/upload/question/20200520/1589961380476318.png" alt="image.png"></p><p><br></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif">4</span><span style="font-size:16px;font-family:PMingLiU">、客服主任如何做品質管制</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU">事前預防：新知識通知到位、定期培訓</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU">事中控制：電話監聽、現場督導</span></p><p style="margin-top: auto; margin-bottom: auto; text-align: left;"><span style="font-size:16px;font-family:PMingLiU">事後分析：分析問題，找出原因以及解決方案</span></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">田霞</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-05-20 16:10:22</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px;"></span></strong></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><strong><span style="font-size:18px;font-family:PMingLiU">五、流程管理</span></strong></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif">1</span><span style="font-size:16px;font-family:PMingLiU">、流程管理的概念</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU">通過一些技術手段，對企業的業務流程進行系統化的梳理，分析，改善和監控，並通過對業務流程的不斷優化，從而規範業務活動，有效降低業務處理成本，提高業務處理效率，並快速反應市場與客戶需求，進而提升企業決策反應能力。</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:宋体">&nbsp;</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif">2</span><span style="font-size:16px;font-family:PMingLiU">、流程管理的價值</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU">降低流程成本</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU">提高流程品質、減少錯誤</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU">減少流程周轉時間</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU">降低培訓成本</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:9px;font-family:'微软雅黑',sans-serif">&nbsp;</span><span style="font-size:16px;font-family:PMingLiU">減少內部支持需求</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:9px;font-family:'微软雅黑',sans-serif">&nbsp;</span><span style="font-size:16px;font-family:PMingLiU">減少客戶投訴</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU">提高流程效率</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:9px;font-family:'微软雅黑',sans-serif">&nbsp;</span><span style="font-size:16px;font-family:PMingLiU">提高流程執行力</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:9px;font-family:'微软雅黑',sans-serif">&nbsp;</span><span style="font-size:16px;font-family:PMingLiU">降低流程失效風險</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:宋体">&nbsp;</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif">3</span><span style="font-size:16px;font-family:PMingLiU">、流程構建的工作步驟</span></p><p><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><img src="/upload/question/20200520/1589961606426252.png" alt="image.png"></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">4、</span><span style="font-family:PMingLiU">流程優化的主要內容</span></p><p><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><table width="652" cellspacing="0" cellpadding="0"><tbody><tr style=";height:10px" class="firstRow"><td style="border: 1px solid rgb(127, 127, 127); background: rgb(172, 185, 202); padding: 5px 10px;" width="161" height="10"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">清除</span></p></td><td style="border-top: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); border-bottom: 1px solid rgb(127, 127, 127); border-image: initial; border-left: none; background: rgb(172, 185, 202); padding: 5px 10px;" width="153" height="10"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">簡化<br></span></p></td><td style="border-top: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); border-bottom: 1px solid rgb(127, 127, 127); border-image: initial; border-left: none; background: rgb(172, 185, 202); padding: 5px 10px;" width="162" height="10"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">整合</span></p></td><td style="border-top: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); border-bottom: 1px solid rgb(127, 127, 127); border-image: initial; border-left: none; background: rgb(172, 185, 202); padding: 5px 10px;" width="176" height="10"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">自動化</span></p></td></tr><tr style=";height:7px"><td style="border-right: 1px solid rgb(127, 127, 127); border-bottom: 1px solid rgb(127, 127, 127); border-left: 1px solid rgb(127, 127, 127); border-image: initial; border-top: none; padding: 5px 10px;" width="161" height="7"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">過量產出</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="153" height="7"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">表格<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="162" height="7"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">活動<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="176" height="7"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">臟活<br></span></p></td></tr><tr style=";height:10px"><td style="border-right: 1px solid rgb(127, 127, 127); border-bottom: 1px solid rgb(127, 127, 127); border-left: 1px solid rgb(127, 127, 127); border-image: initial; border-top: none; padding: 5px 10px;" width="161" height="10">活動間的等待<br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="153" height="10"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">程序<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="162" height="10"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">團隊<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="176" height="10"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">累活<br></span></p></td></tr><tr style=";height:10px"><td style="border-right: 1px solid rgb(127, 127, 127); border-bottom: 1px solid rgb(127, 127, 127); border-left: 1px solid rgb(127, 127, 127); border-image: initial; border-top: none; padding: 5px 10px;" width="161" height="10"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">反復的加工<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="153" height="10"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">溝通<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="162" height="10"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">顧客（流程上游）</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="176" height="10"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">乏味的活</span></p></td></tr><tr style=";height:9px"><td style="border-right: 1px solid rgb(127, 127, 127); border-bottom: 1px solid rgb(127, 127, 127); border-left: 1px solid rgb(127, 127, 127); border-image: initial; border-top: none; padding: 5px 10px;" width="161" height="9"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">缺陷、失誤</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="153" height="9"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">物流<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="162" height="9"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">供應商（流程下游方）</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="176" height="9"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">數據採集<br></span></p></td></tr><tr style=";height:9px"><td style="border-right: 1px solid rgb(127, 127, 127); border-bottom: 1px solid rgb(127, 127, 127); border-left: 1px solid rgb(127, 127, 127); border-image: initial; border-top: none; padding: 5px 10px;" width="161" height="9"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">活動重複<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="153" height="9"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="162" height="9"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="176" height="9"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">數據分析<br></span></p></td></tr><tr style=";height:0"><td style="border-right: 1px solid rgb(127, 127, 127); border-bottom: 1px solid rgb(127, 127, 127); border-left: 1px solid rgb(127, 127, 127); border-image: initial; border-top: none; padding: 5px 10px;" width="161" height="0"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">跨部門的協調</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="153" height="0"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="162" height="0"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="176" height="0"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">數據傳輸<br></span></p></td></tr></tbody></table><p><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">5、流程圖的繪製</span></p><p><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><img src="/upload/question/20200520/1589961751671609.png" alt="image.png"></span></p><p><br></p><p><img src="/upload/question/20200520/1589961918536342.png" alt="image.png"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p><br></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">6、客服事務處理流程列表</span></p><p><br></p><table cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; background: rgb(172, 185, 202) none repeat scroll 0% 0%; padding: 0px 7px; word-break: break-all;" width="83" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">序號</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: initial; background: rgb(172, 185, 202) none repeat scroll 0% 0%; padding: 0px 7px; word-break: break-all;" width="100" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">事務分類</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: initial; background: rgb(172, 185, 202) none repeat scroll 0% 0%; padding: 0px 7px; word-break: break-all;" width="274" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">流程名稱</span></p></td></tr><tr><td rowspan="3" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="83"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1</span></p></td><td rowspan="3" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="100"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">電話類</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="274" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">電話接聽處理流程</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="274" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">回撥處理流程</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="274" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">個人資料核實流程</span></p></td></tr><tr><td rowspan="2" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="83"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2</span></p></td><td rowspan="2" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="100"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">審核類</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="274" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">審核處理流程</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="274" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">審核待處理流程</span></p></td></tr><tr><td rowspan="3" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="83"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3</span></p></td><td rowspan="3" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="100"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">賬號規則類</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="274" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">違規處理流程</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="274" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">復權處理流程</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="274" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">修改賬號個人資料處理流程</span></p></td></tr><tr><td rowspan="3" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="83"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">4</span></p></td><td rowspan="3" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="100"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">現場問題<br></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="274" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">BUG處理流程</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="274" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">新功能新通知處理流程</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="274" valign="top"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">退點退現處理流程</span></p></td></tr></tbody></table><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">田霞</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-05-20 16:27:47</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px;">六、知識管理</span></strong></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif">1</span><span style="font-size:16px;font-family:PMingLiU">、知識管理的概念</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU">企業在內部推行知識管理，旨在將散亂的知進行整合，通過資訊技術將資訊與員工的認知能力充分結合，創造知識共用的文化，激勵員工參與知識共用機制，從而促進員工學習、運用知識，以達到知識創新。知識管理既著眼於顯性知識，更著眼於隱形知識，為企業實現顯性知識與隱形知識的轉化與共用創造了全新的有效途徑。</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">顯性知識：傳統書面檔、電子檔案</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">隱性知識：如經驗、技術、習慣等</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:宋体">&nbsp;</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style=";font-family:'微软雅黑',sans-serif">2</span><span style=";font-family: PMingLiU">、</span><span style="font-size:16px;font-family:PMingLiU">知識的類型與來源</span></p><table width="595" cellspacing="0" cellpadding="0"><tbody><tr style=";height:31px" class="firstRow"><td style="border: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="123" height="31"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">知識的類型</span></p></td><td style="border-top: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); border-bottom: 1px solid rgb(127, 127, 127); border-image: initial; border-left: none; padding: 5px 10px;" width="471" height="31"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">主要來源<br></span></p></td></tr><tr style=";height:31px"><td style="border-right: 1px solid rgb(127, 127, 127); border-bottom: 1px solid rgb(127, 127, 127); border-left: 1px solid rgb(127, 127, 127); border-image: initial; border-top: none; padding: 5px 10px;" width="123" height="31"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">行業知識</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="471" height="31"><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">公司門戶網站、行業協會文章、行業研究報告</span></p></td></tr><tr style=";height:31px"><td style="border-right: 1px solid rgb(127, 127, 127); border-bottom: 1px solid rgb(127, 127, 127); border-left: 1px solid rgb(127, 127, 127); border-image: initial; border-top: none; padding: 5px 10px;" width="123" height="31"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">公司知識<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="471" height="31"><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">公司歷史與檔案、員工手冊、高管訪談</span></p></td></tr><tr style=";height:31px"><td style="border-right: 1px solid rgb(127, 127, 127); border-bottom: 1px solid rgb(127, 127, 127); border-left: 1px solid rgb(127, 127, 127); border-image: initial; border-top: none; padding: 5px 10px;" width="123" height="31"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">業務知識<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="471" height="31"><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">部門負責人或分管副總訪談、行業規範、外部客戶訪談</span></p></td></tr><tr style=";height:31px"><td style="border-right: 1px solid rgb(127, 127, 127); border-bottom: 1px solid rgb(127, 127, 127); border-left: 1px solid rgb(127, 127, 127); border-image: initial; border-top: none; padding: 5px 10px;" width="123" height="31"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">崗位知識<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="471" height="31"><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">部門負責人或直線上級、職位說明書、內外部客戶訪談</span></p></td></tr><tr style=";height:31px"><td style="border-right: 1px solid rgb(127, 127, 127); border-bottom: 1px solid rgb(127, 127, 127); border-left: 1px solid rgb(127, 127, 127); border-image: initial; border-top: none; padding: 5px 10px;" width="123" height="31"><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">崗位技能<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid rgb(127, 127, 127); border-right: 1px solid rgb(127, 127, 127); padding: 5px 10px;" width="471" height="31"><p style="margin-top:auto;margin-bottom: auto;text-align:left;text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">部門負責人或直線上級、內外部客戶訪談、標杆員工行為觀察</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">/</span><span style="font-size:16px;font-family:PMingLiU;color:black">訪談</span></p></td></tr></tbody></table><p><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p><span style="font-family:'微软雅黑',sans-serif">3</span><span style="font-family: PMingLiU">、知識管理的意義</span></p><p><span style="font-size:14px;font-family:'微软雅黑',sans-serif">1</span><span style="font-size:14px;font-family:PMingLiU">）</span><span style="font-family:PMingLiU">將優秀的員工經驗，轉換為企業內部資源，避免人員流失，將知識帶走</span></p><p><span style="font-family:'微软雅黑',sans-serif">2</span><span style="font-family: PMingLiU">）工作中遇到的問題，無法自助找到解決方案</span></p><p><span style="font-family:'微软雅黑',sans-serif">3</span><span style="font-family: PMingLiU">）通過共用知識，提高員工的知識與技能，增加員工競爭力，使企業更長遠的發展</span></p><p>&nbsp;</p><p><span style="font-family:'微软雅黑',sans-serif">4</span><span style="font-family: PMingLiU">、知識管理的方式</span></p><p><span style="font-size:14px;font-family:'微软雅黑',sans-serif">1</span><span style="font-size:14px;font-family:PMingLiU">）</span><span style="font-family:PMingLiU">建立知識管理團隊和工作機制，構建知識分類體系</span></p><p style="margin-left:48px"><span style="font-size: 13px;font-family:Symbol">·<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-family:PMingLiU">建立知識管理團隊，並分工</span></p><p style="margin-left:48px"><span style="font-size: 13px;font-family:Symbol">·<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-family:PMingLiU">構建知識管理藍圖</span></p><p><span style="font-family:'微软雅黑',sans-serif">2</span><span style="font-family: PMingLiU">）建立確保知識管理平臺高效運轉的制度和運作分享機制</span></p><p style="margin-left:48px"><span style="font-size: 13px;font-family:Symbol">·<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-family:PMingLiU">強化制度建設，建立知識管理長效機制</span></p><p style="margin-left:48px"><span style="font-size: 13px;font-family:Symbol">·<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-family:PMingLiU">從具體業務入手，注重知識管理與業務工作的深度融合</span></p><p style="margin-left:48px;text-autospace:none"><span style="font-size:13px;font-family:Symbol">·<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-family:PMingLiU;color:black">通過企業文化和激勵措施等使知識管理更高效</span></p><p style="text-autospace:none"><span style="font-family:'微软雅黑',sans-serif;color:black">3</span><span style="font-family:PMingLiU;color:black">）</span><span style="font-family:PMingLiU">廣泛應用知識庫、公司文檔等一系列資訊化技術</span></p><p style="margin-left:48px;text-autospace:none"><span style="font-size:13px;font-family:Symbol">·<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-family:PMingLiU">知識庫實現知識管理有效控制與高效運行</span></p><p style="margin-left:48px;text-autospace:none"><span style="font-size:13px;font-family:Symbol">·<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-family:PMingLiU">實現知識管理在統一的平臺中運行</span></p><p style="margin-left:48px;text-autospace:none"><span style="font-size:13px;font-family:Symbol">·<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-family:PMingLiU;color:black">強化許可權設置，確保知識安全性</span></p><p style="text-autospace:none"><span style="font-family:'微软雅黑',sans-serif;color:black">4</span><span style="font-family:PMingLiU;color:black">）</span><span style="font-family:PMingLiU">培育形成知識共用文化的長效機制</span></p><p style="margin-left:48px;text-autospace:none"><span style="font-size:13px;font-family:Symbol">·<span style="font:9px 'Times New Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-family:PMingLiU">內部管理標準化、規範化與有效化</span></p><p>&nbsp;</p><p></p><ul><li><p style="text-align:left;text-autospace:none"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p></li><br></ul><p></p><p style="text-align:left;text-autospace:none"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="text-align:left;text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">田霞</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-05-20 17:42:33</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px;">七、目標管理</span></strong></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif">1</span><span style="font-size:16px;font-family:PMingLiU">、什麼是目標管理</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU">目標管理是管理者通過激勵機制的作用，把企業組織或管理者的目標，轉化成被管理者的目標，以實現由自我控制達成整體協調控制的一種管理技術。</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:宋体">&nbsp;</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif">2</span><span style="font-size:16px;font-family:PMingLiU">、目標管理的流程</span></p><p><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><img src="/upload/question/20200520/1589967200930581.png" alt="image.png"></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3、</span>目標來源</span></p><p><br></p><table cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" width="102" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">願景導向</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: initial; padding: 0px 7px; word-break: break-all;" width="567" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">公司</span><span style="font-size: 16px;font-family:'微软雅黑',sans-serif;color:black">/</span><span style="font-size:16px;font-family:PMingLiU;color:black">部門的目標，對組織未來的願景</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="102" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">問題導向<br></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="567" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">特定問題的改善</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="102" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">成長導向<br></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="567" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">個人</span><span style="font-size: 16px;font-family:'微软雅黑',sans-serif;color:black">/</span><span style="font-size:16px;font-family:PMingLiU;color:black">團隊成員的成長與提升</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="102" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">競爭導向<br></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="567" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">針對競爭對手的反應和計畫</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="102" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">顧客導向<br></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="567" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">外部客戶滿意度，內部跨部門的專案協作</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="102" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">責任導向<br></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="567" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">職責範圍內的事務</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="102" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">財務（成本）</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="567" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">在創利增收、減少成本方面為公司做了什麼貢獻</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="102" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">客戶<br></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="567" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">客戶</span><span style="font-size: 16px;font-family:'微软雅黑',sans-serif;color:black">/</span><span style="font-size:16px;font-family:PMingLiU;color:black">內部客戶如何看待我們的工作成果</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="102" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">內部運營</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="567" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">我們夠快了嗎？夠好了嗎？我們適應了市場要求嗎？</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="102" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">學習與發展</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="567" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">如何提升我們團隊的素質和能力？</span></p></td></tr></tbody></table><table width="664" cellspacing="0" cellpadding="0"><tbody><tr style=";height:38px" class="firstRow"><td style="border: 1px solid black; background: white; padding: 5px 10px;" width="103" height="38"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">目標來源</span></p></td><td style="border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black; border-image: initial; border-left: none; background: white; padding: 5px 10px;" width="88" height="38"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">願景導向</span></p></td><td style="border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black; border-image: initial; border-left: none; background: white; padding: 5px 10px;" width="95" height="38"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">問題導向</span></span></p></td><td style="border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black; border-image: initial; border-left: none; background: white; padding: 5px 10px;" width="95" height="38"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">成長導向</span></span></p></td><td style="border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black; border-image: initial; border-left: none; background: white; padding: 5px 10px;" width="95" height="38"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">競爭導向</span></span></p></td><td style="border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black; border-image: initial; border-left: none; background: white; padding: 5px 10px;" width="95" height="38"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">顧客導向</span></span></p></td><td style="border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black; border-image: initial; border-left: none; background: white; padding: 5px 10px;" width="95" height="38"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">責任導向</span></span></p></td></tr><tr style=";height:38px"><td style="border-right: 1px solid black; border-bottom: 1px solid black; border-left: 1px solid black; border-image: initial; border-top: none; background: white; padding: 5px 10px;" width="103" height="38"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">財務<br></span></p><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">（成本）</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="88" height="38"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="95" height="38"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="95" height="38"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="95" height="38"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="95" height="38"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="95" height="38"><br></td></tr><tr style=";height:38px"><td style="border-right: 1px solid black; border-bottom: 1px solid black; border-left: 1px solid black; border-image: initial; border-top: none; background: white; padding: 5px 10px;" width="103" height="38"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">客戶<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="88" height="38"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="95" height="38"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="95" height="38"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="95" height="38"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="95" height="38"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="95" height="38"><br></td></tr><tr style=";height:38px"><td style="border-right: 1px solid black; border-bottom: 1px solid black; border-left: 1px solid black; border-image: initial; border-top: none; background: white; padding: 5px 10px;" width="103" height="38"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">內部運營<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="88" height="38"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="95" height="38"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="95" height="38"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="95" height="38"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="95" height="38"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="95" height="38"><br></td></tr><tr style=";height:37px"><td style="border-right: 1px solid black; border-bottom: 1px solid black; border-left: 1px solid black; border-image: initial; border-top: none; background: white; padding: 5px 10px;" width="103" height="37"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">學習與發展<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="88" height="37"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="95" height="37"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="95" height="37"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="95" height="37"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="95" height="37"><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="95" height="37"><br></td></tr></tbody></table><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif">4</span><span style="font-size:16px;font-family:PMingLiU">、目標設定</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif">1</span><span style="font-size:16px;font-family:PMingLiU">）</span><span style="font-size:9px;font-family:'微软雅黑',sans-serif">&nbsp;&nbsp;</span><span style="font-size:16px;font-family: PMingLiU">目標設定的原則：</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif">Smart</span><span style="font-size:16px;font-family:PMingLiU">原則</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif">S</span><span style="font-size:16px;font-family:PMingLiU">：明確具體的（降低投訴率）</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">M</span><span style="font-size:16px;font-family:PMingLiU;color:black">：可衡量的（每週溝通一次）</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">A</span><span style="font-size:16px;font-family:PMingLiU;color:black">：可達到的（重要節日發簡訊問候）</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">R</span><span style="font-size:16px;font-family:PMingLiU;color:black">：相關的（每天流覽一次客戶網站）</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">T</span><span style="font-size:16px;font-family:PMingLiU;color:black">：基於時間的（每月</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">25</span><span style="font-size:16px;font-family:PMingLiU;color:black">號完成客戶資訊更新）</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">2)</span><span style="font-size:9px;font-family:'微软雅黑',sans-serif">&nbsp;&nbsp;</span><span style="font-size:16px;font-family:PMingLiU">目標設定的步驟</span></p><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><table cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" width="92" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">調查研究</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: initial; padding: 0px 7px; word-break: break-all;" width="576" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p><span style="font-family:PMingLiU;color:black">運用</span><span style="font-family:'微软雅黑',sans-serif;color:black">SWOT</span><span style="font-family:PMingLiU;color:black">分析法，針對目標設立的來源於維度進行分析，為確定戰略目標奠定可靠基礎。</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="92" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">擬定目標<br></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="576" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">依據內外部環境、需要和資源的綜合考慮，確定目標方向；</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">通過對現有能力與手段等條件的全面衡量，對沿著戰略方向展開的活動所要達到的水準做出初步的預測，便形成了可供決策選擇的目標方案。</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">1</span><span style="font-size:16px;font-family:PMingLiU;color:black">、列出各個目標的綜合排列的次序</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">2</span><span style="font-size:16px;font-family:PMingLiU;color:black">、盡可能減少目標個數：①類似的目標合併成一個，②從屬目標歸入總目標</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="92" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">評價論證</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="576" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">組織有關人員評價和論證目標方案：</span></p><p class="MsoListParagraph" style="margin-left:80px;text-autospace:none"><span style="font-family:'微软雅黑',sans-serif;color:black">①<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:9px;font-family:'微软雅黑',sans-serif;color:black">&nbsp; </span><span style="font-family: PMingLiU;color:black">論證正確性、②論證可行性、③評價完善化程度</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="92" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">目標</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="576" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">從三方面權衡各個目標方案：</span></p><p class="MsoListParagraph" style="margin-left:48px;text-autospace:none"><span style="font-family:'微软雅黑',sans-serif;color:black">①<span style="font:9px 'Times New Roman'">&nbsp;&nbsp; </span></span><span style="font-size:9px;font-family:'微软雅黑',sans-serif;color:black">&nbsp; </span><span style="font-family: PMingLiU;color:black">目標方向的正確程度、②實現程度的大小、③期望效益的大小</span></p></td></tr></tbody></table><p><br></p><table width="665" cellspacing="0" cellpadding="0"><tbody><tr style=";height:2px" class="firstRow"><td style="border: 1px solid black; background: white; padding: 5px 10px;" width="39" height="2"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">設立依據</span></p></td><td style="border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black; border-image: initial; border-left: none; background: white; padding: 5px 10px;" width="92" height="2"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">目標項目</span></p></td><td style="border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black; border-image: initial; border-left: none; background: white; padding: 5px 10px;" width="123" height="2"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">具體實施<br></span></p></td><td style="border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black; border-image: initial; border-left: none; background: white; padding: 5px 10px;" width="142" height="2"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">衡量標準</span></p></td><td style="border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black; border-image: initial; border-left: none; background: white; padding: 5px 10px;" width="50" height="2"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">必備資源</span></p></td><td style="border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black; border-image: initial; border-left: none; background: white; padding: 5px 10px;" width="25" height="2"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">權重比例</span></p></td><td style="border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black; border-image: initial; border-left: none; background: white; padding: 0px;" width="41" valign="top" height="2"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">負責人員<br></span></p><br></td><td style="border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black; border-image: initial; border-left: none; background: white; padding: 0px;" width="82" valign="top" height="2"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">完成時間</span></p></td><td style="border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black; border-image: initial; border-left: none; background: white; padding: 0px;" width="32" valign="top" height="2"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">備註<br></span></p><br></td></tr><tr style=";height:3px"><td style="border-right: 1px solid black; border-bottom: 1px solid black; border-left: 1px solid black; border-image: initial; border-top: none; background: white; padding: 5px 10px;" width="39" height="3"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">職責導向<br></span></p><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="92" height="3"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:12px;font-family:PMingLiU;color:black">（定性）</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:12px;font-family:PMingLiU;color:black">建立客服部培訓體系</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="123" height="3"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:12px;font-family:'微软雅黑',sans-serif;color:black">1</span><span style="font-size:12px;font-family:PMingLiU;color:black">、管理崗位由任職者整理</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:12px;font-family:'微软雅黑',sans-serif;color:black">2</span><span style="font-size:12px;font-family:PMingLiU;color:black">、星級客服由督導負責整理</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="142" height="3"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:12px;font-family:'微软雅黑',sans-serif;color:black">1</span><span style="font-size:12px;font-family:PMingLiU;color:black">、所有崗位需有培訓計畫表與培訓資料</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:12px;font-family:'微软雅黑',sans-serif;color:black">2</span><span style="font-size:12px;font-family:PMingLiU;color:black">、所有崗位有清晰的崗位說明書與作業指導書</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="50" height="3"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">學習資料</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="25" height="3"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">60%</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 0px;" width="41" valign="top" height="3"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">P：xxx</span></p><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 0px;" width="82" valign="top" height="3"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2018/06/30</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 0px;" width="32" valign="top" height="3"><br></td></tr><tr style=";height:3px"><td style="border-right: 1px solid black; border-bottom: 1px solid black; border-left: 1px solid black; border-image: initial; border-top: none; background: white; padding: 5px 10px;" width="39" height="3"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">問題<br></span></p><br></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="92" height="3"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:12px;font-family:PMingLiU;color:black">（定量）</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:12px;font-family:PMingLiU;color:black">會員不滿意下降</span><span style="font-size:12px;font-family:'微软雅黑',sans-serif;color:black">20%</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="123" height="3"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:12px;font-family:'微软雅黑',sans-serif;color:black">1</span><span style="font-size:12px;font-family:PMingLiU;color:black">、分析不滿意原因</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:12px;font-family:'微软雅黑',sans-serif;color:black">2</span><span style="font-size:12px;font-family:PMingLiU;color:black">、將目標分解到每位元客服</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:12px;font-family:'微软雅黑',sans-serif;color:black">3</span><span style="font-size:12px;font-family:PMingLiU;color:black">、制定提升服務品質的各項措施…</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="142" height="3"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:12px;font-family:'微软雅黑',sans-serif;color:black">1.</span><span style="font-size:12px;font-family:PMingLiU;color:black">站外投訴下降</span><span style="font-size:12px;font-family:'微软雅黑',sans-serif;color:black">20%</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:12px;font-family:'微软雅黑',sans-serif;color:black">2.</span><span style="font-size:12px;font-family:PMingLiU;color:black">舊版不滿意評分下降</span><span style="font-size:12px;font-family:'微软雅黑',sans-serif;color:black">20%</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:12px;font-family:'微软雅黑',sans-serif;color:black">3.</span><span style="font-size:12px;font-family:PMingLiU;color:black">電話評分下降</span><span style="font-size:12px;font-family:'微软雅黑',sans-serif;color:black">20%</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="50" height="3"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">人力<br></span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="25" height="3"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">20%</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 0px;" width="41" valign="top" height="3"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">P：xx</span></p><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">S：xx</span></p><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 0px;" width="82" valign="top" height="3"><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2018/12/30</span></p><p style=";text-autospace:none"><span style="font-size: 12px; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 0px;" width="32" valign="top" height="3"><br></td></tr></tbody></table><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif">3)</span><span style="font-size:16px;font-family:PMingLiU">目標設定的要點</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU">具有挑戰性的高績效目標又只要盡力就能達到</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU">有最終目標，又有階段性目標</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU">目標需具體及數量化（定性或定量）</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU">避免目標太多或太少</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU">與上級的目標產生關聯，具有一貫性</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU">經縱向及橫向充分溝通，所決定的目標</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU">經歷史資料分析，未來產業變化趨勢評估，同業競爭威脅影響評估，組織內部之長處與弱點分析，形成現狀與理想目標的差距原因分析，考慮組織可以運用資源，然後設定的目標。</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:宋体">&nbsp;</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif">5</span><span style="font-size:16px;font-family:PMingLiU">、目標分解</span></p><p><br></p><table width="670" cellspacing="0" cellpadding="0"><tbody><tr style=";height:34px" class="firstRow"><td rowspan="4" style="border: 1px solid black; background: white; padding: 5px 10px;" width="93" height="34"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">WBS</span></p><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">分解法</span></p></td><td style="border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black; border-image: initial; border-left: none; background: white; padding: 5px 10px;" width="197" height="34"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">層次結構<br></span></p></td><td style="border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black; border-image: initial; border-left: none; background: white; padding: 5px 10px;" width="169" height="34"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">工作編碼<br></span></p></td><td style="border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black; border-image: initial; border-left: none; background: white; padding: 5px 10px;" width="210" height="34"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">展示方式<br></span></p></td></tr><tr style=";height:95px"><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="197" height="95"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">項目</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">子項目</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">任務</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">子任務</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">工作包</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">有效活動</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="169" height="95"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1.0</span></p><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">※1.1</span></p><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">•1.1.1</span></p><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">•1.1.2</span></p><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">-1.1.2.1</span></p><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">※1.2</span></p><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">•1.2.1</span></p><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">-1.2.1.1</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="210" height="95"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">1</span><span style="font-size:16px;font-family:PMingLiU;color:black">、組織架構型</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">2</span><span style="font-size:16px;font-family:PMingLiU;color:black">、任務清單型</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">3</span><span style="font-size:16px;font-family:PMingLiU;color:black">、兩者混合型</span></p></td></tr><tr style=";height:79px"><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="197" height="79"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">注意點：</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">1</span><span style="font-size:16px;font-family:PMingLiU;color:black">、向自上而下逐層細化分解</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">2</span><span style="font-size:16px;font-family:PMingLiU;color:black">、以專案過程階段（上）</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">+</span><span style="font-size:16px;font-family:PMingLiU;color:black">可交付成果為導向（下）</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">3</span><span style="font-size:16px;font-family:PMingLiU;color:black">、有效活動是能快速完成的工作，邏輯上不能再細分</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="169" height="79"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">注意點：</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">1</span><span style="font-size:16px;font-family:PMingLiU;color:black">、一般劃分為</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">3~5</span><span style="font-size:16px;font-family:PMingLiU;color:black">級，超過則劃分成子項目任務</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">2</span><span style="font-size:16px;font-family:PMingLiU;color:black">、需要</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">100%</span><span style="font-size:16px;font-family:PMingLiU;color:black">覆蓋各專案任務</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">3</span><span style="font-size:16px;font-family:PMingLiU;color:black">、每個工作包約為</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">2</span><span style="font-size:16px;font-family:PMingLiU;color:black">周時間</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="210" height="79"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">注意點：</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">1</span><span style="font-size:16px;font-family:PMingLiU;color:black">、總體概括及概覽採用組織架構型</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">2</span><span style="font-size:16px;font-family:PMingLiU;color:black">、事務跟蹤及監控採用任務清單型，並結合甘特圖</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">3</span><span style="font-size:16px;font-family:PMingLiU;color:black">、兩者混合型不建議常用</span></p></td></tr><tr style=";height:77px"><td colspan="3" style="border-top: none; border-left: none; border-bottom: 1px solid black; border-right: 1px solid black; background: white; padding: 5px 10px;" width="577" height="77"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">關注點：</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">1</span><span style="font-size:16px;font-family:PMingLiU;color:black">、通常“專案管理”本身作為</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">WBS</span><span style="font-size:16px;font-family:PMingLiU;color:black">中第二層的組成部分</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">2</span><span style="font-size:16px;font-family:PMingLiU;color:black">、</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">WBS</span><span style="font-size:16px;font-family:PMingLiU;color:black">編制與確定需要團隊成員共同參與，以提高團隊成員的認同感與歸屬感</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">3</span><span style="font-size:16px;font-family:PMingLiU;color:black">、</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">WBS</span><span style="font-size:16px;font-family:PMingLiU;color:black">確定後還需要編制“</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">WBS</span><span style="font-size:16px;font-family:PMingLiU;color:black">詞典”（工作包層級），包括任務描述表、工作責任分配矩陣等，以明確具體工作與責任歸屬</span></p></td></tr></tbody></table><p><br></p><p><br><img src="/upload/question/20200520/1589967410918915.png" alt="image.png"></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">6、制定計劃</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1）步驟</span></p><p><br></p><table cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" width="121" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">明確目標</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: initial; padding: 0px 7px; word-break: break-all;" width="548" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">用多少時間，多少成本，辦成什麼事，衡量事的結果是什麼。</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="121" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">制定方案<br></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="548" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">調查研究，收集資訊，徵求意見，民主討論，拍板決定</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="121" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">工作分解<br></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="548" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">將方案分解為可管理的任務，周密完整，不僅包括工作本身，還包括工作管理。</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="121" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">任務分析<br></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="548" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">分析評估任務的</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">5W3H</span><span style="font-size:16px;font-family:PMingLiU;color:black">要素。</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="121" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">分工授權<br></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="548" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">將任務以恰當的方式下達給合適的責任人，並適當授權。</span></p></td></tr></tbody></table><p><br></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2）方法：5W3H</span></p><p><br></p><table cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" width="168" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">What&nbsp; &nbsp; 工作任務</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: initial; padding: 0px 7px; word-break: break-all;" width="501" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">包括工作內容與工作量及工作要求與目標</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="168" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">Where&nbsp; 工作切入點</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="501" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">從哪裡開始入手，按什麼路徑（程式步驟）開展下去，到哪裡終止</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="168" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">When&nbsp; &nbsp; 工作進程</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="501" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">工作程式步驟對應的工作日程與安排（包括所用時間預算）</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="168" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">Who&nbsp; &nbsp; 組織分工<br></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="501" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">這件事由誰或哪些人去做，他們分別承擔什麼工作任務。</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="168" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">Why&nbsp; &nbsp; 做事的目的</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="501" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">這件事情是否有必要去做，或做這件事情的目的、意圖、方向是什麼。</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="168" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">How&nbsp; &nbsp; 方法工具<br></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="501" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p><span style="font-family:PMingLiU;color:black">完成工作所需用到的工具方法及關鍵環節策劃佈置（工作方案的核心）</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="168" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">How much&nbsp; 工作資源<br></span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="501" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">完成工作需哪些資源與條件，分別需要多少。如人、財、物、時間、資訊、技術等資源，及權力、政策、機制等條件的配合。</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="168" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">How do you feel&nbsp; 工作結果</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="501" valign="top">工作結果預測</td></tr></tbody></table><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">7、執行控制</span></p><table cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" width="130" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">管理死循環</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: initial; padding: 0px 7px; word-break: break-all;" width="538" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">計畫、分工、準備、指導、檢查、協調、攻關、考核</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="130" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">九大控制法</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="538" valign="top"><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">限制選擇法、橫向控制法、三要素法、分段控制法、資料流程動法、稽核控制法、案例分析法、全員主角法、持續種因法</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="130" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">分段控制法</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="538" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">責任分段、過程分段、時間分段、控制動作</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="130" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">三要素控制法</span></p><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="538" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">標準：解決問題的辦法：怎麼做，做到什麼程度</span> </p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">制約：誰去跟蹤，檢查，督促</span> </p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">責任：未按要求做：如何懲罰，表現優異：如何獎勵（獎勵項未必需要規定）</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="130" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">稽核控制法</span></p><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="538" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">稽核控制法是以員工肯定要違反的模式規定為前提的</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">!</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">在設計模式之初，就要做好反復</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">10</span><span style="font-size:16px;font-family:PMingLiU;color:black">次的準備</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">!</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">建立好標準後要搞制約，實行獎懲</span><span style="font-size:16px;font-family:'微软雅黑',sans-serif;color:black">!</span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">違反了標準就得追究責任和承擔責任！</span></p><p style=";text-autospace:none"><span style="font-size:16px;font-family:PMingLiU;color:black">稽核真正的目的是説明每一個人養成良好的習慣，是“防微杜漸</span><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">”</span><br></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: initial; padding: 0px 7px; word-break: break-all;" width="130" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">結果數據考核法</span></p><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px; word-break: break-all;" width="538" valign="top"><p style=";text-autospace:none"><span style="color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="margin-top:auto;margin-bottom: auto;text-align:left"><span style="font-size:16px;font-family:PMingLiU;color:black">針對具體問題、抓住結果事項、設定資料目標、抓好資料管理、考核責任到人、及時有效獎懲</span></p></td></tr></tbody></table><p><br></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p></div></div> <div><span class="button">我要編輯</span></div></li></ul> <h3 class="more-answer">我要補充</h3> <div style=""><div id="editor_21641428096500136" class="edui-default" style=""><div id="edui1" class="edui-editor  edui-default" style="width: 100%; z-index: 999;"><div id="edui1_toolbarbox" class="edui-editor-toolbarbox edui-default"><div id="edui1_toolbarboxouter" class="edui-editor-toolbarboxouter edui-default"><div class="edui-editor-toolbarboxinner edui-default"><div id="edui2" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui2&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui3" class="edui-box edui-button edui-for-fullscreen edui-default"><div id="edui3_state" onmousedown="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui3_body" unselectable="on" title="全屏" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui3&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui3&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui4" class="edui-box edui-button edui-for-source edui-default"><div id="edui4_state" onmousedown="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui4_body" unselectable="on" title="源代码" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui4&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui4&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui5" class="edui-box edui-button edui-for-undo edui-default"><div id="edui5_state" onmousedown="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui5_body" unselectable="on" title="撤销" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui5&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui5&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui6" class="edui-box edui-button edui-for-redo edui-default"><div id="edui6_state" onmousedown="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui6_body" unselectable="on" title="重做" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui6&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui6&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui7" class="edui-box edui-button edui-for-simpleupload edui-default"><div id="edui7_state" onmousedown="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui7_body" unselectable="on" title="单图上传" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui7&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui7&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui8" class="edui-box edui-button edui-for-imageleft edui-default"><div id="edui8_state" onmousedown="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui8_body" unselectable="on" title="左浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui8&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui8&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui9" class="edui-box edui-button edui-for-imageright edui-default"><div id="edui9_state" onmousedown="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui9_body" unselectable="on" title="右浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui9&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui9&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui10" class="edui-box edui-button edui-for-imagecenter edui-default"><div id="edui10_state" onmousedown="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui10_body" unselectable="on" title="居中" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui10&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui10&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui11" class="edui-box edui-separator  edui-default"></div><div id="edui12" class="edui-box edui-button edui-for-date edui-default"><div id="edui12_state" onmousedown="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui12_body" unselectable="on" title="日期" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui12&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui12&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui13" class="edui-box edui-button edui-for-time edui-default"><div id="edui13_state" onmousedown="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui13_body" unselectable="on" title="时间" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui13&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui13&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui14" class="edui-box edui-button edui-for-horizontal edui-default"><div id="edui14_state" onmousedown="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui14_body" unselectable="on" title="分隔线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui14&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui14&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui15" class="edui-box edui-separator  edui-default"></div><div id="edui16" class="edui-box edui-splitbutton edui-for-inserttable edui-default"><div title="插入表格" id="edui16_state" onmousedown="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui16_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onArrowClick();"></div></div></div></div><div id="edui19" class="edui-box edui-button edui-for-deletetable edui-default"><div id="edui19_state" onmousedown="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui19_body" unselectable="on" title="删除表格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui19&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui19&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui20" class="edui-box edui-button edui-for-insertrow edui-default"><div id="edui20_state" onmousedown="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui20_body" unselectable="on" title="前插入行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui20&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui20&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui21" class="edui-box edui-button edui-for-insertcol edui-default"><div id="edui21_state" onmousedown="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui21_body" unselectable="on" title="前插入列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui21&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui21&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui22" class="edui-box edui-button edui-for-mergeright edui-default"><div id="edui22_state" onmousedown="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui22_body" unselectable="on" title="右合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui22&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui22&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui23" class="edui-box edui-button edui-for-mergedown edui-default"><div id="edui23_state" onmousedown="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui23_body" unselectable="on" title="下合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui23&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui23&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui24" class="edui-box edui-button edui-for-deleterow edui-default"><div id="edui24_state" onmousedown="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui24_body" unselectable="on" title="删除行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui24&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui24&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui25" class="edui-box edui-button edui-for-deletecol edui-default"><div id="edui25_state" onmousedown="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui25_body" unselectable="on" title="删除列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui25&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui25&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui26" class="edui-box edui-button edui-for-splittorows edui-default"><div id="edui26_state" onmousedown="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui26_body" unselectable="on" title="拆分成行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui26&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui26&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui27" class="edui-box edui-button edui-for-splittocols edui-default"><div id="edui27_state" onmousedown="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui27_body" unselectable="on" title="拆分成列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui27&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui27&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui28" class="edui-box edui-button edui-for-splittocells edui-default"><div id="edui28_state" onmousedown="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui28_body" unselectable="on" title="完全拆分单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui28&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui28&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui29" class="edui-box edui-button edui-for-mergecells edui-default"><div id="edui29_state" onmousedown="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui29_body" unselectable="on" title="合并多个单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui29&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui29&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui36" class="edui-box edui-button edui-for-edittable edui-default"><div id="edui36_state" onmousedown="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui36_body" unselectable="on" title="表格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui36&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui36&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui41" class="edui-box edui-button edui-for-edittd edui-default"><div id="edui41_state" onmousedown="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui41_body" unselectable="on" title="单元格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui41&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui41&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui42" class="edui-box edui-separator  edui-default"></div><div id="edui43" class="edui-box edui-menubutton edui-for-lineheight edui-default"><div title="行间距" id="edui43_state" onmousedown="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui43_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onArrowClick();"></div></div></div></div><div id="edui52" class="edui-box edui-menubutton edui-for-rowspacingtop edui-default"><div title="段前距" id="edui52_state" onmousedown="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui52_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onArrowClick();"></div></div></div></div><div id="edui59" class="edui-box edui-menubutton edui-for-rowspacingbottom edui-default"><div title="段后距" id="edui59_state" onmousedown="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui59_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onArrowClick();"></div></div></div></div></div><div id="edui66" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui66&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui67" class="edui-box edui-combox edui-for-fontfamily edui-default"><div title="字体" id="edui67_state" onmousedown="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui67_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onButtonClick(event, this);">字体</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onArrowClick();"></div></div></div></div><div id="edui80" class="edui-box edui-combox edui-for-customstyle edui-default"><div title="自定义标题" id="edui80_state" onmousedown="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui80_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onButtonClick(event, this);">自定义标题</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onArrowClick();"></div></div></div></div><div id="edui86" class="edui-box edui-combox edui-for-paragraph edui-default"><div title="段落格式" id="edui86_state" onmousedown="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui86_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onButtonClick(event, this);">段落格式</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onArrowClick();"></div></div></div></div><div id="edui95" class="edui-box edui-combox edui-for-fontsize edui-default"><div title="字号" id="edui95_state" onmousedown="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui95_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onButtonClick(event, this);">字号</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onArrowClick();"></div></div></div></div><div id="edui110" class="edui-box edui-button edui-for-link edui-default"><div id="edui110_state" onmousedown="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui110_body" unselectable="on" title="超链接" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui110&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui110&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui111" class="edui-box edui-button edui-for-indent edui-default"><div id="edui111_state" onmousedown="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui111_body" unselectable="on" title="首行缩进" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui111&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui111&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui112" class="edui-box edui-button edui-for-bold edui-default"><div id="edui112_state" onmousedown="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui112_body" unselectable="on" title="加粗" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui112&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui112&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui113" class="edui-box edui-splitbutton edui-for-forecolor edui-default edui-colorbutton"><div title="字体颜色" id="edui113_state" onmousedown="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui113_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui113_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onArrowClick();"></div></div></div></div><div id="edui116" class="edui-box edui-splitbutton edui-for-backcolor edui-default edui-colorbutton"><div title="背景色" id="edui116_state" onmousedown="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui116_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui116_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onArrowClick();"></div></div></div></div><div id="edui119" class="edui-box edui-button edui-for-italic edui-default"><div id="edui119_state" onmousedown="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui119_body" unselectable="on" title="斜体" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui119&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui119&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui120" class="edui-box edui-button edui-for-underline edui-default"><div id="edui120_state" onmousedown="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui120_body" unselectable="on" title="下划线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui120&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui120&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui121" class="edui-box edui-button edui-for-strikethrough edui-default"><div id="edui121_state" onmousedown="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui121_body" unselectable="on" title="删除线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui121&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui121&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui122" class="edui-box edui-button edui-for-superscript edui-default"><div id="edui122_state" onmousedown="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui122_body" unselectable="on" title="上标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui122&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui122&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui123" class="edui-box edui-button edui-for-subscript edui-default"><div id="edui123_state" onmousedown="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui123_body" unselectable="on" title="下标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui123&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui123&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui124" class="edui-box edui-button edui-for-removeformat edui-default"><div id="edui124_state" onmousedown="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui124_body" unselectable="on" title="清除格式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui124&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui124&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui125" class="edui-box edui-button edui-for-blockquote edui-default"><div id="edui125_state" onmousedown="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui125_body" unselectable="on" title="引用" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui125&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui125&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui126" class="edui-box edui-button edui-for-pasteplain edui-default"><div id="edui126_state" onmousedown="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui126_body" unselectable="on" title="纯文本粘贴模式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui126&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui126&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui127" class="edui-box edui-menubutton edui-for-insertorderedlist edui-default"><div title="有序列表" id="edui127_state" onmousedown="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui127_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onArrowClick();"></div></div></div></div><div id="edui140" class="edui-box edui-menubutton edui-for-insertunorderedlist edui-default"><div title="无序列表" id="edui140_state" onmousedown="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui140_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onArrowClick();"></div></div></div></div><div id="edui147" class="edui-box edui-button edui-for-cleardoc edui-default"><div id="edui147_state" onmousedown="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui147_body" unselectable="on" title="清空文档" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui147&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui147&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div></div></div></div><div id="edui1_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;"><div id="edui1_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui1&quot;].showWordImageDialog();">点击上传</div><div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui1&quot;].hideToolbarMsg();">x</div><div id="edui1_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div><div style="height:0;overflow:hidden;clear:both;" class="edui-default"></div></div><div id="edui1_message_holder" class="edui-editor-messageholder edui-default"></div></div><div id="edui1_iframeholder" class="edui-editor-iframeholder edui-default" style="width: 100%; height: 300px; z-index: 999; overflow: hidden;"><iframe id="ueditor_0" width="100%" height="100%" frameborder="0" src="javascript:void(function(){document.open();document.write(&quot;<!DOCTYPE html><html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}
body{margin:8px;font-family:sans-serif;font-size:13px;}p{margin:5px 0;}</style><link rel='stylesheet' type='text/css' href='/static/ueditor/themes/iframe.css'/></head><body class='view' ></body><script type='text/javascript'  id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant0'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);</script></html>&quot;);document.close();}())"></iframe></div><div id="edui1_bottombar" class="edui-editor-bottomContainer edui-default"><table class="edui-default"><tbody class="edui-default"><tr class="edui-default"><td id="edui1_elementpath" class="edui-editor-bottombar edui-default"><div class="edui-editor-breadcrumb">元素路径:</div></td><td id="edui1_wordcount" class="edui-editor-wordcount edui-default">字数统计</td><td id="edui1_scale" class="edui-editor-scale edui-default" style="display: none;"><div class="edui-editor-icon edui-default"></div></td></tr></tbody></table></div><div id="edui1_scalelayer" class="edui-default"></div></div></div> <div class="edit-btn"><button type="button" class="button">提交回答</button> <label data-v-0fe26aab="" for="commonCheckbox" class="checkbox"><input data-v-0fe26aab="" type="checkbox" id="commonCheckbox" name="commonCheckbox"> <i data-v-0fe26aab=""></i> 需要彈窗，請勾選</label></div></div></div> <div class="index-right"><div class="right-bk"><h3 class="com-title"><a href="javascript:;">我的最爱</a></h3> <div class="item-box clearfix"><span>暫無數據</span></div></div> <div class="hotTag right-bk"><h3 class="com-title">热门标签</h3> <div class="hotTag-box item-box clearfix"><p>無數據</p></div></div> <div class="right-bk rank"><h3 class="com-title"><a href="javascript:;">冠軍排行榜</a></h3> <ul><li>暫無數據</li></ul></div> <div class="activeUser right-bk"><h3 class="com-title">活跃用户</h3> <div class="activeUser-box"><a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a></div></div> <img src="data:image/png;base64,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" class="to-top"></div></div> <!----> <!----> <!----> <div class="page-cnt footer"><p>T5客服知识库<span>|</span>zsk.591.com.tw</p></div></div></div><script type="text/javascript" src="/js/manifest.f192b.js"></script><script type="text/javascript" src="/js/vendor.17570.js"></script><script type="text/javascript" src="/js/app.58254.js"></script><div id="edui_fixedlayer" class="edui-default" style="position: fixed; left: 0px; top: 0px; width: 0px; height: 0px;"><div id="edui148" class="edui-popup  edui-bubble edui-default" onmousedown="return false;" style="display: none;"> <div id="edui148_body" class="edui-popup-body edui-default"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank" class="edui-default"></iframe> <div class="edui-shadow edui-default"></div> <div id="edui148_content" class="edui-popup-content edui-default">  </div> </div></div></div></body></html>