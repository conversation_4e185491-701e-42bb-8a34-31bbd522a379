<html class=" "><head><meta charset="utf-8"><title>591客服知識庫</title><link rel="stylesheet" href="/static/font/font-awesome.min.css"><link href="/css/app.e5aa4.css" rel="stylesheet"><script type="text/javascript" charset="utf-8" async="" src="/js/questionDetail.d0a6b.js"></script><style type="text/css">.que-detail-page .index-left .button{width:74px;height:32px;line-height:32px;font-size:13px;margin-right:10px}.que-detail-page .index-left .button:hover{color:#fff}.que-detail-page .detail-box{padding:20px 30px;border:1px solid #e5e5e5;border-radius:4px}.que-detail-page .detail-box h3{font-size:18px;color:#333;margin-bottom:10px}.que-detail-page .detail-box h3 span{color:#f77d24}.que-detail-page .detail-box .add-fav{cursor:pointer}.que-detail-page .d-b-menu{margin:15px 0 25px;font-size:13px;color:#999}.que-detail-page .d-b-menu b{display:inline-block;font-weight:400;margin:0 10px}.que-detail-page .d-b-menu .r{float:right;color:#f77d24}.que-detail-page .d-b-menu .r .fa{margin-right:5px;font-size:16px}.que-detail-page .d-b-cnt{margin-bottom:25px;font-size:13px;color:#666}.que-detail-page .submit-ask{display:block;margin:32px auto 0}.que-detail-page .reply-list{margin:30px 0 10px;overflow:hidden}.que-detail-page .reply-list .reply-item{padding:15px 30px 25px;margin-bottom:30px;border-radius:4px;border:1px solid #e5e5e5}.que-detail-page .reply-list .reply-item h4{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;line-height:50px;font-size:13px}.que-detail-page .reply-list .reply-item h4 img{width:40px;height:40px;border-radius:50%}.que-detail-page .reply-list .reply-item h4 .time{color:#999}.que-detail-page .reply-list .reply-item h4 .button{-ms-flex-item-align:center;align-self:center}.que-detail-page .reply-list .reply-item h4 .delete{color:#666;cursor:pointer}.que-detail-page .reply-list .reply-item .reply-detail{padding:20px 0 30px}.que-detail-page .more-answer{font-size:18px;color:#666;margin-bottom:5px}.que-detail-page .editor-content table{width:100%;border:1px solid #ddd}.que-detail-page .editor-content table td,.que-detail-page .editor-content table th{border:1px solid #ddd;padding:2px 8px;line-height:2}.que-detail-page .editor-content img{max-width:100%;vertical-align:text-bottom}.que-detail-page .edit-btn{text-align:center;margin-top:20px}.que-detail-page .edit-btn .edit-cancle{background:#aaa}</style><style type="text/css">.head-info{line-height:26px;background:#f5f5f5;font-size:13px;color:#999}.head-info a{margin-left:20px;color:#333}.head-search{margin-top:22px}.head-search img{float:left}.head-search .srh-box{float:left;margin:20px 0 0 20px}.head-search .srh-box .srh-input{float:left;width:260px;height:38px;line-height:38px;border:2px solid #37a508;border-radius:4px 0 0 4px}.head-search .srh-box a,.head-search .srh-box button{float:left;width:80px;height:38px;line-height:36px;font-size:16px;color:#fff}.head-search .srh-box .srh-btn{border-radius:0 4px 4px 0}.head-search .srh-box .ask-btn{margin-left:10px}.head-nav{float:left;margin:20px 0 0 60px;color:#fff;font-size:16px;line-height:36px}.head-nav a{display:block;height:100%;color:#333}.head-nav li{position:relative;float:left;margin-right:35px}.head-nav .active a,.head-nav li:hover a{color:#30b940}.head-nav .active:after,.head-nav li:hover:after{display:block;content:"";height:3px;width:100%;background:#30b940}.head-pic{width:100%;height:90px;margin-top:20px;background:url(/static/banner.png) top no-repeat}</style><style type="text/css">.top-breadcrumb{height:20px;margin-top:10px;margin-bottom:40px;font-size:13px;color:#666}.top-breadcrumb .t-b-icon{color:#30b940}.top-breadcrumb span{margin-left:8px}.top-breadcrumb span .fa{font-size:16px;margin-left:8px}.top-breadcrumb span:first-child{margin-left:0}.top-breadcrumb .now{color:#30b940}</style><script type="text/javascript" src="/static/ueditor/ueditor.config.js?20210319" id="configScriptTag"></script><script type="text/javascript" src="/static/ueditor/ueditor.all.js?20210319" id="editorScriptTag"></script><style type="text/css">.checkbox[data-v-0fe26aab]{word-spacing:4px;vertical-align:bottom;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.checkbox i[data-v-0fe26aab]{display:inline-block;width:14px;height:14px;border-radius:2px;border:1px solid #ccc;background:#fff;position:relative;top:1px}.checkbox input[data-v-0fe26aab]{opacity:0;width:0;height:0;margin:0}.checkbox input:checked+i[data-v-0fe26aab]{background:#30b940;border-color:#30b940}.checkbox input:checked+i[data-v-0fe26aab]:before{content:"";position:absolute;left:2px;top:1px;width:11px;height:6px;border-bottom:2px solid #fff;border-left:2px solid #fff;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}</style><style type="text/css">.popbox{position:fixed;top:50%;left:50%;z-index:1011;background:#fff;border-radius:6px;overflow:hidden}.popbox h3{font-size:16px;line-height:40px;padding:0 20px}.popbox h3 span{float:right;color:#666;font-size:13px;cursor:pointer}.popbox .pb-cnt{padding:0 20px;font-size:13px}.popbox .pb-btn-area{position:absolute;bottom:0;left:0;z-index:1;width:100%;text-align:right;padding:0 20px 20px 0}.popbox .pb-btn-area button{height:32px;line-height:32px;border:0;background:#fff;cursor:pointer;border-radius:6px;margin-left:15px;padding:0 15px}.popbox .pb-btn-area button.pb-btn-ok{background:#daf3cf;color:#279005}.popbox .pb-btn-area button.pb-btn-ok:hover{background:#279005;color:#fff}</style><style type="text/css">.shadow{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1010;background:#000}</style><style type="text/css">.footer{margin-top:70px;margin-bottom:20px;padding-top:20px;text-align:center;color:#999;border-top:1px solid #f5f5f5}.footer span{margin:0 10px}</style><script src="/static/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript" defer="defer"></script><link href="/static/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet"><script src="/static/ueditor/third-party/codemirror/codemirror.js" type="text/javascript" defer="defer"></script><link rel="stylesheet" type="text/css" href="/static/ueditor/third-party/codemirror/codemirror.css"><script src="/static/ueditor/third-party/zeroclipboard/ZeroClipboard.js" type="text/javascript" defer="defer"></script><style type="text/css">.activeUser,.favorite,.hotTag,.rank{margin-bottom:40px}</style><style type="text/css">.rank .rank-page{height:60px;line-height:60px;font-size:18px;margin-bottom:20px;text-align:center;background:#fafafa;border-radius:5px}.rank ul{padding:0 20px}.rank li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;border-bottom:1px dotted #f5f5f5;line-height:50px}.rank li .flag{position:relative;display:inline-block;width:20px;height:30px;background:#476472;vertical-align:middle;margin-right:12px;color:#fff;text-align:center;line-height:20px;font-size:13px;font-style:normal}.rank li .flag:after{content:"";position:absolute;bottom:-4px;left:-4px;border:14px solid transparent;border-bottom-color:#fff}.rank li:first-child .flag{background:#fb6757}.rank li:nth-child(2) .flag{background:#fac525}.rank li:nth-child(3) .flag{background:#00a9fb}.rank li img{width:36px;height:36px;border-radius:50%;margin-right:12px}</style><style type="text/css">.activeUser .activeUser-box a{display:inline-block;width:64px;height:64px;border-radius:50%;overflow:hidden;margin:0 6px 12px}</style><style type="text/css">.to-top{display:none;position:fixed;bottom:150px;z-index:5;width:50px;height:50px;cursor:pointer}</style></head><body style=""><div id="app" style=""><div class="que-detail-page" style=""><div class="wiki-hd"><div class="head-info"><p class="page-cnt">
      您好，欢迎来到591客服知识库
      <a href="https://www.591.com.tw/admin.php?new_url=https://zsk.591.com.tw">[请登入]</a> <!----></p></div> <div class="page-cnt head-search clearfix"><a href="/" class="router-link-active"><img src="/static/logo.png"></a> <ul class="head-nav clearfix"><li class="router-link-active active"><a href="/">591房屋交易网</a></li> <li class=""><a href="/life">客服充电站</a></li> <li class=""><a href="/notification">通知事项</a></li></ul> <div class="srh-box"><input type="text" placeholder="请输入搜索内容" class="srh-input"> <button type="button" class="button srh-btn">搜索</button> <a href="/ask/591" class="button button-orange ask-btn">提問</a></div></div> <div class="head-pic"></div></div> <div class="top-breadcrumb com-width"><span><a href="/collection/591/1302" class="">客服質檢相關<i class="fa fa-angle-double-right"></i></a></span><span><a href="/collection/591/1302/1386" class="">資料審核<i class="fa fa-angle-double-right"></i></a></span><span><strong class="now">问题详情</strong></span></div> <div class="com-width clearfix" style=""><div class="index-left" style=""><div class="detail-box"><h3><!---->修改審核規範及處理流程</h3> <p class="clearfix d-b-menu"><span class="l">
            黃雪靜&nbsp;&nbsp;&nbsp;浏览424次&nbsp;&nbsp;&nbsp;2019-09-06 16:40:31
          </span> <span class="r"><span class="add-fav"><i class="fa fa-heart"></i>加入最爱</span></span></p> <div class="d-b-cnt editor-content big-img-parent"><div><p>如題</p></div></div> <div class="d-b-button"><a href="/edit/591/3624" class="button">编辑</a> <button class="button button-orange">关闭提问</button></div></div> <div class="hr-line"></div> <ul class="reply-list big-img-parent"><li class="reply-item"><h4><div><img src="/upload/head/10177.gif?time=1556271256">&nbsp;&nbsp;&nbsp;
              <span class="user">黃雪靜</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-06-04 14:47:21</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><table cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td width="728" colspan="4" valign="top" style="border: 1px solid windowtext; background: rgb(217, 217, 217); padding: 0px 7px;"><p style="text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong>修訂歷史</strong></span></p></td></tr><tr><td width="87" valign="middle" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; background: rgb(217, 217, 217); padding: 0px 7px;" align="center"><p style="text-align:center"><strong><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">日期</span></strong></p></td><td width="131" valign="middle" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(217, 217, 217); padding: 0px 7px;" align="center"><p style="text-align:center"><strong><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">修訂前</span></strong></p></td><td width="411" valign="middle" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(217, 217, 217); padding: 0px 7px;" align="center"><p style="text-align:center"><strong><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">修訂後</span></strong></p></td><td width="57" valign="middle" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; background: rgb(217, 217, 217); padding: 0px 7px;" align="center"><p style="text-align:center"><strong><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">作者</span></strong></p></td></tr><tr><td width="87" valign="middle" align="center" style="border-width: 1px; border-right-style: solid; border-color: windowtext; border-bottom-style: solid; border-image: initial; padding: 0px 7px; word-break: break-all;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px; text-align: center;">2024.10.29</span></td><td width="131" valign="middle" align="center" style="border-width: 1px; border-color: windowtext; border-bottom-style: solid; border-right-style: solid; padding: 0px 7px; word-break: break-all;"><p><span style="font-family:微软雅黑, Microsoft YaHei"><span style="font-size: 14px;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px; text-align: center; text-wrap: wrap;">新增</span></span></span></p></td><td width="411" valign="middle" align="left" style="border-width: 1px; border-color: windowtext; border-bottom-style: solid; border-right-style: solid; padding: 0px 7px; word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span><p>出售案件（不包含土地）可不用参考以下审核规范，只需要审核：</p><ol class=" list-paddingleft-2" style="list-style-type: decimal;"><li><p><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-wrap-mode: wrap;">虛假金額、金额上下不一致</span></p></li><li><p><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-wrap-mode: wrap;">虛假坪數、坪数上下不一致</span></p></li><li><p><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-wrap-mode: wrap;">楼层错误、楼层上下不一致</span></p></li><li><p><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-wrap-mode: wrap;"><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-wrap-mode: wrap;">格局上下不一致</span></span></p></li><li><p><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-wrap-mode: wrap;"><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-wrap-mode: wrap;">廣告標題/特色備註另一個路名或多個路名，結合地圖查詢，非附近街道</span></span></p></li><li><p><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-wrap-mode: wrap;"><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-wrap-mode: wrap;">廣告標題/特色備註已售出、已完销、賀成交等</span></span><br></p></li></ol></td><td width="57" valign="middle" align="center" style="border-width: 1px; border-color: windowtext; border-bottom-style: solid; border-right-style: solid; padding: 0px 7px; word-break: break-all;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px; text-align: center;">黃雪靜</span></td></tr><tr><td width="87" valign="middle" align="center" style="border-width: 1px; border-right-style: solid; border-color: windowtext; border-bottom-style: solid; border-image: initial; padding: 0px 7px; word-break: break-all;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px; text-align: center;">2024.10.16</span></td><td width="131" valign="middle" align="center" style="border-width: 1px; border-color: windowtext; border-bottom-style: solid; border-right-style: solid; padding: 0px 7px; word-break: break-all;"><p><span style="font-family:微软雅黑, Microsoft YaHei"><span style="font-size: 14px;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px; text-align: center; text-wrap: wrap;">新增</span></span></span></p></td><td width="411" valign="middle" align="left" style="border-width: 1px; border-color: windowtext; border-bottom-style: solid; border-right-style: solid; padding: 0px 7px; word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span>出售新刊登物件30天內更換資料可審核通過，超過30天按現規則處理</td><td width="57" valign="middle" align="center" style="border-width: 1px; border-color: windowtext; border-bottom-style: solid; border-right-style: solid; padding: 0px 7px; word-break: break-all;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px; text-align: center;">黃雪靜</span></td></tr><tr><td width="93" valign="middle" align="center" style="border-width: 1px; border-right-style: solid; border-color: windowtext; border-bottom-style: solid; border-image: initial; padding: 0px 7px; word-break: break-all;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px; text-align: center;">2024.3.29</span></td><td width="131" valign="middle" align="center" style="border-width: 1px; border-color: windowtext; border-bottom-style: solid; border-right-style: solid; padding: 0px 7px; word-break: break-all;"><p><span style="font-family:微软雅黑, Microsoft YaHei"><span style="font-size: 14px;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px; text-align: center; text-wrap: wrap;">新增</span></span></span></p></td><td width="411" valign="middle" align="left" style="border-width: 1px; border-color: windowtext; border-bottom-style: solid; border-right-style: solid; padding: 0px 7px; word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span>中古屋新刊登物件7天內更換資料可審核通過，超過7天按現規則處理</td><td width="57" valign="middle" align="center" style="border-width: 1px; border-color: windowtext; border-bottom-style: solid; border-right-style: solid; padding: 0px 7px; word-break: break-all;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px; text-align: center;">黃雪靜</span></td></tr><tr style=";height:26px"><td width="89" valign="middle" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" height="26" align="center"><p style="text-align:center"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">2020.6.4</span></p></td><td width="131" valign="middle" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" height="26" align="center"><p style="text-align:center"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">新增</span></p></td><td width="411" valign="middle" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" height="26" align="center"><p style="text-align:left"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">新刊登審核及修改審核賬號出現違規，需操作【限制或停權】時請先發註冊手機簡訊通知會員（從6月5日開始執行）</span></p><p style="text-align:left"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">限制簡訊模板：【591】親愛的會員：您帳號新刊登/修改違規第五次，現新刊登/修改功能將被限制使用3天，3天後系統自動復權，如有疑問，請您聯絡客服中心，591感謝您！</span></p><p style="text-align:left"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">停權簡訊模板：【591】親愛的會員：您帳號新刊登/修改違規已累計第*次，現已被停權，請您聯絡客服中心瞭解並復權，591感謝您！</span></p></td><td width="57" valign="middle" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" height="26" align="center"><p style="text-align:center"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">黃雪靜</span></p></td></tr><tr style=";height:30px"><td width="93" valign="middle" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px; word-break: break-all;" height="30" align="center"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px; text-align: center;">2020.6.4</span></td><td width="131" valign="middle" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" height="30" align="left"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第六次賬號暫時停權</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">第七次學習，往後以此類推</span></p></td><td width="411" valign="middle" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" height="30" align="left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span><span style="font-size: 13px;font-family: 微软雅黑, sans-serif">第六次以上（含第六次）帳號暫時停權，請聯絡客服中心</span></td><td width="57" valign="middle" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" height="30" align="center"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px; text-align: center;">黃雪靜</span></td></tr><tr style=";height:30px"><td width="93" valign="middle" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px; word-break: break-all;" height="30" align="center">2022.7.26</td><td width="131" valign="middle" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" height="30" align="center"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px; text-align: center;">新增</span></td><td width="411" valign="middle" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" height="30" align="left">出租住家/商用套餐物件，修改更換房屋時，可直接通過</td><td width="57" valign="middle" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px; word-break: break-all;" height="30" align="center"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px; text-align: center;">黃雪靜</span></td></tr></tbody></table><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/upload/head/10177.gif?time=1556271256">&nbsp;&nbsp;&nbsp;
              <span class="user">黃雪靜</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-06-04 14:48:09</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p class="MsoListParagraph" style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;margin-left: 48px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;font-size: 13px;white-space: normal"><strong style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 16px">一、修改審核規範</span></strong></p><table width="773" cellspacing="0" cellpadding="0"><tbody style="box-sizing: border-box"><tr style="box-sizing: border-box" class="firstRow"><td style="box-sizing: border-box;padding: 0px 7px;border-color: windowtext;line-height: 2" width="76" valign="middle"><p style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 16px">類型</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top-color: windowtext;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="369" valign="middle"><p style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei'">不可通過</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top-color: windowtext;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="284" valign="middle"><p style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei'">可通過</span></p></td></tr><tr style="box-sizing: border-box;height: 30px"><td colspan="3" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left-color: windowtext;line-height: 2" width="728" valign="middle" height="30"><p style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';color: #E36C09">單項修改規則</span></p></td></tr><tr style="box-sizing: border-box"><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left-color: windowtext;line-height: 2" width="76"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">金額</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="369" valign="top"><p style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">1.虛假金額如123456/888888等或特色備註欄位價格為假，需電話聯絡</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">2.出租價格更改為出售價格（或反之）、整層價格改為一個房間價<span style="box-sizing: border-box;color: black">格均需電話聯絡</span></span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">3.房屋價格修改為車位價<span style="box-sizing: border-box;color: black">格，需確認聯絡（結合內容判斷）</span></span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">4.若金額修改有誤或上下不一致，需電話聯絡</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">5.金額修改一半以上的（不含一半），需電話聯絡</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="284" valign="top"><p style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">1.所有類型廣告價格做一半內調整（含一半）</span></p></td></tr><tr style="box-sizing: border-box"><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left-color: windowtext;line-height: 2" width="76"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">坪數</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="369" valign="top"><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">1.虛假坪數如12345/88888等，需電話聯絡</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">2.坪數修改上下不一致，需電話聯絡</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">3.坪數修改一半以上（不含一半），需電話聯絡</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">4.<span style="font-style: normal; text-decoration: none;">獨立套房/車位單項坪數修改超過3坪（不含3坪）不可通過</span></span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">譬如：100坪修改為49坪/201坪，則電話確認</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="284" valign="top"><p style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">1.坪數修改一半（含一半）</span></p></td></tr><tr style="box-sizing: border-box"><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left-color: windowtext;line-height: 2" width="76"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">樓層</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="369" valign="top"><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">1.樓層上下修改不一致，需電話聯絡</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">2.欄位樓層更換，需按違規發站內簡訊還原處理</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="284"><p style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">/</span></p></td></tr><tr style="box-sizing: border-box"><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left-color: windowtext;line-height: 2" width="76"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">格局</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="369" valign="top"><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">1.格局上下不一致，需電話聯絡</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">2.格局修改1房以上（不含1房），需電話聯絡</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">3.住家類衛浴未填寫，需電話聯絡（PS：若已上傳格局圖或特色有備註可直接修改）</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="284" valign="top"><p style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">1.出租：除套雅房外，其他廣告類型格局修改上下差一房</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">2.出售：所有廣告類型格局修改上下差一房</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">3.出租/售，格局只增加或減少客廳、衛浴</span></p></td></tr><tr style="box-sizing: border-box"><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left-color: windowtext;line-height: 2" width="76"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">標題/特色</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="369" valign="top"><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">1.廣告標題/特色備註另一個路名或多個路名，結合地圖查詢，非附近街道，需電話確認</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">2.廣告標題/特色備註已出租、已出售、賀成交等，需電話聯絡</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">3.獨立套房與分租套房/雅房相互備註，需電話確認</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">4.標題/特色備註其他間資訊，需按違規發站內簡訊編輯刪除</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">5.標題/特色備註修改用途和樓層完全更換，需按違規發站內簡訊還原</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">6.分租套雅房若修改超過新刊登規定範圍，需按違規發站內簡訊還原</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="284" valign="top"><p style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">1.出租店面標題/特色可備註頂讓金額（ps：欄位需填寫店面出租金額）</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">2.在原資訊正常範圍內修改</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">3.標題/特色備註已收訂、待簽約、暫不帶看等</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">4.標題/特色坪數或金額備註‘起’以欄位為准直接編輯（PS：若判斷為多間則按違規編輯刪除）</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">5.在新刊登規定範圍內：分租套雅房資訊任意修改均可通過</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">6.一般住家類與商用類型相互備註，結合照片現況，可店住/住辦兩用，可通過</span></p></td></tr><tr style="box-sizing: border-box"><td colspan="3" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left-color: windowtext;line-height: 2" width="728"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #E36C09; font-size: 16px; text-decoration: none;">多項修改規則</span></p></td></tr><tr style="box-sizing: border-box"><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left-color: windowtext;line-height: 2" width="76"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">/</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="369" valign="top"><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">1.金額+坪數兩項修改超過一半</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">2.金額+格局兩項修改超過一半</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">3.坪數+格局兩項修改超過一半</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">4.兩項修改一項未超過一半，一項超過一半</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">5.金額+坪數+格局資訊均修改</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">6. 有多項修改：獨立套房/車位修改3坪以上的（不含3坪）</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">7.標題/特色加其他項修改，結合內容靈活判斷是否為更換</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">注：以上需按更換違規，發站內簡訊還原</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="284" valign="top"><p style="box-sizing: border-box"><span style="box-sizing: border-box; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">1.刊登3天內修改直接通過（從刊登第二天起算），若修改有誤需電話聯絡確認</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">2.若會員資料填寫有誤，2項或3項修改如能結合廣告判斷不是更換，則通過</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; text-decoration: none;">3.两项修改，但獨立套房/車位修改3坪以內（包含3坪）</span></p></td></tr></tbody></table><p style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;font-size: 13px;white-space: normal"><br style="box-sizing: border-box"></p><p style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;font-size: 13px;white-space: normal"><br style="box-sizing: border-box"></p><p style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;font-size: 13px;white-space: normal"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 16px"><strong style="box-sizing: border-box"><span style="box-sizing: border-box;font-variant-numeric: normal;font-variant-east-asian: normal;font-weight: normal;font-stretch: normal;line-height: normal">&nbsp;</span></strong><strong style="box-sizing: border-box"><span style="box-sizing: border-box;font-variant-numeric: normal;font-variant-east-asian: normal;font-weight: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp; &nbsp;二、&nbsp;</span></strong><strong style="box-sizing: border-box">修改審核撥出處理流程</strong></span></p><p style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;font-size: 13px;white-space: normal"><strong style="box-sizing: border-box"><span style="box-sizing: border-box;font-size: 12px;font-family: 微软雅黑, sans-serif;color: black"><br style="box-sizing: border-box"></span></strong><span style="box-sizing: border-box;font-size: 14px;color: #E36C09"><strong style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, sans-serif">1.</span></strong><strong style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: PMingLiU, serif">違規處理流程：</span></strong></span></p><table width="773" cellspacing="0" cellpadding="0"><tbody style="box-sizing: border-box"><tr style="box-sizing: border-box" class="firstRow"><td style="box-sizing: border-box;padding: 0px 7px;border-color: windowtext;line-height: 2" width="160" valign="top"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">違規次數</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top-color: windowtext;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="473" valign="top"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">處罰規則</span></p></td></tr><tr style="box-sizing: border-box"><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left-color: windowtext;line-height: 2" width="160" valign="top"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第一至三次</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="473" valign="top"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">網站將違規資訊編輯，發站內簡訊警告</span></p></td></tr><tr style="box-sizing: border-box"><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left-color: windowtext;line-height: 2" width="160" valign="top"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第四次</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="473" valign="top"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">帳號學習，刊登時需通過網站試題考核，考核通過方可複權</span></p></td></tr><tr style="box-sizing: border-box"><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left-color: windowtext;line-height: 2" width="160" valign="top"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第五次</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="473" valign="top"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">帳號限制修改3天，3天後系統自動復權</span></p></td></tr><tr style="box-sizing: border-box"><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left-color: windowtext;line-height: 2" width="160" valign="top"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第六次</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="473" valign="top"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px"><span style="box-sizing: border-box;font-size: 13px"><span style="box-sizing: border-box">第六次以上（含第六次）帳號暫時停權，請聯絡客服中心</span></span></span></p></td></tr></tbody></table><p><strong style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: rgb(51, 51, 51); box-sizing: border-box;">註<span style="color:#7f7f7f;box-sizing: border-box;">：</span></strong><br></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #7F7F7F;">1、帳號當天多次出現違規只記一次，第二天仍出現違規則按流程處理</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #7F7F7F;">2、若</span><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #7F7F7F;">賬號第五次違規限制刊登（還在限制中），今天第六次違規，則不記違規次數，但需將違規內容編輯掉並做好記錄</span></p><p><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #7F7F7F;"><br></span></p><p><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #7F7F7F;"></span></p><p><span style="color: #000000;"><strong><span style="font-family: 微软雅黑,&quot;Microsoft YaHei&quot;; font-size: 14px;">【新增】2021.7.1生效</span></strong></span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #7F7F7F;"><br></span></p><p><span style="color: #FF0000; font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;">PS：會員違規次數累計以曆年制來計算，超過一年，新刊登修改違規审核記錄將清零<br>歷年制:即每年1月1日至12月31日</span></p><p><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #7F7F7F;"></span></p><p><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #7F7F7F;"></span><br></p><p style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;font-size: 13px;white-space: normal"><br style="box-sizing: border-box"></p><p style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;font-size: 13px;white-space: normal"><span style="box-sizing: border-box;font-size: 12px;font-family: 微软雅黑, sans-serif;color: black">&nbsp;</span></p><p style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;font-size: 13px;white-space: normal"><span style="box-sizing: border-box;font-size: 14px;color: #E36C09"><strong style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, sans-serif">2.</span></strong><strong style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: PMingLiU, serif">撥出處理流程：</span></strong></span></p><p style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;font-size: 13px;white-space: normal"><span style="box-sizing: border-box;font-size: 14px;color: #E36C09"><strong style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: PMingLiU, serif"><br style="box-sizing: border-box"></span></strong></span></p><table width="773" cellspacing="0" cellpadding="0"><tbody style="box-sizing: border-box"><tr style="box-sizing: border-box;height: 65px" class="firstRow"><td rowspan="5" style="box-sizing: border-box;padding: 0px 7px;border-color: windowtext;line-height: 2" width="95" height="65"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">資訊類</span></p></td><td rowspan="2" style="box-sizing: border-box;padding: 0px 7px;border-top-color: windowtext;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="66" height="65"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">聯絡到</span></p></td><td colspan="2" style="box-sizing: border-box;padding: 0px 7px;border-top-color: windowtext;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="123" height="65"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">配合</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top-color: windowtext;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="444" height="65"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第一天：同意客服修改，直接處理並審核通過；需自行修改，跟蹤至第二天</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第二天：查看是否修改正確，若正確審核通過，不正確則依照記錄再聯絡或編輯</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第三天：查看是否修改正確，若仍未修改，發站內簡訊還原資訊</span></p></td></tr><tr style="box-sizing: border-box;height: 66px"><td colspan="2" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="123" height="66"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">不配合</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="444" height="66"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第一天：聯絡不配合處理則做好記錄，發手機簡訊通知</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第二天：再次聯絡仍然不配合，發站內簡訊還原資訊</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">PS：特殊情況請與現場帶班主任回饋確認</span></p></td></tr><tr style="box-sizing: border-box;height: 37px"><td rowspan="2" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="66" height="37"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">聯絡不到</span></p></td><td rowspan="2" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="57" height="37"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">跟2天</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="66" height="37"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">聯絡到</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="444" height="37"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第二天：聯絡同意修改，直接處理並審核通過；需自行修改，跟蹤至第三天</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第三天：查看是否修改正確，若仍未修改，發站內簡訊還原資訊</span></p></td></tr><tr style="box-sizing: border-box"><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="66"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">聯絡不到</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="444"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">發站內簡訊+還原資訊，做好記錄</span></p></td></tr><tr style="box-sizing: border-box"><td colspan="4" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="633"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">注：若資訊有誤聯絡兩天均無人接聽，發手機簡訊+郵件+暫時關閉廣告</span></p></td></tr><tr style="box-sizing: border-box;height: 31px"><td rowspan="4" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left-color: windowtext;line-height: 2" width="95" height="31"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">備註已成交</span></p></td><td rowspan="2" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="66" height="31"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">聯絡到</span></p></td><td colspan="2" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="123" height="31"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">配合</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="444" height="31"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">1.同意下架則直接幫其操作，需提醒廣告時間隨之結束</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">2.若表示描述錯誤，則幫其修改並審核通過</span></p></td></tr><tr style="box-sizing: border-box;height: 31px"><td colspan="2" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="123" height="31"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">不配合</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="444" height="31"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第一天：聯絡不配合處理則做好記錄，發手機簡訊通知</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第二天：聯絡仍不同意處理，則發註冊手機簡訊+郵件+廣告暫時關閉處理</span></p></td></tr><tr style="box-sizing: border-box;height: 31px"><td rowspan="2" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="66" height="31"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">聯絡不到</span></p></td><td rowspan="2" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="57" height="31"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">跟2天</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="66" height="31"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">聯絡到</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="444" height="31"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">根據結果處理</span></p></td></tr><tr style="box-sizing: border-box;height: 31px"><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="66" height="31"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">聯絡不到</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="444" height="31"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">發註冊手機簡訊+郵件+廣告暫時關閉處理，做好記錄</span></p></td></tr><tr style="box-sizing: border-box"><td rowspan="5" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left-color: windowtext;line-height: 2" width="95"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">重複更換</span></p></td><td rowspan="2" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="66"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">聯絡到</span></p></td><td colspan="2" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="123"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">配合</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="444"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第一天：同意客服修改，直接處理並審核通過；需自行處理，跟蹤至第二天</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第二天：查看是否修改，若未處理直接發簡訊還原資訊</span></p></td></tr><tr style="box-sizing: border-box"><td colspan="2" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="123"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">不配合</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="444"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第一天：聯絡不配合處理則做好記錄，發手機簡訊通知</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第二天：再次聯絡仍然不配合，發站內簡訊還原資訊</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">PS：特殊情況請與現場帶班主任回饋確認</span></p></td></tr><tr style="box-sizing: border-box"><td rowspan="2" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="66"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">聯絡不到</span></p></td><td rowspan="2" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="57"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">跟2天</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="66"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">聯絡到</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="444"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">根據結果處理</span></p></td></tr><tr style="box-sizing: border-box"><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="66"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">聯絡不到</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="444"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第一次：發手機簡訊通知<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">（簡訊內需提醒未處理將會直接還原）</span></span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第二次：<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">做好記錄，</span>直接還原資訊</span></p></td></tr><tr style="box-sizing: border-box"><td colspan="4" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="633"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">注意：1.重複更換聯絡兩次無人接聽還原資訊，若再次更換仍需放待處理；2.重複更換已聯絡過，後續再次更換，直接發站內簡訊還原資訊</span></p></td></tr><tr style="box-sizing: border-box"><td rowspan="3" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left-color: windowtext;line-height: 2" width="95"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">租屋法案</span></p></td><td rowspan="2" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="66"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">已傳藤本</span></p></td><td rowspan="2" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="57"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">匹配不一致</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="66"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">聯絡到</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="444"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">1.直接幫其修改資料核實或勾選未辦產權登記</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">2.聯絡藤本上傳有誤，則重新上傳，跟蹤至第二天，如核實正確直接通過，若未上傳做好記錄通過</span></p></td></tr><tr style="box-sizing: border-box"><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="66"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">聯絡不到</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="444"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第一天：無人接聽，做好記錄，跟蹤至第二天</span></p><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">第二天：聯絡到根據結果處理；仍無人接聽，則做好記錄通過</span></p></td></tr><tr style="box-sizing: border-box"><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="66"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">未傳藤本</span></p></td><td colspan="2" style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="123"><p style="box-sizing: border-box;text-align: center"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">/</span></p></td><td style="box-sizing: border-box;padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;line-height: 2" width="444"><p style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">目前處理方式直接審核通過</span></p></td></tr></tbody></table><p style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;font-size: 13px;white-space: normal"><span style="box-sizing: border-box;color: #E36C09"><strong style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 16px">&nbsp;</span></strong></span></p><p style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;font-size: 13px;white-space: normal"><span style="box-sizing: border-box;color: #E36C09"><strong style="box-sizing: border-box"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 16px">注意事項：</span></strong></span></p><p style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;font-size: 13px;white-space: normal"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">1.若會員未修改特色說明，請點擊“特色說明”查看資訊是否有誤</span></p><p style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;font-size: 13px;white-space: normal"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">2.不管任何情況下，<span style="box-sizing: border-box">審核中若</span>遇到無法直接判斷是否需要確認的，則放待處理確認</span></p><p style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;font-size: 13px;white-space: normal"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">3.若前一次修改暫無廣告標題，會員違規修改按還原後，標題無法還原成功，如資訊上下不一致則需手動編輯</span></p><p style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;font-size: 13px;white-space: normal"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">4.若會員同一天修改多次，後臺出現多條未審核，請操作審核時優先審最新一個時間的資訊，否則系統無法確保是最新修改資訊，請知曉</span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li></ul> <h3 class="more-answer">我要補充</h3> <div style=""><div id="editor_61811940524292700" class="edui-default" style=""><div id="edui1" class="edui-editor  edui-default" style="width: 100%; z-index: 999;"><div id="edui1_toolbarbox" class="edui-editor-toolbarbox edui-default"><div id="edui1_toolbarboxouter" class="edui-editor-toolbarboxouter edui-default"><div class="edui-editor-toolbarboxinner edui-default"><div id="edui2" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui2&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui3" class="edui-box edui-button edui-for-fullscreen edui-default"><div id="edui3_state" onmousedown="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui3_body" unselectable="on" title="全屏" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui3&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui3&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui4" class="edui-box edui-button edui-for-source edui-default"><div id="edui4_state" onmousedown="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui4_body" unselectable="on" title="源代码" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui4&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui4&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui5" class="edui-box edui-button edui-for-undo edui-default"><div id="edui5_state" onmousedown="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui5_body" unselectable="on" title="撤销" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui5&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui5&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui6" class="edui-box edui-button edui-for-redo edui-default"><div id="edui6_state" onmousedown="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui6_body" unselectable="on" title="重做" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui6&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui6&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui7" class="edui-box edui-button edui-for-simpleupload edui-default"><div id="edui7_state" onmousedown="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui7_body" unselectable="on" title="单图上传" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui7&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui7&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui8" class="edui-box edui-button edui-for-imageleft edui-default"><div id="edui8_state" onmousedown="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui8_body" unselectable="on" title="左浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui8&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui8&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui9" class="edui-box edui-button edui-for-imageright edui-default"><div id="edui9_state" onmousedown="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui9_body" unselectable="on" title="右浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui9&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui9&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui10" class="edui-box edui-button edui-for-imagecenter edui-default"><div id="edui10_state" onmousedown="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui10_body" unselectable="on" title="居中" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui10&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui10&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui11" class="edui-box edui-separator  edui-default"></div><div id="edui12" class="edui-box edui-button edui-for-date edui-default"><div id="edui12_state" onmousedown="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui12_body" unselectable="on" title="日期" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui12&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui12&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui13" class="edui-box edui-button edui-for-time edui-default"><div id="edui13_state" onmousedown="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui13_body" unselectable="on" title="时间" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui13&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui13&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui14" class="edui-box edui-button edui-for-horizontal edui-default"><div id="edui14_state" onmousedown="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui14_body" unselectable="on" title="分隔线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui14&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui14&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui15" class="edui-box edui-separator  edui-default"></div><div id="edui16" class="edui-box edui-splitbutton edui-for-inserttable edui-default"><div title="插入表格" id="edui16_state" onmousedown="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui16_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onArrowClick();"></div></div></div></div><div id="edui19" class="edui-box edui-button edui-for-deletetable edui-default"><div id="edui19_state" onmousedown="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui19_body" unselectable="on" title="删除表格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui19&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui19&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui20" class="edui-box edui-button edui-for-insertrow edui-default"><div id="edui20_state" onmousedown="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui20_body" unselectable="on" title="前插入行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui20&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui20&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui21" class="edui-box edui-button edui-for-insertcol edui-default"><div id="edui21_state" onmousedown="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui21_body" unselectable="on" title="前插入列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui21&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui21&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui22" class="edui-box edui-button edui-for-mergeright edui-default"><div id="edui22_state" onmousedown="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui22_body" unselectable="on" title="右合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui22&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui22&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui23" class="edui-box edui-button edui-for-mergedown edui-default"><div id="edui23_state" onmousedown="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui23_body" unselectable="on" title="下合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui23&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui23&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui24" class="edui-box edui-button edui-for-deleterow edui-default"><div id="edui24_state" onmousedown="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui24_body" unselectable="on" title="删除行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui24&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui24&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui25" class="edui-box edui-button edui-for-deletecol edui-default"><div id="edui25_state" onmousedown="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui25_body" unselectable="on" title="删除列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui25&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui25&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui26" class="edui-box edui-button edui-for-splittorows edui-default"><div id="edui26_state" onmousedown="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui26_body" unselectable="on" title="拆分成行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui26&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui26&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui27" class="edui-box edui-button edui-for-splittocols edui-default"><div id="edui27_state" onmousedown="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui27_body" unselectable="on" title="拆分成列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui27&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui27&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui28" class="edui-box edui-button edui-for-splittocells edui-default"><div id="edui28_state" onmousedown="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui28_body" unselectable="on" title="完全拆分单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui28&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui28&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui29" class="edui-box edui-button edui-for-mergecells edui-default"><div id="edui29_state" onmousedown="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui29_body" unselectable="on" title="合并多个单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui29&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui29&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui36" class="edui-box edui-button edui-for-edittable edui-default"><div id="edui36_state" onmousedown="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui36_body" unselectable="on" title="表格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui36&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui36&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui41" class="edui-box edui-button edui-for-edittd edui-default"><div id="edui41_state" onmousedown="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui41_body" unselectable="on" title="单元格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui41&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui41&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui42" class="edui-box edui-separator  edui-default"></div><div id="edui43" class="edui-box edui-menubutton edui-for-lineheight edui-default"><div title="行间距" id="edui43_state" onmousedown="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui43_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onArrowClick();"></div></div></div></div><div id="edui52" class="edui-box edui-menubutton edui-for-rowspacingtop edui-default"><div title="段前距" id="edui52_state" onmousedown="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui52_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onArrowClick();"></div></div></div></div><div id="edui59" class="edui-box edui-menubutton edui-for-rowspacingbottom edui-default"><div title="段后距" id="edui59_state" onmousedown="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui59_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onArrowClick();"></div></div></div></div></div><div id="edui66" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui66&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui67" class="edui-box edui-combox edui-for-fontfamily edui-default"><div title="字体" id="edui67_state" onmousedown="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui67_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onButtonClick(event, this);">字体</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onArrowClick();"></div></div></div></div><div id="edui80" class="edui-box edui-combox edui-for-customstyle edui-default"><div title="自定义标题" id="edui80_state" onmousedown="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui80_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onButtonClick(event, this);">自定义标题</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onArrowClick();"></div></div></div></div><div id="edui86" class="edui-box edui-combox edui-for-paragraph edui-default"><div title="段落格式" id="edui86_state" onmousedown="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui86_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onButtonClick(event, this);">段落格式</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onArrowClick();"></div></div></div></div><div id="edui95" class="edui-box edui-combox edui-for-fontsize edui-default"><div title="字号" id="edui95_state" onmousedown="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui95_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onButtonClick(event, this);">字号</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onArrowClick();"></div></div></div></div><div id="edui110" class="edui-box edui-button edui-for-link edui-default"><div id="edui110_state" onmousedown="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui110_body" unselectable="on" title="超链接" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui110&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui110&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui111" class="edui-box edui-button edui-for-indent edui-default"><div id="edui111_state" onmousedown="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui111_body" unselectable="on" title="首行缩进" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui111&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui111&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui112" class="edui-box edui-button edui-for-bold edui-default"><div id="edui112_state" onmousedown="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui112_body" unselectable="on" title="加粗" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui112&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui112&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui113" class="edui-box edui-splitbutton edui-for-forecolor edui-default edui-colorbutton"><div title="字体颜色" id="edui113_state" onmousedown="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui113_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui113_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onArrowClick();"></div></div></div></div><div id="edui116" class="edui-box edui-splitbutton edui-for-backcolor edui-default edui-colorbutton"><div title="背景色" id="edui116_state" onmousedown="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui116_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui116_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onArrowClick();"></div></div></div></div><div id="edui119" class="edui-box edui-button edui-for-italic edui-default"><div id="edui119_state" onmousedown="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui119_body" unselectable="on" title="斜体" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui119&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui119&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui120" class="edui-box edui-button edui-for-underline edui-default"><div id="edui120_state" onmousedown="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui120_body" unselectable="on" title="下划线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui120&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui120&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui121" class="edui-box edui-button edui-for-strikethrough edui-default"><div id="edui121_state" onmousedown="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui121_body" unselectable="on" title="删除线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui121&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui121&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui122" class="edui-box edui-button edui-for-superscript edui-default"><div id="edui122_state" onmousedown="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui122_body" unselectable="on" title="上标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui122&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui122&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui123" class="edui-box edui-button edui-for-subscript edui-default"><div id="edui123_state" onmousedown="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui123_body" unselectable="on" title="下标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui123&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui123&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui124" class="edui-box edui-button edui-for-removeformat edui-default"><div id="edui124_state" onmousedown="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui124_body" unselectable="on" title="清除格式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui124&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui124&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui125" class="edui-box edui-button edui-for-blockquote edui-default"><div id="edui125_state" onmousedown="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui125_body" unselectable="on" title="引用" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui125&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui125&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui126" class="edui-box edui-button edui-for-pasteplain edui-default"><div id="edui126_state" onmousedown="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui126_body" unselectable="on" title="纯文本粘贴模式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui126&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui126&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui127" class="edui-box edui-menubutton edui-for-insertorderedlist edui-default"><div title="有序列表" id="edui127_state" onmousedown="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui127_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onArrowClick();"></div></div></div></div><div id="edui140" class="edui-box edui-menubutton edui-for-insertunorderedlist edui-default"><div title="无序列表" id="edui140_state" onmousedown="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui140_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onArrowClick();"></div></div></div></div><div id="edui147" class="edui-box edui-button edui-for-cleardoc edui-default"><div id="edui147_state" onmousedown="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui147_body" unselectable="on" title="清空文档" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui147&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui147&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div></div></div></div><div id="edui1_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;"><div id="edui1_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui1&quot;].showWordImageDialog();">点击上传</div><div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui1&quot;].hideToolbarMsg();">x</div><div id="edui1_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div><div style="height:0;overflow:hidden;clear:both;" class="edui-default"></div></div><div id="edui1_message_holder" class="edui-editor-messageholder edui-default"></div></div><div id="edui1_iframeholder" class="edui-editor-iframeholder edui-default" style="width: 100%; height: 300px; z-index: 999; overflow: hidden;"><iframe id="ueditor_0" width="100%" height="100%" frameborder="0" src="javascript:void(function(){document.open();document.write(&quot;<!DOCTYPE html><html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}
body{margin:8px;font-family:sans-serif;font-size:13px;}p{margin:5px 0;}</style><link rel='stylesheet' type='text/css' href='/static/ueditor/themes/iframe.css'/></head><body class='view' ></body><script type='text/javascript'  id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant0'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);</script></html>&quot;);document.close();}())"></iframe></div><div id="edui1_bottombar" class="edui-editor-bottomContainer edui-default"><table class="edui-default"><tbody class="edui-default"><tr class="edui-default"><td id="edui1_elementpath" class="edui-editor-bottombar edui-default"><div class="edui-editor-breadcrumb">元素路径:</div></td><td id="edui1_wordcount" class="edui-editor-wordcount edui-default">字数统计</td><td id="edui1_scale" class="edui-editor-scale edui-default" style="display: none;"><div class="edui-editor-icon edui-default"></div></td></tr></tbody></table></div><div id="edui1_scalelayer" class="edui-default"></div></div></div> <div class="edit-btn"><button type="button" class="button">提交回答</button> <label data-v-0fe26aab="" for="commonCheckbox" class="checkbox"><input data-v-0fe26aab="" type="checkbox" id="commonCheckbox" name="commonCheckbox"> <i data-v-0fe26aab=""></i> 需要彈窗，請勾選</label></div></div></div> <div class="index-right"><div class="right-bk"><h3 class="com-title"><a href="javascript:;">我的最爱</a></h3> <div class="item-box clearfix"><span>錯誤，請您登入後再操作！</span></div></div> <div class="hotTag right-bk"><h3 class="com-title">热门标签</h3> <div class="hotTag-box item-box clearfix"><p>無數據</p></div></div> <div class="right-bk rank"><h3 class="com-title"><a href="javascript:;">冠軍排行榜</a></h3> <ul><li><div><span class="number">1</span> <img src="/static/timg.jpeg"> <span>王菊芳</span></div> <span class="score">612分</span></li><li><div><span class="number">2</span> <img src="/upload/head/10177.gif?time=1556271256"> <span>黃雪靜</span></div> <span class="score">258分</span></li><li><div><span class="number">3</span> <img src="/static/timg.jpeg"> <span>田霞</span></div> <span class="score">240分</span></li><li><div><span class="number">4</span> <img src="/static/timg.jpeg"> <span>曹超雲</span></div> <span class="score">168分</span></li><li><div><span class="number">5</span> <img src="/static/timg.jpeg"> <span>文宇</span></div> <span class="score">96分</span></li><li><div><span class="number">6</span> <img src="/upload/head/10217.png?time=1548059679"> <span>黃興興</span></div> <span class="score">78分</span></li><li><div><span class="number">7</span> <img src="/static/timg.jpeg"> <span>黃佳麗</span></div> <span class="score">62分</span></li><li><div><span class="number">8</span> <img src="/static/timg.jpeg"> <span>马泽鹏</span></div> <span class="score">46分</span></li><li><div><span class="number">9</span> <img src="/static/timg.jpeg"> <span>陳敏南</span></div> <span class="score">40分</span></li><li><div><span class="number">10</span> <img src="/static/timg.jpeg"> <span>Cs-刘婷</span></div> <span class="score">10分</span></li><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></ul></div> <div class="activeUser right-bk"><h3 class="com-title">活跃用户</h3> <div class="activeUser-box"><a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a></div></div> <img src="data:image/png;base64,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" class="to-top"></div></div> <!----> <!----> <!----> <div class="page-cnt footer"><p>T5客服知识库<span>|</span>zsk.591.com.tw</p></div></div></div><script type="text/javascript" src="/js/manifest.f192b.js"></script><script type="text/javascript" src="/js/vendor.17570.js"></script><script type="text/javascript" src="/js/app.58254.js"></script><div id="edui_fixedlayer" class="edui-default" style="position: fixed; left: 0px; top: 0px; width: 0px; height: 0px;"><div id="edui148" class="edui-popup  edui-bubble edui-default" onmousedown="return false;" style="display: none;"> <div id="edui148_body" class="edui-popup-body edui-default"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank" class="edui-default"></iframe> <div class="edui-shadow edui-default"></div> <div id="edui148_content" class="edui-popup-content edui-default">  </div> </div></div></div></body></html>