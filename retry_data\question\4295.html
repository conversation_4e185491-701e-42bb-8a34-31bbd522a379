<html class="nprogress-busy"><head><meta charset="utf-8"><title>591客服知識庫</title><link rel="stylesheet" href="/static/font/font-awesome.min.css"><link href="/css/app.e5aa4.css" rel="stylesheet"><script type="text/javascript" charset="utf-8" async="" src="/js/questionDetail.d0a6b.js"></script><style type="text/css">.que-detail-page .index-left .button{width:74px;height:32px;line-height:32px;font-size:13px;margin-right:10px}.que-detail-page .index-left .button:hover{color:#fff}.que-detail-page .detail-box{padding:20px 30px;border:1px solid #e5e5e5;border-radius:4px}.que-detail-page .detail-box h3{font-size:18px;color:#333;margin-bottom:10px}.que-detail-page .detail-box h3 span{color:#f77d24}.que-detail-page .detail-box .add-fav{cursor:pointer}.que-detail-page .d-b-menu{margin:15px 0 25px;font-size:13px;color:#999}.que-detail-page .d-b-menu b{display:inline-block;font-weight:400;margin:0 10px}.que-detail-page .d-b-menu .r{float:right;color:#f77d24}.que-detail-page .d-b-menu .r .fa{margin-right:5px;font-size:16px}.que-detail-page .d-b-cnt{margin-bottom:25px;font-size:13px;color:#666}.que-detail-page .submit-ask{display:block;margin:32px auto 0}.que-detail-page .reply-list{margin:30px 0 10px;overflow:hidden}.que-detail-page .reply-list .reply-item{padding:15px 30px 25px;margin-bottom:30px;border-radius:4px;border:1px solid #e5e5e5}.que-detail-page .reply-list .reply-item h4{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;line-height:50px;font-size:13px}.que-detail-page .reply-list .reply-item h4 img{width:40px;height:40px;border-radius:50%}.que-detail-page .reply-list .reply-item h4 .time{color:#999}.que-detail-page .reply-list .reply-item h4 .button{-ms-flex-item-align:center;align-self:center}.que-detail-page .reply-list .reply-item h4 .delete{color:#666;cursor:pointer}.que-detail-page .reply-list .reply-item .reply-detail{padding:20px 0 30px}.que-detail-page .more-answer{font-size:18px;color:#666;margin-bottom:5px}.que-detail-page .editor-content table{width:100%;border:1px solid #ddd}.que-detail-page .editor-content table td,.que-detail-page .editor-content table th{border:1px solid #ddd;padding:2px 8px;line-height:2}.que-detail-page .editor-content img{max-width:100%;vertical-align:text-bottom}.que-detail-page .edit-btn{text-align:center;margin-top:20px}.que-detail-page .edit-btn .edit-cancle{background:#aaa}</style><style type="text/css">.head-info{line-height:26px;background:#f5f5f5;font-size:13px;color:#999}.head-info a{margin-left:20px;color:#333}.head-search{margin-top:22px}.head-search img{float:left}.head-search .srh-box{float:left;margin:20px 0 0 20px}.head-search .srh-box .srh-input{float:left;width:260px;height:38px;line-height:38px;border:2px solid #37a508;border-radius:4px 0 0 4px}.head-search .srh-box a,.head-search .srh-box button{float:left;width:80px;height:38px;line-height:36px;font-size:16px;color:#fff}.head-search .srh-box .srh-btn{border-radius:0 4px 4px 0}.head-search .srh-box .ask-btn{margin-left:10px}.head-nav{float:left;margin:20px 0 0 60px;color:#fff;font-size:16px;line-height:36px}.head-nav a{display:block;height:100%;color:#333}.head-nav li{position:relative;float:left;margin-right:35px}.head-nav .active a,.head-nav li:hover a{color:#30b940}.head-nav .active:after,.head-nav li:hover:after{display:block;content:"";height:3px;width:100%;background:#30b940}.head-pic{width:100%;height:90px;margin-top:20px;background:url(/static/banner.png) top no-repeat}</style><style type="text/css">.top-breadcrumb{height:20px;margin-top:10px;margin-bottom:40px;font-size:13px;color:#666}.top-breadcrumb .t-b-icon{color:#30b940}.top-breadcrumb span{margin-left:8px}.top-breadcrumb span .fa{font-size:16px;margin-left:8px}.top-breadcrumb span:first-child{margin-left:0}.top-breadcrumb .now{color:#30b940}</style><script type="text/javascript" src="/static/ueditor/ueditor.config.js?20210319" id="configScriptTag"></script><script type="text/javascript" src="/static/ueditor/ueditor.all.js?20210319" id="editorScriptTag"></script><style type="text/css">.checkbox[data-v-0fe26aab]{word-spacing:4px;vertical-align:bottom;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.checkbox i[data-v-0fe26aab]{display:inline-block;width:14px;height:14px;border-radius:2px;border:1px solid #ccc;background:#fff;position:relative;top:1px}.checkbox input[data-v-0fe26aab]{opacity:0;width:0;height:0;margin:0}.checkbox input:checked+i[data-v-0fe26aab]{background:#30b940;border-color:#30b940}.checkbox input:checked+i[data-v-0fe26aab]:before{content:"";position:absolute;left:2px;top:1px;width:11px;height:6px;border-bottom:2px solid #fff;border-left:2px solid #fff;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}</style><style type="text/css">.popbox{position:fixed;top:50%;left:50%;z-index:1011;background:#fff;border-radius:6px;overflow:hidden}.popbox h3{font-size:16px;line-height:40px;padding:0 20px}.popbox h3 span{float:right;color:#666;font-size:13px;cursor:pointer}.popbox .pb-cnt{padding:0 20px;font-size:13px}.popbox .pb-btn-area{position:absolute;bottom:0;left:0;z-index:1;width:100%;text-align:right;padding:0 20px 20px 0}.popbox .pb-btn-area button{height:32px;line-height:32px;border:0;background:#fff;cursor:pointer;border-radius:6px;margin-left:15px;padding:0 15px}.popbox .pb-btn-area button.pb-btn-ok{background:#daf3cf;color:#279005}.popbox .pb-btn-area button.pb-btn-ok:hover{background:#279005;color:#fff}</style><style type="text/css">.shadow{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1010;background:#000}</style><style type="text/css">.footer{margin-top:70px;margin-bottom:20px;padding-top:20px;text-align:center;color:#999;border-top:1px solid #f5f5f5}.footer span{margin:0 10px}</style><script src="/static/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript" defer="defer"></script><link href="/static/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet"><script src="/static/ueditor/third-party/codemirror/codemirror.js" type="text/javascript" defer="defer"></script><link rel="stylesheet" type="text/css" href="/static/ueditor/third-party/codemirror/codemirror.css"><script src="/static/ueditor/third-party/zeroclipboard/ZeroClipboard.js" type="text/javascript" defer="defer"></script><style type="text/css">.activeUser,.favorite,.hotTag,.rank{margin-bottom:40px}</style><style type="text/css">.rank .rank-page{height:60px;line-height:60px;font-size:18px;margin-bottom:20px;text-align:center;background:#fafafa;border-radius:5px}.rank ul{padding:0 20px}.rank li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;border-bottom:1px dotted #f5f5f5;line-height:50px}.rank li .flag{position:relative;display:inline-block;width:20px;height:30px;background:#476472;vertical-align:middle;margin-right:12px;color:#fff;text-align:center;line-height:20px;font-size:13px;font-style:normal}.rank li .flag:after{content:"";position:absolute;bottom:-4px;left:-4px;border:14px solid transparent;border-bottom-color:#fff}.rank li:first-child .flag{background:#fb6757}.rank li:nth-child(2) .flag{background:#fac525}.rank li:nth-child(3) .flag{background:#00a9fb}.rank li img{width:36px;height:36px;border-radius:50%;margin-right:12px}</style><style type="text/css">.activeUser .activeUser-box a{display:inline-block;width:64px;height:64px;border-radius:50%;overflow:hidden;margin:0 6px 12px}</style><style type="text/css">.to-top{display:none;position:fixed;bottom:150px;z-index:5;width:50px;height:50px;cursor:pointer}</style></head><body style=""><div id="app" style=""><div class="que-detail-page" style=""><div class="wiki-hd"><div class="head-info"><p class="page-cnt">
      您好，欢迎来到591客服知识库
      <a href="https://www.591.com.tw/admin.php?new_url=https://zsk.591.com.tw">[请登入]</a> <!----></p></div> <div class="page-cnt head-search clearfix"><a href="/" class="router-link-active"><img src="/static/logo.png"></a> <ul class="head-nav clearfix"><li class="router-link-active active"><a href="/">591房屋交易网</a></li> <li class=""><a href="/life">客服充电站</a></li> <li class=""><a href="/notification">通知事项</a></li></ul> <div class="srh-box"><input type="text" placeholder="请输入搜索内容" class="srh-input"> <button type="button" class="button srh-btn">搜索</button> <a href="/ask/591" class="button button-orange ask-btn">提問</a></div></div> <div class="head-pic"></div></div> <div class="top-breadcrumb com-width"><span><a href="/collection/591/1302" class="">客服質檢相關<i class="fa fa-angle-double-right"></i></a></span><span><a href="/collection/591/1302/1383" class="">電話接聽<i class="fa fa-angle-double-right"></i></a></span><span><strong class="now">问题详情</strong></span></div> <div class="com-width clearfix" style=""><div class="index-left" style=""><div class="detail-box"><h3><!---->會員要求刪除個資/註銷手機的處理流程</h3> <p class="clearfix d-b-menu"><span class="l">
            黃興興&nbsp;&nbsp;&nbsp;浏览264次&nbsp;&nbsp;&nbsp;2020-10-26 15:22:39
          </span> <span class="r"><span class="add-fav"><i class="fa fa-heart"></i>加入最爱</span></span></p> <div class="d-b-cnt editor-content big-img-parent"><div><p><span style="font-size:13px;font-family:'Microsoft YaHei',sans-serif">來電、申訴、信箱處理規則相同</span></p></div></div> <div class="d-b-button"><a href="/edit/591/4295" class="button">编辑</a> <button class="button button-orange">关闭提问</button></div></div> <div class="hr-line"></div> <ul class="reply-list big-img-parent"><li class="reply-item"><h4><div><img src="/upload/head/10217.png?time=1548059679">&nbsp;&nbsp;&nbsp;
              <span class="user">黃興興</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-26 15:36:56</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="white-space: normal; line-height: 1.75em;"><strong><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 16px;">一、定義</span></strong></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-size: 14px;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif;">1、個人資料：姓名、出生年月日、國民身分證統一編號、護照號碼、特徵、指紋、婚姻、家庭、教育、職業、</span><span style="box-sizing: border-box;">病歷、醫療、基因 、性生活、健康檢查、犯罪前科、聯絡方式、財務情況、社會活動及得以直接或間接方式識別該個人之資料</span>；<span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif;"></span></span></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-size: 14px; text-decoration: underline; color: #E36C09;"><strong><span style="font-size: 14px; color: #E36C09;"><a href="https://zsk.591.com.tw/question/3000" target="_blank" style="font-size: 14px; text-decoration: underline; color: rgb(227, 108, 9);">個資法相關內容</a>鏈接</span></strong></span><span style="font-size: 14px;"><br></span></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-size: 14px;"><br></span></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">2、刪除個資（刪除帳號）：是指會員不再使用此591的會員帳號，要求591的系統裡面不要再保留他的任何資料；</span></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-size: 14px;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">PS：標準的說法是“刪除個資<span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">”</span>，因為當會員來要求刪除帳號的時候，</span><strong><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px; color: #000000;"><span style="font-size: 14px;">我們僅可以幫他刪除此帳號的個人資料，如帳號、姓名、手機、固話、認證資料及客服記錄/申訴</span>記錄。</span></strong><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">而帳號內的刊登記錄、儲值記錄、發票等資訊，屬於商業會計法規定的交易憑證，需<strong>保留7年後才可刪除。</strong></span></span></p><p style="white-space: normal; line-height: 1.75em;"><span style="color: #FF0000;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;"><strong>注意：</strong></span><strong style="color: rgb(51, 51, 51); font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; box-sizing: border-box;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #FF0000;">超七年未登入之會員資料將被刪除</span></strong></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1）<span style="box-sizing: border-box; background-color: #FFFFFF;">統一刪除超七年未登入（指最後一次登入開始算起），且帳號餘額為0的會員帳號及相關資料。</span></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 14px;">2）系統會自動識別賬號未登入時間，若已達七年，我們將提前七天寄信至註冊信箱進行提醒；</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 14px;">3）若會員要繼續使用網站服務，重新登入會員帳號即可</span></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-size: 14px;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;"></span></span><br></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;"><br></span></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">3、註銷手機：是指會員在註冊或修改資料時，發現自己的手機被使用了，要求註銷，以便自己可以重新註冊或使用。這種情況，僅需要註銷手機即可。不需要刪除帳號的姓名、固話、認證資料等。</span></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-size: 13px; font-family: &quot;Microsoft YaHei&quot;, sans-serif;"><br></span></p><p style="white-space: normal; line-height: 1.75em;"><strong><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 16px;">二、處理流程</span></strong></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">1、判斷會員的訴求，明確會員是要刪除個資，還是要註銷手機。</span></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">2、若是刪除個資<span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px; color: #A5A5A5;">&lt;帳號是會員本人註冊的，現在不需要使用了&gt;：</span></span></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">1）確認是否是本人以及要刪除的原因（核對資料時，不可以外洩註冊資料給會員），是本人下一步，非本人請本人來電</span></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">2）判斷帳號是否符合刪除條件，符合下一步，不符合告知原因；</span></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;"></span></p><p style="line-height: 1.75em;"><span style="font-size: 14px; color: #548DD4;">PS：刪除個資必須同時滿足的條件：</span></p><p style="line-height: 1.75em;"><span style="font-size: 14px; color: #548DD4;">* 沒有刊登中的廣告；</span></p><p style="line-height: 1.75em;"><span style="font-size: 14px; color: #548DD4;">* 沒有進行中的服務（如：改開發票）；</span></p><p style="line-height: 1.75em;"><span style="font-size: 14px; color: #548DD4;">* 帳戶餘額為0，且無被凍結的款項；<span style="font-size: 14px; color: #A5A5A5;">（不為0，需要簽切結書同意放棄才可以刪除，這種可建議會員花掉，或者幫他停權）</span></span></p><p style="line-height: 1.75em;"><span style="font-size: 14px; color: #548DD4;">* 帳號內未發生任何糾紛（如，非本人儲值等）；</span></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">3）回撥註冊電話，核對至少三項註冊資料，核實後做好記錄請主任操作刪除；</span></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">4）註冊電話沒在用，請會員傳真或寄信提供身份證正反面+健保卡過來核實身份後再刪除。若不願意提供，則告知可先協助停權帳號。</span></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">&nbsp;</span></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">3、<span style="font-variant-numeric: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; font-size: 14px;">&nbsp;</span>若是註銷手機<span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px; color: #A5A5A5;">&lt;自己的手機被別人註冊了，或者自己想用這個手機註冊一個新帳號，不需要刪除原帳號的資料&gt;</span></span></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">1）<span style="font-variant-numeric: normal; font-stretch: normal; line-height: normal; font-family: &quot;Times New Roman&quot;; font-size: 14px;">&nbsp;</span>確認要註銷手機的原因，確認該手機註冊的帳號是否是來電者本人；</span></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">2）若是本人，則建議他繼續使用原帳號，若會員不願意，也可以協助註銷。若非本人（即註冊資料並不是來電者的），則需要協助註銷手機（<strong><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px; color: #FF0000;">一定不能<strong style="color: rgb(255, 0, 0); font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px; white-space: normal;">建議對方去找回此帳號的帳密！或</strong>刪除原帳號個資</span></strong>），如果他不註銷手機，此帳號也需要先停權，等帳號註冊人來電修改註冊手機才可開啟；</span></p><p style="white-space: normal; line-height: 1.75em;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 14px;">3）確認要註銷，則回撥註冊行動電話確認要註銷。即可做好記錄發給主任註銷。</span></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/upload/head/10217.png?time=1548059679">&nbsp;&nbsp;&nbsp;
              <span class="user">黃興興</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-26 16:47:27</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="text-align: center; line-height: 3em;"><strong><span style="font-size:24px;line-height:115%;font-family:標楷體">591</span></strong><strong><span style="font-size:24px;line-height:115%;font-family:標楷體">點數放棄切結書</span></strong></p><p style="text-indent: 32px; line-height: 3em;"><span style="font-size:19px;line-height:115%;font-family:標楷體">本人<span style="text-decoration:underline;">　&nbsp;&nbsp;&nbsp; &nbsp;&nbsp; </span>知曉於數字科技股份有限公司旗下站台591房屋交易網帳號<span style="text-decoration:underline;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>內尚有剩餘點數，本人在此聲明：</span></p><p style="margin-left: 56px; line-height: 3em;"><span style="font-size:19px;line-height:115%;font-family:標楷體">1. </span><span style="font-size:19px;line-height:115%;font-family:標楷體">上述帳號及剩餘點數確屬本人所有；</span></p><p style="margin-left: 56px; line-height: 3em;"><span style="font-size:19px;line-height:115%;font-family:標楷體">2. </span><span style="font-size:19px;line-height:115%;font-family:標楷體">本人因故不願提領，願意放棄上述帳號內所有點數，並清楚點數一經刪除無法回復；</span></p><p style="margin-left: 56px; line-height: 3em;"><span style="font-size:19px;line-height:115%;font-family:標楷體">3. </span><span style="font-size:19px;line-height:115%;font-family:標楷體">日後若因放棄點數造成之一切糾紛，與貴公司無涉，本人願負一切法律責任。</span></p><p style="margin-left: 32px; line-height: 3em;"><span style="font-size:19px;line-height:115%;font-family:標楷體">恐口說無憑，特此具書。</span></p><p style="text-indent: 32px; line-height: 3em;"><span style=";font-family:標楷體">此致 </span></p><p style="line-height: 3em;"><span style=";font-family:標楷體">數字科技股份有限公司 591房屋交易網</span></p><p style="line-height: 3em;"><span style="font-size:19px;font-family:標楷體">&nbsp;</span></p><p style="line-height: 3em;"><span style="font-size:19px;font-family:標楷體">立 書 人(</span><span style="font-size:19px;font-family:標楷體">簽章</span><span style="font-size:19px;font-family:標楷體">)</span><span style="font-size:19px;font-family:標楷體">:</span></p><p style="line-height: 3em;"><span style="font-size:19px;line-height:115%;font-family:標楷體">身分證字號:</span></p><p style="line-height: 3em;"><span style="font-size:19px;line-height:115%;font-family:標楷體">地&nbsp;&nbsp;&nbsp; 址:</span></p><p style="line-height: 3em;"><span style="font-size:19px;line-height:115%;font-family:標楷體">聯絡電話:</span></p><p style="line-height: 3em;"><span style="font-size:19px;font-family:標楷體">&nbsp;</span></p><p style="line-height: 3em;"><span style="font-size:19px;font-family:標楷體">立書時間:&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp; &nbsp;月&nbsp;&nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日</span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">曹超雲</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-27 10:54:47</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><span style="font-size: 16px;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">刪除個資回覆話術參考：</span></strong></span><span style="font-family: 微软雅黑, sans-serif; color: #7F7F7F;">&lt;帳號是會員本人註冊的，現在不需要使用了&gt;</span></p><p><br></p><p><span style="font-size: 14px;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></strong></span></p><table style="width: 800px;" data-sort="sortDisabled" width="984" cellspacing="0" cellpadding="0"><colgroup><col style=";width:149px" width="149"><col style=";width:835px" width="834"></colgroup><tbody><tr class="firstRow"><td colspan="1" rowspan="10"><span style="font-size: 14px;">開頭</span></td><td colspan="1" rowspan="1"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服：客服部您好，蔽姓X，很高興為您服務~</span></td></tr><tr style=";height:33px" height="33"><td style="" width="835"><span style="font-size: 14px;">會員：小姐你好，我想刪除我的賬號個人資料。</span></td></tr><tr style=";height:33px" height="33"><td style="" height="33"><span style="font-size: 14px;">客服：您好，請您提供一下您註冊的行動電話，客服幫您查詢看看唷~</span></td></tr><tr style=";height:33px" height="33"><td style="" height="33"><span style="font-size: 14px;">會員：09xxxxxxx</span></td></tr><tr style=";height:33px" height="33"><td style="" height="33"><span style="font-size: 14px;">客服：好的，請問您賬號登記的真實姓名是哪一位呢？</span></td></tr><tr style=";height:33px" height="33"><td style="" height="33"><span style="font-size: 14px;">會員：xxxx</span></td></tr><tr style=";height:33px" height="33"><td style="" height="33"><span style="font-size: 14px;">客服：請問您是什麼原因需要刪除賬號呢？</span></td></tr><tr style=";height:33px" height="33"><td style="" height="33"><span style="font-size: 14px;">會員：我房子賣掉了，現在不用591了，幫我把資料都刪除掉</span></td></tr><tr style=";height:33px" height="33"><td style="" height="33"><span style="font-size: 14px;">客服：好的~客服需要回撥您這隻行動電話，跟您核實賬號內的三項資料，請問這隻賬號的行動電話是您本人使用的嗎？</span></td></tr><tr style=";height:33px" height="33"><td style="" height="33"><span style="font-size: 14px;">會員：是的，是我在使用/不是，是我家人的</span></td></tr><tr style=";height:44px" height="44"><td rowspan="3" style="" height="278"><span style="font-size: 14px;">本人使用</span></td><td><span style="font-size: 14px;">客服：瞭解，那客服先回撥行動電話確認本人，並與您核實賬號資料後，方可為您處理唷，請問您現在方便接聽電話嗎?</span></td></tr><tr style=";height:35px" height="35"><td style="" width="835" height="35"><span style="font-size: 14px;">會員：可以接聽/電話現在沒有使用了</span></td></tr><tr style=";height:199px" height="199"><td style="" width="835" height="199"><p><span style="font-size: 14px;">客服：（分2種情況）</span></p><p><span style="font-size: 14px;">a）好的，那客服先掛斷，稍後客服回撥您的行動電話，請您留意接聽唷~<span style="font-size: 14px; color: #C00000;">（回撥核實賬號內三項資料）</span></span></p><p><span style="font-size: 14px;">b) &nbsp; 不好意思，若您的電話現在沒有使用了，需麻煩您<span style="font-size: 14px; color: #C00000;">將身份證正反面+健保卡</span>傳真或email至客服信箱，核實身份後，客服方可提交專員註銷賬號唷~</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #C00000;">若會員不願意提供</span></p><p><span style="font-size: 14px; color: #4F81BD;">很抱歉唷，為保證用戶的個資安全，註銷賬號需要核實身份後才能處理，若您現在不方便提供，客服可幫您先將賬號停權，待您提供證件後，客服再協助您處理唷~</span></p></td></tr><tr style=";height:44px" height="44"><td style="" height="44"><span style="font-size: 14px;">非本人使用</span></td><td style="" width="835"><span style="font-size: 14px;">不好意思唷，若需要刪除賬號，需麻煩您請註冊本人來電，客服核實賬號資料後，方可處理唷~</span></td></tr></tbody></table><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">曹超雲</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-27 11:16:08</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">註銷手機回覆話術參考：</span></strong><span style="font-family: 微软雅黑, sans-serif; color: #A5A5A5;">&lt;自己的手機被別人註冊了，或者自己想用這個手機註冊一個新帳號，不需要刪除原帳號的資料&gt;</span></p><p><br></p><table style="width: 800px;" data-sort="sortDisabled" width="844" cellspacing="0" cellpadding="0"><colgroup><col style=";width:140px" width="140"><col style=";width:704px" width="704"></colgroup><tbody><tr class="firstRow"><td colspan="1" rowspan="8"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">開頭</span></td><td colspan="1" rowspan="1"><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服：客服部您好，蔽姓X，很高興為您服務~</span></td></tr><tr style=";height:33px" height="33"><td style="" width="704"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員：小姐你好，我在註冊你們的會員，但是上面提示我的行動電話已經被註冊了</span></td></tr><tr style=";height:33px" height="33"><td style="" height="33"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服：您好，請您提供一下您的行動電話，客服幫您查詢看看~</span></td></tr><tr style=";height:33px" height="33"><td style="" height="33"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員：09xxxxxxxxx</span></td></tr><tr style=";height:33px" height="33"><td style="" height="33"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服：好的~請問您這個賬號登記的姓名是哪一位呢？</span></td></tr><tr style=";height:33px" height="33"><td style="" height="33"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員：不記得了，應該不是我註冊的</span></td></tr><tr style=";height:33px" height="33"><td style="" height="33"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服：方便請問先生/小姐您貴姓，怎麼稱呼您呢？（方便請問先生/小姐您的全名是？）</span></td></tr><tr style=";height:33px" height="33"><td style="" height="33"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員：我姓王，全名是王曉敏</span></td></tr><tr style=";height:93px" height="93"><td rowspan="8" style="" height="454"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">若核實資料是本人的</span></td><td><p><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">情況1</span></strong></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服：客服查詢這個賬號登記的姓名跟您的姓名不一致唷，但賬號登記的也是王小姐，可能登記的是家人的名字，或您有填寫其他的姓名嗎？您方便提供一下嗎？客服跟您核實看看~<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #C00000;">（可引導會員說家人的名字或其他的名字幫核實，但不可直接告訴他登記的全名）</span></span></p></td></tr><tr style=";height:33px" height="33"><td style="" height="33"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員：王曉花嗎？</span></td></tr><tr style=";height:33px" height="33"><td style="" height="33"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服：是的，客服核實註冊姓名是王曉花小姐，請問是您本人嗎？還是您的家人註冊的呢？</span></td></tr><tr style=";height:33px" height="33"><td style="" height="33"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員：是我啦，我改過名字（或：是我媽媽，她不會用電腦，我幫她用的）</span></td></tr><tr style="height:23px" height="23"><td style="" height="23"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服：有瞭解，請問您這個手機現在還有在使用嗎？方便接聽或者接受簡訊嗎？</span></td></tr><tr style="height:23px" height="23"><td style="" height="23"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員：有在用的</span></td></tr><tr style=";height:77px" height="77"><td style="" height="77"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服：好的，因為是您本人註冊（您幫家人註冊），手機也有正常使用中，客服建議您可以直接使用這個賬號，無需重新註冊唷，網站有提供忘記密碼的功能，您通過行動電話重新設定密碼再使用唷~</span></td></tr><tr style="height:138px" height="138"><td style="" width="704" height="138"><p><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">情況2：</span></strong></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服查詢您這個賬號登記的就是您的名字唷，應該是您先前有註冊過的，您忘記了，建議您可以直接使用這個賬號，無需重新註冊唷，網站有提供忘記密碼的功能，您通過行動電話重新設定密碼再使用唷~<br><br></span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #C00000;">若會員堅持要註銷，重新再註冊</span></p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #548DD4;">好的，客服稍後回撥您這隻行動電話，確認本人之後，再幫您提交專員註銷唷，請問您的電話方便接聽嗎？<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #C00000;">（<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">回撥時需核實三項資料方可提交主任處理</span>）&nbsp; </span>&nbsp;</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #4F81BD;">&nbsp;</span></td></tr><tr style="height:23px" height="23"><td rowspan="8" style="" height="276"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">若核實資料不是本人的</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服：客服查詢這個註冊姓名不是您提供的名字唷？請問這隻行動電話是您本人在使用嗎？</span></td></tr><tr style="height:23px" height="23"><td style="" height="23"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員：是的</span></td></tr><tr style="height:23px" height="23"><td style="" height="23"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服：好的，客服查詢這個註冊名字也是王小姐，但您提供的全名最後一個字不正確唷</span></td></tr><tr style="height:23px" height="23"><td style="" height="23"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">請問您是否有其他名字或者您方便提供下您家人的相關名字嗎？客服幫您核實看看。</span></td></tr><tr style="height:23px" height="23"><td style="" height="23"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員：沒有呢，只有這個名字<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #C00000;">（會員提供了其他的名字都對不上）</span></span></td></tr><tr style="height:46px" height="46"><td style="" height="46"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服：很抱歉，由於您提供的名字都無法核實，請問您這個手機是什麼時候開始使用的呢？客服幫您核實下該賬號註冊的時間？</span></td></tr><tr style="height:23px" height="23"><td style="" height="23"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員：這個是我剛用1年的手機號碼</span></td></tr><tr style="height:69px" height="69"><td style="" height="69"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">客服：瞭解，核實到該賬號註冊時間比較久，已經超過您使用的時間唷，可能是之前別人使用該手機註冊了，請您不要擔心，客服稍後回撥這隻行動電話給您，確認是您本人後，提交專員註銷此賬號，您再重新註冊即可唷~</span></td></tr></tbody></table><p><br></p><p><br></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">PS：若會員不需要註銷，則也需將此賬號停權</span></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/upload/head/10217.png?time=1548059679">&nbsp;&nbsp;&nbsp;
              <span class="user">黃興興</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-12-15 14:09:40</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin-top:5px;margin-right:0;margin-bottom:5px;margin-left: 0"><strong><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">刪除個資特殊案例記錄：</span></strong></p><p style="margin: 5px 0"><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">ID</span><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">：1121164會員L先生于2020/9/8日致電臺灣公司，表示不接受我們最新版服務條款的內容（覺得我們收集資料的範圍超出合理值，建議我們參考NCC的條款。因臺灣同事表示會轉交客服處理，對方諮詢客服的辦公地點後，強烈拒絕與客服溝通），要求取消刊登中的廣告，退還刊登費，並刪除他的個人資料。由於之前客服回電他未接聽，故再次於12月致電臺灣公司處理此問題。此時其廣告刊登時間已過期。</span></p><p style="margin: 5px 0"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 13px;">經過討論，我們同意退款，但其不接受我們的退款流程（在網頁上填寫退款資料與來公司核實退款資料），與個資處理流程。</span></p><p style="margin: 5px 0"><br></p><p style="margin: 5px 0"><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">過程中我們確認過的處理方式有：</span></p><p style="margin: 5px 0"><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">1</span><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">、請會員到臺灣公司現場辦理退款與刪除個資事宜，我們不影印他的資料；</span></p><p style="margin: 5px 0"><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">2</span><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">、在電話中與其核對資料後，請會計直接匯款給他，核對的資料當初儲值卡片相同的銀行名稱、分行名稱、帳號資料、戶名，同時我們需要幫他操作線上申請退款（不會輸入真實資料，但需要讓系統生成折讓單）。等他收到款項後，我們會刪除他的個資；</span></p><p style="margin: 5px 0"><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">3</span><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">、他有提出不要那400元了，直接刪帳號（臺灣同事擔心後續有糾紛，不建議這樣操作）。</span></p><p style="margin: 5px 0"><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">&nbsp;</span></p><p style="margin: 5px 0"><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">因以上方案會員皆不接受。<strong>最終處理方式</strong>為：</span></p><p style="margin: 5px 0"><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">1</span><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">、需退還的400元：用<span style="font-size: 13px; font-family: &quot;Microsoft YaHei&quot;, sans-serif; color: #FF0000;"><strong>法院提存</strong></span>的方式處理。我們提供會員基本資料給法務，法務會走流程，等有案號了再傳簡訊通知對方，對方可在10年內至法院辦理提存。</span></p><p style="margin: 5px 0"><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">2</span><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">、帳號個資的刪除：用591的測試手機傳簡訊至該會員註冊手機，請對方確認要註銷時進行回覆，我們收到再進行刪除作業。</span></p><p style="margin: 5px 0"><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">&nbsp;</span></p><p style="margin: 5px 0"><strong><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">提存內容：</span></strong></p><p style="margin: 5px 0"><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">其要退費之400元已於民國109年12月7日至臺灣新北地方法院進行提存，提存案號：109年度存字第22**號，司法院已進行公告，其可自行上網查看，並可隨時向臺灣新北地方法院領取提存款項，其需向法院提出證明其為受取權人（手機號碼0931-***-***之所有權人證明　或　繳款新台幣400元至(013)89280931******之繳費證明），至於其他相關法院領取行政程式請洽法院提存所，新北地院提存所電話02-22616714分機1020~1024及1026。</span></p><p style="margin: 5px 0"><span style="color: #A5A5A5;"><span style="font-size: 13px; font-family: &quot;Microsoft YaHei&quot;, sans-serif;">PS：</span><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 13px;">依民法第 328 條 提存後，給付物毀損、滅失之危險，由債權人(賴先生)負擔，債務人(591)亦無須支付利息，或賠償其孳息未收取之損害。</span></span></p><p style="margin: 5px 0"><span style="color: #A5A5A5;"><span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 13px;"><br></span></span></p><p style="margin: 5px 0"><strong><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">簡訊內容：</span></strong></p><p style="margin: 5px 0"><span style="font-size: 13px;font-family: 'Microsoft YaHei', sans-serif">【591】會員ID.1121164 <span style="font-family: &quot;Microsoft YaHei&quot;, sans-serif; font-size: 13px;">L</span>先生(註冊手機:　)您好，我們已收到您的刪除個人資料需求，若您為帳號所有人且同意依個人資料保護法第11條第3項:「個人資料蒐集之特定目的消失或期限屆滿時，應主動或依當事人之請求，刪除、停止處理或利用該個人資料。但因執行職務或業務所必須或經當事人書面同意者，不在此限。」向591申請刪除個人資料，請您使用此註冊手機輸入「1」回傳，我們收到後盡快處理。提醒您，刪除個人資料的帳號將被永久停權。若您非會員本人或並未提出刪除個人資料之申請，請忽略此簡訊，謝謝。</span></p><p><span style="font-size: 13px; font-family: &quot;Microsoft YaHei&quot;, sans-serif; color: #FF0000;">PS：法務要求後續刪除個資的流程，皆參照以上文本。</span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li></ul> <h3 class="more-answer">我要補充</h3> <div style=""><div id="editor_17964237300284724" class="edui-default" style=""><div id="edui1" class="edui-editor  edui-default" style="width: 100%; z-index: 999;"><div id="edui1_toolbarbox" class="edui-editor-toolbarbox edui-default"><div id="edui1_toolbarboxouter" class="edui-editor-toolbarboxouter edui-default"><div class="edui-editor-toolbarboxinner edui-default"><div id="edui2" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui2&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui3" class="edui-box edui-button edui-for-fullscreen edui-default"><div id="edui3_state" onmousedown="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui3_body" unselectable="on" title="全屏" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui3&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui3&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui4" class="edui-box edui-button edui-for-source edui-default"><div id="edui4_state" onmousedown="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui4_body" unselectable="on" title="源代码" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui4&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui4&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui5" class="edui-box edui-button edui-for-undo edui-default"><div id="edui5_state" onmousedown="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui5_body" unselectable="on" title="撤销" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui5&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui5&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui6" class="edui-box edui-button edui-for-redo edui-default"><div id="edui6_state" onmousedown="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui6_body" unselectable="on" title="重做" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui6&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui6&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui7" class="edui-box edui-button edui-for-simpleupload edui-default"><div id="edui7_state" onmousedown="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui7_body" unselectable="on" title="单图上传" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui7&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui7&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui8" class="edui-box edui-button edui-for-imageleft edui-default"><div id="edui8_state" onmousedown="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui8_body" unselectable="on" title="左浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui8&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui8&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui9" class="edui-box edui-button edui-for-imageright edui-default"><div id="edui9_state" onmousedown="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui9_body" unselectable="on" title="右浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui9&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui9&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui10" class="edui-box edui-button edui-for-imagecenter edui-default"><div id="edui10_state" onmousedown="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui10_body" unselectable="on" title="居中" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui10&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui10&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui11" class="edui-box edui-separator  edui-default"></div><div id="edui12" class="edui-box edui-button edui-for-date edui-default"><div id="edui12_state" onmousedown="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui12_body" unselectable="on" title="日期" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui12&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui12&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui13" class="edui-box edui-button edui-for-time edui-default"><div id="edui13_state" onmousedown="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui13_body" unselectable="on" title="时间" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui13&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui13&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui14" class="edui-box edui-button edui-for-horizontal edui-default"><div id="edui14_state" onmousedown="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui14_body" unselectable="on" title="分隔线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui14&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui14&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui15" class="edui-box edui-separator  edui-default"></div><div id="edui16" class="edui-box edui-splitbutton edui-for-inserttable edui-default"><div title="插入表格" id="edui16_state" onmousedown="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui16_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onArrowClick();"></div></div></div></div><div id="edui19" class="edui-box edui-button edui-for-deletetable edui-default"><div id="edui19_state" onmousedown="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui19_body" unselectable="on" title="删除表格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui19&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui19&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui20" class="edui-box edui-button edui-for-insertrow edui-default"><div id="edui20_state" onmousedown="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui20_body" unselectable="on" title="前插入行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui20&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui20&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui21" class="edui-box edui-button edui-for-insertcol edui-default"><div id="edui21_state" onmousedown="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui21_body" unselectable="on" title="前插入列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui21&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui21&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui22" class="edui-box edui-button edui-for-mergeright edui-default"><div id="edui22_state" onmousedown="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui22_body" unselectable="on" title="右合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui22&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui22&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui23" class="edui-box edui-button edui-for-mergedown edui-default"><div id="edui23_state" onmousedown="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui23_body" unselectable="on" title="下合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui23&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui23&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui24" class="edui-box edui-button edui-for-deleterow edui-default"><div id="edui24_state" onmousedown="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui24_body" unselectable="on" title="删除行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui24&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui24&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui25" class="edui-box edui-button edui-for-deletecol edui-default"><div id="edui25_state" onmousedown="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui25_body" unselectable="on" title="删除列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui25&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui25&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui26" class="edui-box edui-button edui-for-splittorows edui-default"><div id="edui26_state" onmousedown="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui26_body" unselectable="on" title="拆分成行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui26&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui26&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui27" class="edui-box edui-button edui-for-splittocols edui-default"><div id="edui27_state" onmousedown="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui27_body" unselectable="on" title="拆分成列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui27&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui27&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui28" class="edui-box edui-button edui-for-splittocells edui-default"><div id="edui28_state" onmousedown="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui28_body" unselectable="on" title="完全拆分单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui28&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui28&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui29" class="edui-box edui-button edui-for-mergecells edui-default"><div id="edui29_state" onmousedown="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui29_body" unselectable="on" title="合并多个单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui29&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui29&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui36" class="edui-box edui-button edui-for-edittable edui-default"><div id="edui36_state" onmousedown="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui36_body" unselectable="on" title="表格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui36&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui36&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui41" class="edui-box edui-button edui-for-edittd edui-default"><div id="edui41_state" onmousedown="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui41_body" unselectable="on" title="单元格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui41&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui41&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui42" class="edui-box edui-separator  edui-default"></div><div id="edui43" class="edui-box edui-menubutton edui-for-lineheight edui-default"><div title="行间距" id="edui43_state" onmousedown="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui43_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onArrowClick();"></div></div></div></div><div id="edui52" class="edui-box edui-menubutton edui-for-rowspacingtop edui-default"><div title="段前距" id="edui52_state" onmousedown="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui52_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onArrowClick();"></div></div></div></div><div id="edui59" class="edui-box edui-menubutton edui-for-rowspacingbottom edui-default"><div title="段后距" id="edui59_state" onmousedown="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui59_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onArrowClick();"></div></div></div></div></div><div id="edui66" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui66&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui67" class="edui-box edui-combox edui-for-fontfamily edui-default"><div title="字体" id="edui67_state" onmousedown="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui67_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onButtonClick(event, this);">字体</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onArrowClick();"></div></div></div></div><div id="edui80" class="edui-box edui-combox edui-for-customstyle edui-default"><div title="自定义标题" id="edui80_state" onmousedown="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui80_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onButtonClick(event, this);">自定义标题</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onArrowClick();"></div></div></div></div><div id="edui86" class="edui-box edui-combox edui-for-paragraph edui-default"><div title="段落格式" id="edui86_state" onmousedown="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui86_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onButtonClick(event, this);">段落格式</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onArrowClick();"></div></div></div></div><div id="edui95" class="edui-box edui-combox edui-for-fontsize edui-default"><div title="字号" id="edui95_state" onmousedown="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui95_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onButtonClick(event, this);">字号</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onArrowClick();"></div></div></div></div><div id="edui110" class="edui-box edui-button edui-for-link edui-default"><div id="edui110_state" onmousedown="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui110_body" unselectable="on" title="超链接" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui110&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui110&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui111" class="edui-box edui-button edui-for-indent edui-default"><div id="edui111_state" onmousedown="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui111_body" unselectable="on" title="首行缩进" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui111&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui111&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui112" class="edui-box edui-button edui-for-bold edui-default"><div id="edui112_state" onmousedown="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui112_body" unselectable="on" title="加粗" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui112&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui112&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui113" class="edui-box edui-splitbutton edui-for-forecolor edui-default edui-colorbutton"><div title="字体颜色" id="edui113_state" onmousedown="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui113_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui113_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onArrowClick();"></div></div></div></div><div id="edui116" class="edui-box edui-splitbutton edui-for-backcolor edui-default edui-colorbutton"><div title="背景色" id="edui116_state" onmousedown="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui116_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui116_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onArrowClick();"></div></div></div></div><div id="edui119" class="edui-box edui-button edui-for-italic edui-default"><div id="edui119_state" onmousedown="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui119_body" unselectable="on" title="斜体" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui119&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui119&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui120" class="edui-box edui-button edui-for-underline edui-default"><div id="edui120_state" onmousedown="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui120_body" unselectable="on" title="下划线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui120&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui120&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui121" class="edui-box edui-button edui-for-strikethrough edui-default"><div id="edui121_state" onmousedown="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui121_body" unselectable="on" title="删除线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui121&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui121&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui122" class="edui-box edui-button edui-for-superscript edui-default"><div id="edui122_state" onmousedown="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui122_body" unselectable="on" title="上标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui122&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui122&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui123" class="edui-box edui-button edui-for-subscript edui-default"><div id="edui123_state" onmousedown="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui123_body" unselectable="on" title="下标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui123&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui123&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui124" class="edui-box edui-button edui-for-removeformat edui-default"><div id="edui124_state" onmousedown="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui124_body" unselectable="on" title="清除格式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui124&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui124&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui125" class="edui-box edui-button edui-for-blockquote edui-default"><div id="edui125_state" onmousedown="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui125_body" unselectable="on" title="引用" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui125&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui125&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui126" class="edui-box edui-button edui-for-pasteplain edui-default"><div id="edui126_state" onmousedown="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui126_body" unselectable="on" title="纯文本粘贴模式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui126&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui126&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui127" class="edui-box edui-menubutton edui-for-insertorderedlist edui-default"><div title="有序列表" id="edui127_state" onmousedown="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui127_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onArrowClick();"></div></div></div></div><div id="edui140" class="edui-box edui-menubutton edui-for-insertunorderedlist edui-default"><div title="无序列表" id="edui140_state" onmousedown="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui140_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onArrowClick();"></div></div></div></div><div id="edui147" class="edui-box edui-button edui-for-cleardoc edui-default"><div id="edui147_state" onmousedown="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui147_body" unselectable="on" title="清空文档" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui147&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui147&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div></div></div></div><div id="edui1_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;"><div id="edui1_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui1&quot;].showWordImageDialog();">点击上传</div><div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui1&quot;].hideToolbarMsg();">x</div><div id="edui1_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div><div style="height:0;overflow:hidden;clear:both;" class="edui-default"></div></div><div id="edui1_message_holder" class="edui-editor-messageholder edui-default"></div></div><div id="edui1_iframeholder" class="edui-editor-iframeholder edui-default" style="width: 100%; height: 300px; z-index: 999; overflow: hidden;"><iframe id="ueditor_0" width="100%" height="100%" frameborder="0" src="javascript:void(function(){document.open();document.write(&quot;<!DOCTYPE html><html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}
body{margin:8px;font-family:sans-serif;font-size:13px;}p{margin:5px 0;}</style><link rel='stylesheet' type='text/css' href='/static/ueditor/themes/iframe.css'/></head><body class='view' ></body><script type='text/javascript'  id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant0'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);</script></html>&quot;);document.close();}())"></iframe></div><div id="edui1_bottombar" class="edui-editor-bottomContainer edui-default"><table class="edui-default"><tbody class="edui-default"><tr class="edui-default"><td id="edui1_elementpath" class="edui-editor-bottombar edui-default"><div class="edui-editor-breadcrumb">元素路径:</div></td><td id="edui1_wordcount" class="edui-editor-wordcount edui-default">字数统计</td><td id="edui1_scale" class="edui-editor-scale edui-default" style="display: none;"><div class="edui-editor-icon edui-default"></div></td></tr></tbody></table></div><div id="edui1_scalelayer" class="edui-default"></div></div></div> <div class="edit-btn"><button type="button" class="button">提交回答</button> <label data-v-0fe26aab="" for="commonCheckbox" class="checkbox"><input data-v-0fe26aab="" type="checkbox" id="commonCheckbox" name="commonCheckbox"> <i data-v-0fe26aab=""></i> 需要彈窗，請勾選</label></div></div></div> <div class="index-right"><div class="right-bk"><h3 class="com-title"><a href="javascript:;">我的最爱</a></h3> <div class="item-box clearfix"><span>暫無數據</span></div></div> <div class="hotTag right-bk"><h3 class="com-title">热门标签</h3> <div class="hotTag-box item-box clearfix"><p>無數據</p></div></div> <div class="right-bk rank"><h3 class="com-title"><a href="javascript:;">冠軍排行榜</a></h3> <ul><li>暫無數據</li></ul></div> <div class="activeUser right-bk"><h3 class="com-title">活跃用户</h3> <div class="activeUser-box"><a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a></div></div> <img src="data:image/png;base64,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" class="to-top"></div></div> <!----> <!----> <!----> <div class="page-cnt footer"><p>T5客服知识库<span>|</span>zsk.591.com.tw</p></div></div></div><script type="text/javascript" src="/js/manifest.f192b.js"></script><script type="text/javascript" src="/js/vendor.17570.js"></script><script type="text/javascript" src="/js/app.58254.js"></script><div id="edui_fixedlayer" class="edui-default" style="position: fixed; left: 0px; top: 0px; width: 0px; height: 0px;"><div id="edui148" class="edui-popup  edui-bubble edui-default" onmousedown="return false;" style="display: none;"> <div id="edui148_body" class="edui-popup-body edui-default"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank" class="edui-default"></iframe> <div class="edui-shadow edui-default"></div> <div id="edui148_content" class="edui-popup-content edui-default">  </div> </div></div></div><div id="nprogress"><div class="bar" role="bar" style="transform: translate3d(-30.5682%, 0px, 0px); transition: 500ms;"><div class="peg"></div></div></div></body></html>