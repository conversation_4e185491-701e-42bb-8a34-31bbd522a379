<html class=" "><head><meta charset="utf-8"><title>591客服知識庫</title><link rel="stylesheet" href="/static/font/font-awesome.min.css"><link href="/css/app.e5aa4.css" rel="stylesheet"><script type="text/javascript" charset="utf-8" async="" src="/js/questionDetail.d0a6b.js"></script><style type="text/css">.que-detail-page .index-left .button{width:74px;height:32px;line-height:32px;font-size:13px;margin-right:10px}.que-detail-page .index-left .button:hover{color:#fff}.que-detail-page .detail-box{padding:20px 30px;border:1px solid #e5e5e5;border-radius:4px}.que-detail-page .detail-box h3{font-size:18px;color:#333;margin-bottom:10px}.que-detail-page .detail-box h3 span{color:#f77d24}.que-detail-page .detail-box .add-fav{cursor:pointer}.que-detail-page .d-b-menu{margin:15px 0 25px;font-size:13px;color:#999}.que-detail-page .d-b-menu b{display:inline-block;font-weight:400;margin:0 10px}.que-detail-page .d-b-menu .r{float:right;color:#f77d24}.que-detail-page .d-b-menu .r .fa{margin-right:5px;font-size:16px}.que-detail-page .d-b-cnt{margin-bottom:25px;font-size:13px;color:#666}.que-detail-page .submit-ask{display:block;margin:32px auto 0}.que-detail-page .reply-list{margin:30px 0 10px;overflow:hidden}.que-detail-page .reply-list .reply-item{padding:15px 30px 25px;margin-bottom:30px;border-radius:4px;border:1px solid #e5e5e5}.que-detail-page .reply-list .reply-item h4{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;line-height:50px;font-size:13px}.que-detail-page .reply-list .reply-item h4 img{width:40px;height:40px;border-radius:50%}.que-detail-page .reply-list .reply-item h4 .time{color:#999}.que-detail-page .reply-list .reply-item h4 .button{-ms-flex-item-align:center;align-self:center}.que-detail-page .reply-list .reply-item h4 .delete{color:#666;cursor:pointer}.que-detail-page .reply-list .reply-item .reply-detail{padding:20px 0 30px}.que-detail-page .more-answer{font-size:18px;color:#666;margin-bottom:5px}.que-detail-page .editor-content table{width:100%;border:1px solid #ddd}.que-detail-page .editor-content table td,.que-detail-page .editor-content table th{border:1px solid #ddd;padding:2px 8px;line-height:2}.que-detail-page .editor-content img{max-width:100%;vertical-align:text-bottom}.que-detail-page .edit-btn{text-align:center;margin-top:20px}.que-detail-page .edit-btn .edit-cancle{background:#aaa}</style><style type="text/css">.head-info{line-height:26px;background:#f5f5f5;font-size:13px;color:#999}.head-info a{margin-left:20px;color:#333}.head-search{margin-top:22px}.head-search img{float:left}.head-search .srh-box{float:left;margin:20px 0 0 20px}.head-search .srh-box .srh-input{float:left;width:260px;height:38px;line-height:38px;border:2px solid #37a508;border-radius:4px 0 0 4px}.head-search .srh-box a,.head-search .srh-box button{float:left;width:80px;height:38px;line-height:36px;font-size:16px;color:#fff}.head-search .srh-box .srh-btn{border-radius:0 4px 4px 0}.head-search .srh-box .ask-btn{margin-left:10px}.head-nav{float:left;margin:20px 0 0 60px;color:#fff;font-size:16px;line-height:36px}.head-nav a{display:block;height:100%;color:#333}.head-nav li{position:relative;float:left;margin-right:35px}.head-nav .active a,.head-nav li:hover a{color:#30b940}.head-nav .active:after,.head-nav li:hover:after{display:block;content:"";height:3px;width:100%;background:#30b940}.head-pic{width:100%;height:90px;margin-top:20px;background:url(/static/banner.png) top no-repeat}</style><style type="text/css">.top-breadcrumb{height:20px;margin-top:10px;margin-bottom:40px;font-size:13px;color:#666}.top-breadcrumb .t-b-icon{color:#30b940}.top-breadcrumb span{margin-left:8px}.top-breadcrumb span .fa{font-size:16px;margin-left:8px}.top-breadcrumb span:first-child{margin-left:0}.top-breadcrumb .now{color:#30b940}</style><script type="text/javascript" src="/static/ueditor/ueditor.config.js?20210319" id="configScriptTag"></script><script type="text/javascript" src="/static/ueditor/ueditor.all.js?20210319" id="editorScriptTag"></script><style type="text/css">.checkbox[data-v-0fe26aab]{word-spacing:4px;vertical-align:bottom;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.checkbox i[data-v-0fe26aab]{display:inline-block;width:14px;height:14px;border-radius:2px;border:1px solid #ccc;background:#fff;position:relative;top:1px}.checkbox input[data-v-0fe26aab]{opacity:0;width:0;height:0;margin:0}.checkbox input:checked+i[data-v-0fe26aab]{background:#30b940;border-color:#30b940}.checkbox input:checked+i[data-v-0fe26aab]:before{content:"";position:absolute;left:2px;top:1px;width:11px;height:6px;border-bottom:2px solid #fff;border-left:2px solid #fff;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}</style><style type="text/css">.popbox{position:fixed;top:50%;left:50%;z-index:1011;background:#fff;border-radius:6px;overflow:hidden}.popbox h3{font-size:16px;line-height:40px;padding:0 20px}.popbox h3 span{float:right;color:#666;font-size:13px;cursor:pointer}.popbox .pb-cnt{padding:0 20px;font-size:13px}.popbox .pb-btn-area{position:absolute;bottom:0;left:0;z-index:1;width:100%;text-align:right;padding:0 20px 20px 0}.popbox .pb-btn-area button{height:32px;line-height:32px;border:0;background:#fff;cursor:pointer;border-radius:6px;margin-left:15px;padding:0 15px}.popbox .pb-btn-area button.pb-btn-ok{background:#daf3cf;color:#279005}.popbox .pb-btn-area button.pb-btn-ok:hover{background:#279005;color:#fff}</style><style type="text/css">.shadow{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1010;background:#000}</style><style type="text/css">.footer{margin-top:70px;margin-bottom:20px;padding-top:20px;text-align:center;color:#999;border-top:1px solid #f5f5f5}.footer span{margin:0 10px}</style><script src="/static/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript" defer="defer"></script><link href="/static/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet"><script src="/static/ueditor/third-party/codemirror/codemirror.js" type="text/javascript" defer="defer"></script><link rel="stylesheet" type="text/css" href="/static/ueditor/third-party/codemirror/codemirror.css"><script src="/static/ueditor/third-party/zeroclipboard/ZeroClipboard.js" type="text/javascript" defer="defer"></script><style type="text/css">.activeUser,.favorite,.hotTag,.rank{margin-bottom:40px}</style><style type="text/css">.rank .rank-page{height:60px;line-height:60px;font-size:18px;margin-bottom:20px;text-align:center;background:#fafafa;border-radius:5px}.rank ul{padding:0 20px}.rank li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;border-bottom:1px dotted #f5f5f5;line-height:50px}.rank li .flag{position:relative;display:inline-block;width:20px;height:30px;background:#476472;vertical-align:middle;margin-right:12px;color:#fff;text-align:center;line-height:20px;font-size:13px;font-style:normal}.rank li .flag:after{content:"";position:absolute;bottom:-4px;left:-4px;border:14px solid transparent;border-bottom-color:#fff}.rank li:first-child .flag{background:#fb6757}.rank li:nth-child(2) .flag{background:#fac525}.rank li:nth-child(3) .flag{background:#00a9fb}.rank li img{width:36px;height:36px;border-radius:50%;margin-right:12px}</style><style type="text/css">.activeUser .activeUser-box a{display:inline-block;width:64px;height:64px;border-radius:50%;overflow:hidden;margin:0 6px 12px}</style><style type="text/css">.to-top{display:none;position:fixed;bottom:150px;z-index:5;width:50px;height:50px;cursor:pointer}</style></head><body style=""><div id="app" style=""><div class="que-detail-page" style=""><div class="wiki-hd"><div class="head-info"><p class="page-cnt">
      您好，欢迎来到591客服知识库
      <a href="https://www.591.com.tw/admin.php?new_url=https://zsk.591.com.tw">[请登入]</a> <!----></p></div> <div class="page-cnt head-search clearfix"><a href="/" class="router-link-active"><img src="/static/logo.png"></a> <ul class="head-nav clearfix"><li class="router-link-active active"><a href="/">591房屋交易网</a></li> <li class=""><a href="/life">客服充电站</a></li> <li class=""><a href="/notification">通知事项</a></li></ul> <div class="srh-box"><input type="text" placeholder="请输入搜索内容" class="srh-input"> <button type="button" class="button srh-btn">搜索</button> <a href="/ask/591" class="button button-orange ask-btn">提問</a></div></div> <div class="head-pic"></div></div> <div class="top-breadcrumb com-width"><span><a href="/collection/591/1344" class="">基礎入門<i class="fa fa-angle-double-right"></i></a></span><span><a href="/collection/591/1344/1566" class="">網站服務<i class="fa fa-angle-double-right"></i></a></span><span><strong class="now">问题详情</strong></span></div> <div class="com-width clearfix" style=""><div class="index-left" style=""><div class="detail-box"><h3><!---->買房工具</h3> <p class="clearfix d-b-menu"><span class="l">
            文宇&nbsp;&nbsp;&nbsp;浏览180次&nbsp;&nbsp;&nbsp;2019-12-10 10:25:03
          </span> <span class="r"><span class="add-fav"><i class="fa fa-heart"></i>加入最爱</span></span></p> <div class="d-b-cnt editor-content big-img-parent"><div><p>1、最新房貸利率</p><p>2、房貸試算器</p><p>3、社區行情（實價登錄）</p><p>4、找經紀人</p></div></div> <div class="d-b-button"><a href="/edit/591/3903" class="button">编辑</a> <button class="button button-orange">关闭提问</button></div></div> <div class="hr-line"></div> <ul class="reply-list big-img-parent"><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2019-12-11 11:14:14</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong style="box-sizing: border-box; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; box-sizing: border-box;">一、最新房貸利率</span></strong></span></p><p><strong style="box-sizing: border-box; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></strong></p><p><strong><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; box-sizing: border-box;">1、網站<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #4A4A4A; background-color: #FFFFFF;">提供全台&nbsp;36&nbsp;家銀行約&nbsp;142&nbsp;個貸款方案，給房客使用，房客提通過此功能找出最低的房屋貸款利率 ，再考量決定要使用哪一家銀行。</span></span></strong></p><p><span style="font-size: 14px; box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #4A4A4A; background-color: #FFFFFF;"><br></span></p><p><strong><span style="font-size: 14px; box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #4A4A4A; background-color: #FFFFFF;">2、提供的服務如下：</span></strong></p><p><br></p><table cellpadding="0" cellspacing="0" style=""><colgroup><col width="154" style=";width:155px"><col width="127" style=";width:127px"><col width="100" style=";width:100px"><col width="144" style=";width:144px"><col width="165" style=";width:165px"><col width="140" style=";width:140px"><col width="85" style=";width:85px"><col width="105" style=";width:105px"><col width="126" style=";width:127px"><col width="124" style=";width:124px"></colgroup><tbody><tr height="30" style=";height:30px" class="firstRow"><td height="30" width="155" style=""><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">最新房贷率</span></strong></td><td width="127" style=""><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">房貸額度試算</span></strong></td><td width="27" style=""><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">房貸試算</span></strong></td><td width="157" style=""><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">可負擔房貸計算器</span></strong></td><td width="165" style=""><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">房地合一稅試算</span></strong></td><td width="64" style=""><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">海砂屋查詢</span></strong></td><td width="43" style=""><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">購房流程</span></strong></td><td width="105" style=""><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">實價登入查詢</span></strong></td><td width="127" style=""><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">出售行情</span></strong></td><td width="124" style=""><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">找經紀人</span></strong></td></tr><tr height="21" style="height:21px"><td height="21" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">個性化定制方案</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">填寫姓名</span></td><td width="27"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">房貸試算器</span></td><td width="13"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">每月收入</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">持有人身份證</span></td><td width="0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">下載海砂屋列管名冊</span></td><td width="34"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">購房流程</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">地圖查詢</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">591網站刊登行情</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">找經紀業者服務</span></td></tr><tr height="21" style="height:21px"><td height="21" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">首購/非首購</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">行動電話</span></td><td width="27"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">貸款類型</span></td><td width="13"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">房貸占月收入比例</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">特殊原因</span></td><td width="0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">海砂屋介紹</span></td><td width="34"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">所需資料</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">區域查詢</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">縣市/街道</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">可屋主委託</span></td></tr><tr height="21" style="height:21px"><td height="21" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">房屋價格</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">手機驗證碼</span></td><td width="27"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">計算方式</span></td><td width="13"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">貸款期限</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">取得日</span></td><td width="0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">海砂屋的形成說明</span></td><td width="34"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">注意事項</span></td><td><br></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">用途</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">買家/房客委託</span></td></tr><tr height="21" style="height:21px"><td height="21" style="word-break: break-all;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">首付<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #FF0000;">（系統默認20%，可根據自己的需求修改，但無法保存）</span></span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">勾選已閱讀條款</span></td><td width="27"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">貸款總額</span></td><td width="13"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">貸款成數</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">售出日</span></td><td width="0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">海砂屋把關</span></td><td width="34"><br></td><td><br></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">形態</span></td><td><br></td></tr><tr height="21" style="height:21px"><td height="21" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">貸款期限</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">可立即開始試算</span></td><td width="27"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">貸款期限</span></td><td width="13"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">貸款利率</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">出售價格</span></td><td width="0"><br></td><td width="34"><br></td><td><br></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">時間</span></td><td><br></td></tr><tr height="21" style="height:21px"><td height="21" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">軍/公教</span></td><td><br></td><td width="27"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">寬限期</span></td><td width="13"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">開始計算</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">購房價格</span></td><td width="0"><br></td><td width="34"><br></td><td><br></td><td><br></td><td><br></td></tr><tr height="21" style="height:21px"><td height="21" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">指定銀行</span></td><td><br></td><td width="27"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">利率方式</span></td><td width="13"><br></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">相關費用</span></td><td width="0"><br></td><td width="34"><br></td><td><br></td><td><br></td><td><br></td></tr><tr height="21" style="height:21px"><td height="21" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">獲取最新利率</span></td><td><br></td><td width="27"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">年利率</span></td><td width="13"><br></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">土地漲價總額</span></td><td width="0"><br></td><td width="34"><br></td><td><br></td><td><br></td><td><br></td></tr><tr height="21" style="height:21px"><td height="21" colspan="2" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">提供各大銀行最新利率</span></td><td width="27"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">開始計算</span></td><td width="13"><br></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3年內房地交易損失金額</span></td><td width="0"><br></td><td width="34"><br></td><td><br></td><td><br></td><td><br></td></tr></tbody></table><p><br></p><p><br></p><p><br></p><p><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、相關名稱解釋：</span></strong></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #000000;">1）<span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">可負擔房價計算器：<span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">通過輸入你的收入、購屋自備款等信息，計算你可以買多少錢的房子</span><span class="fc-gray" style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; box-sizing: border-box; background-color: #FFFFFF;">（計算結果僅供參考）</span></span></span></p><p><span style="background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #000000;"><span class="fc-gray" style="box-sizing: border-box; background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #000000;">2）</span>房地合一稅試算：房地合一稅指房屋及土地以合並後的實價總額，扣除實際取得成本後，根據您期望的購房要求計算需要課稅多少</span></p><p><span style="background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #000000;">3）房貸計算器：使用我們的房屋貸款計算器，輕鬆估算您的每月付款、完整支付明細等<span class="fc-gray" style="box-sizing: border-box; background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #000000;">（計算結果僅供參考）</span></span></p><p><br></p><p><span style="font-size: 14px; color: #000000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-weight: 700; background-color: #F7F7EF;"></span><br></p><p><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;"><span style="box-sizing: border-box; font-size: 15px; background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;"></span><span style="color: #000000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">二、社區行情（<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px; color: #FF0000;">已改成名為：實價登錄</span>）</span></span></strong></p><p><span style="font-size: 14px;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">1、什麼是實價登錄</span></strong></span><strong><span style="color: #FFFFFF; font-family: Arial, 微軟正黑體, &quot;Microsoft JhengHei&quot;, sans-serif; font-size: 20px; font-weight: 600; background-color: #FFFFFF;">麼是社區行情？</span></strong></p><p><span style="background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #000000;">社區行情是591房屋交易網為用戶提供查詢社區成交價、行情走勢的功能。591通過對內政部實價登錄資料的處理（如剔除特殊交易、拆分車位單價），經過大量數據計算和推演得出社區行情數據。</span></p><p><span style="background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #000000;"><br></span></p><p><strong><span style="background-color: #FFFFFF; color: #000000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、<strong style="font-size: 14px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">實價登錄</span></strong>的目的：</span></strong></p><p><span style="background-color: #FFFFFF; color: #000000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span></p><p style="text-align:left"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">解决因<span style="text-decoration: underline; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">房价不透明</span>造成<span style="text-decoration: underline; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">议价风险</span>的问题，为购房者提供查询、评估<span style="text-decoration: underline; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">房价</span>的方法和数据参考。</span></p><p><br><span style="background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #000000;"></span></p><p><strong><span style="background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #000000;">3、<strong style="font-size: 14px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">實價登錄</span></strong>準確度</span></strong></p><p><span style="color: #000000; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">591社區參考單價平均誤差率低於5%，這意味著大多數時候，社區實際成交價與參考均價相差不超過5%。超過85%的用戶表示591社區行情能提供參考價值。</span></p><p><span style="color: #000000; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;"><br></span></p><p><strong><span style="background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #000000;">4、查詢的內容及方式</span></strong></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; background-color: #FFFFFF;">1）查詢的方式：在實價登錄頻道，輸入社區或街道即可搜尋</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; background-color: #FFFFFF;">目前僅顯示該社區所有的出售物件，無法查看租屋物件，如需查看社區內的租屋廣告方法如下：</span></p><p><span style="text-align: center; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; background-color: #FFFFFF;">進入【租屋】頻道，輸入關鍵字</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="text-align: center; font-size: 14px; background-color: #FFFFFF;">"</span><span style="text-align: center; background-color: #FFFFFF; font-size: 14.6667px; font-family: Calibri, Arial;">新巨蛋<span style="text-align: center; background-color: #FFFFFF; font-size: 14.6667px;"></span></span><span style="text-align: center; background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">"，點擊社區進入即可查看所有的出售物件及出租物件</span></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; background-color: #FFFFFF;">2）查詢的內容：可查詢近1年的平均成交單價，成交筆數、在售筆數、社區實價登入查詢</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; background-color: #FFFFFF;"><br></span></p><p><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; background-color: #FFFFFF;">5、<strong style="font-size: 14px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">實價登錄</span></strong>內容介紹</span></strong></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 14px;">1）出售詳情頁之實價登錄</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 14px;">a、*房成交單價（該廣告刊登幾房，顯示幾房的成交價格）：該社區刊登幾房的平均單價</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 14px;">b、價格區間：此社區最低價格~最高價格顯示</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 14px;">c、社區屋齡：<span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">抓取的是使照時間</span></span></p><p><span style="background-color: #FFFFFF; font-size: 14px; color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; 成交屋龄：抓取的是建照时间</span></p><p><span style="background-color: #FFFFFF; font-size: 14px; color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">d、</span><span style="background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">同社區成交：顯示該社區最近兩筆成交資料（資料來源實價登錄）</span></p><p><span style="background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 14px;">2）實價登錄名詞解釋</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 14px;">a、單價拆分車位：選擇後就會剔除車位的價格</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 14px;">b、特殊交易：<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; color: #333333; font-size: 14px;">不是正常關係的交易，<span style="color: #333333; text-align: center; white-space: pre-wrap; background-color: #FFFFFF; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">系統在內政部網站抓取的，</span>包含（1）<span style="color: #333333; text-align: center; white-space: pre-wrap; background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">親友、員工或其他特殊關係間之交易。（2）含增建或未登記建物。（3）建商與地主合建案。（4）(包含)公共設施保留地之交易。（5）畸零地或有合併使用之交易。（6）向政府機關承購之案件。（7）受債權債務影響或債務抵償之交易。（8）急買急賣。（9）有民情風俗因素之交易。（10）單獨車位交易。（11）土地及建物分次登記案件。前案土地登記收件字號：（12）瑕疵物件之交易。（13）其他 </span></span></span></p><p><span style="color: #333333; text-align: center; white-space: pre-wrap; background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><p><span style="color: #333333; background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3）單價計算方式</span></p><p><span style="color: #333333; background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal; line-height: 1.5em;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">a、每筆單價計算方式為：</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal; line-height: 1.5em;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">單價=（總價-車位價格）/（總坪數-車位坪數）</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal; line-height: 1.5em;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal; line-height: 1.5em;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 14px;">b、社區均價（參考成交單價）計算方式：</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 14px;"></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &nbsp;如果近1年有成交，则取近一年的成交單價中位數<span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; background-color: #FFFFFF;">（排除特殊交易和一樓交易）</span></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &nbsp;如果近1年没有成交，那就取两年的，以最后成交那笔的那个时间，往前推两年（把这两年内的成交进行计算，取單價中位數）</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 14px;">&nbsp; &nbsp;</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&gt;若一年內只有兩筆成交，則取平均數</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="box-sizing: border-box;">&nbsp;&nbsp; &gt;若一年內只有一筆，直接取</span>&nbsp;&nbsp;</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; box-sizing: border-box;">舉例：</span>理想时代大厦</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2021年有成交，那就拿2021的成交计算</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2021没有成交，最后成交那笔是2018年，那就取2018和2017年的成交进行计算</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span><br></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">c、平均銷售單價計算方式：</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &nbsp; &nbsp;取目前在售房屋的單價的中位數</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal;"><span style="box-sizing: border-box; color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &gt;若沒有在售房屋，則顯示“-”</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;&nbsp;</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #262626;">d、實價登錄的區域單價計算規則：</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #262626;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial; color: #262626;">591區域均價：取近一年成交的中位價（住宅大樓）</span> <span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial; color: #262626;">同時我們會抓取樂居均價做比較（防止差幅過大）&nbsp;</span></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #262626;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial; color: #262626;">- 591與樂居：均價差幅小於9% →</span> <span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial; color: #262626;">取591區域均價進行展示</span></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #262626;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial; color: #262626;"> - 591與樂居：均價差幅大於9% →</span> <span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial; color: #262626;">取兩個區域均價平均值進行展示&nbsp;</span></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #262626;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial; color: #262626;">- 若樂居為空值無法對比時</span> <span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial; color: #262626;">→</span> <span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial; color: #262626;">取591區域均價進行展示均價</span></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #262626;">以上都是扣掉車位的數據</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span><br></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><p><strong><span style="color: #333333; font-size: 14px; background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">6、為什麼實價登入部分地區沒有呢？如：<span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; background-color: #FFFFFF;">宜蘭縣、東部</span></span></strong></p><p><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; background-color: #FFFFFF;">因為東部的社區和成交量都比較少，同時也比較難錄到資料（內部知道即可）</span></p><p><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; background-color: #FFFFFF;">若會員詢問可以回覆會員：我們目前還沒做這些地區的，但會記錄優化，未來會有東部的實價登錄信息</span></p><p><span style="color: #333333; font-size: 14px; background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p><strong><span style="color: #333333; font-size: 14px; background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">7、社區資料有誤</span></strong></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #333333; font-size: 13px;">1）首先客服去谷歌核實資料是否一致，如確認不存在了是什麼原因?是改名字了，還是重建？</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2）如果核實到網站社區資料有誤，可按以下方式修改資料</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">例如：如果只是改名字，則直接進入【客服相關】-【資料審核】-【建案審核】，在所有社區內輸入社區名字，找到該社區後，點擊【修改社區資料】即可</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><img src="https://zsk.591.com.tw/upload/question/20191222/1576980724197479.png" alt="image.png" width="809" height="736" style="box-sizing: border-box; border: 0px; vertical-align: text-bottom; max-width: 100%; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif !important; width: 809px; height: 736px;"></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><img src="https://zsk.591.com.tw/upload/question/20191222/1576980510417947.png" alt="image.png" width="807" height="480" style="box-sizing: border-box; border: 0px; vertical-align: text-bottom; max-width: 100%; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif !important; width: 807px; height: 480px;"></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><img src="https://zsk.591.com.tw/upload/question/20191222/1576980559295639.png" alt="image.png" width="809" height="223" style="box-sizing: border-box; border: 0px; vertical-align: text-bottom; max-width: 100%; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif !important; width: 809px; height: 223px;"></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3）如果是重建，則社區的所有資料都不一樣，是不能只修改社區名字，需提交確認後在回電給會員。</span></p><p><br></p><p><span style="font-size: 14px;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">8、實價登錄排序規則</span></strong></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">中古屋詳情頁顯示規則：優先排序<strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #FF0000;">非特殊交易</span></strong>之記錄，其次按成交日期從新到舊排序，一次展示2筆記錄</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">實價登錄頻道顯示規則：按成交日期從新到舊排序，含特殊交易</span></p><p><br></p><p><span style="font-size: 14px;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">9、實價登錄系統自動識別門牌號碼<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #FF0000;">【2020.11.19日上線】</span></span></strong></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1）實價登錄資料均從內政部資料庫抓取的成交記錄<br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2）實價登錄資料<span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">並未公開房屋任何人的個人資料，不存在<span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">侵害隱私權的問題</span></span></span></p><p><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3）網站結合內政部提供的實價登入資料，再運用大數據技術識別到具體門號。</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4）資料有誤需至內政部核實，若網站資料有誤，可提交現場處理，預計2個工作內核實處理</span></p><p><br></p><p><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">10、實價登錄抓取資料頻率分三種：</span></strong></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1）每天最新抓取（參考下圖，每天都在抓當月發佈的實價登錄，因為政府更新時間不固定不可控）</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">執行時間：每天01：13<br></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><img src="https://zsk.591.com.tw/upload/question/20201127/1606444146514785.png" alt="image.png" style="box-sizing: border-box; border: 0px; vertical-align: bottom; max-width: 100%; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif !important;"></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2）每月抓取（每個月抓取1年內的成交數據）</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">每天01：43分，每天抓取更新對應天的縣市ID的季度成交記錄<br></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3）每季度抓取（每個季度抓取1年前的成交數據）</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">每天02：33分，每天抓取</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">每天抓取：按縣市取數據分到月份，按季度取數據分到對應天</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 13px; white-space: normal;"><br><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span></p><p><span style="font-size: 14px; box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #4A4A4A; background-color: #FFFFFF;"></span><span style="color: #000000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>三、找經紀人</strong></span></p><p><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #000000;">1、規則：需進行職業認證及開通店鋪功能方可進入找經紀人頁面</span></strong></p><p><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #000000;">2、搜尋條件</span></strong></p><table cellpadding="0" cellspacing="0" width="877" style="width: 801px;"><colgroup><col width="448" style=";width:448px"><col width="429" style=";width:429px"></colgroup><tbody><tr height="29" style=";height:29px" class="firstRow"><td colspan="2" height="29" width="877" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">找經紀人搜尋條件</span></td></tr><tr height="29" style=";height:29px"><td height="29" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">縣市/街道</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">√</span></td></tr><tr height="29" style=";height:29px"><td height="29" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">業務特長</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">√</span></td></tr><tr height="29" style=";height:29px"><td height="29" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">經紀人名稱</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">√</span></td></tr><tr height="29" style=";height:29px"><td height="29" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">出租速度</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">√</span></td></tr><tr height="29" style=";height:29px"><td height="29" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">出售速度</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">√</span></td></tr><tr height="29" style=";height:29px"><td height="29" style=""><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">瀏覽人數</span></td><td><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">√</span></td></tr></tbody></table><p><br></p><p><span style="color: #000000;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、如何申請進入找經紀人</span></strong></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1）上傳職業認證+個人頭像</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2）填寫店鋪資料</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><p><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4、找經紀人列表頁排序規則</span></strong></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="color: #333333; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; font-size: 14px; background-color: #FFFFFF;">默認排序：登錄時間倒序，物件數倒序，註冊時間倒序，開通時間倒序</span><br style="font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 14px; white-space: normal; background-color: rgb(255, 255, 255);"><span style="color: #333333; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; font-size: 14px; background-color: #FFFFFF;">詳細解析：最近登入會員中心的仲介會員會排序比較前面</span><br style="font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 14px; white-space: normal; background-color: rgb(255, 255, 255);"><span style="color: #333333; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; font-size: 14px; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 會員中心開啟中的物件比較多的會排序比較前面</span><br style="font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 14px; white-space: normal; background-color: rgb(255, 255, 255);"><span style="color: #333333; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; font-size: 14px; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 最近注冊的仲介會員會排序比較前面</span><br style="font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; color: rgb(51, 51, 51); font-size: 14px; white-space: normal; background-color: rgb(255, 255, 255);"><span style="color: #333333; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; font-size: 14px; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 最近開通店鋪的仲介會員會排序比較前面</span></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="color: #333333; font-family: dashicons, &quot;Segoe UI&quot;, &quot;WenQuanYi Micro Hei&quot;, &quot;WenQuanYi Micro Hei Mono&quot;, &quot;Microsoft Yahei&quot;, &quot;Microsoft Yahei Mono&quot;, 微软雅黑, sans-serif; font-size: 14px; background-color: #FFFFFF;"><br></span></span></p><p><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></strong></p><p><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #000000;"></span></strong><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5、找經紀人列表的成交速度：</span></strong></p><p><img src="https://zsk.591.com.tw/upload/question/20220424/1650787770927137.png" alt="image.png" style="white-space: normal;"></p><p>（1）標籤規則：</p><p style="line-height: 1.75em;">&nbsp; &nbsp;<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">① 租屋和售屋"成交速度" 是根據會員賬號內從功能上線後（2011年6月1日）起算的所有過期、成交、已成交再次開啟的物件的【 平均成交天數 】計算</span></p><p style="line-height: 1.75em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &nbsp;② 共劃分為五個成交速度等級（0=" "，1="很快"，2="較快"，3="一般"，4="較慢"）</span></p><p style="line-height: 1.75em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &nbsp;③ 租售各等級成交天數：</span></p><p><img src="/upload/question/20220424/1650788083770770.png" alt="image.png"></p><p><br></p><p style="line-height: 1.75em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">（2）平均成交天數計算方式</span></p><p style="line-height: 1.75em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &nbsp; ①&nbsp; 平均成交天數計算方式是：租屋（售屋）成交總天數 / 租屋（售屋）總成交物件數量</span></p><p style="line-height: 1.75em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #7F7F7F;">&nbsp; （舉例： 會員賬號內2筆出租，一筆10天成交，一筆20天成交，則平均成交天數15天；）</span></p><p style="line-height: 1.75em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &nbsp; ②&nbsp; 當租屋和售屋都有成交時， 總成交等級 = 租屋成交等級 + 售屋成交等級</span></p><p style="line-height: 1.75em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; <span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #7F7F7F;">（舉例：A會員賬號內有刊登出租及出售物件，出租的總筆數平均成交天數是10天，則屬於出租2等級；出售的總筆數平均成交天數是5天，則 屬於出售1等級，那總的成交速度是2 +1 = 3&nbsp; ，属于&nbsp; “较快”）</span></span></p><p style="line-height: 1.75em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &nbsp; ③&nbsp; 當租屋(售屋)無成交時，則總成交等級=售屋（租屋）成交等級</span></p><p style="line-height: 1.75em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><p style="line-height: 1.75em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">（3）其他注意事情</span></p><p style="line-height: 1.75em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &nbsp; ①若租屋（售屋）總成交數 小於3筆 ，不展示 "成交速度" 標簽</span></p><p style="line-height: 1.75em;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &nbsp; ②&nbsp;只有很快、較快展示標簽，其餘不展示標簽</span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li></ul> <h3 class="more-answer">我要補充</h3> <div style=""><div id="editor_78440620687198060" class="edui-default" style=""><div id="edui1" class="edui-editor  edui-default" style="width: 100%; z-index: 999;"><div id="edui1_toolbarbox" class="edui-editor-toolbarbox edui-default"><div id="edui1_toolbarboxouter" class="edui-editor-toolbarboxouter edui-default"><div class="edui-editor-toolbarboxinner edui-default"><div id="edui2" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui2&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui3" class="edui-box edui-button edui-for-fullscreen edui-default"><div id="edui3_state" onmousedown="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui3_body" unselectable="on" title="全屏" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui3&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui3&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui4" class="edui-box edui-button edui-for-source edui-default"><div id="edui4_state" onmousedown="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui4_body" unselectable="on" title="源代码" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui4&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui4&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui5" class="edui-box edui-button edui-for-undo edui-default"><div id="edui5_state" onmousedown="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui5_body" unselectable="on" title="撤销" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui5&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui5&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui6" class="edui-box edui-button edui-for-redo edui-default"><div id="edui6_state" onmousedown="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui6_body" unselectable="on" title="重做" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui6&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui6&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui7" class="edui-box edui-button edui-for-simpleupload edui-default"><div id="edui7_state" onmousedown="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui7_body" unselectable="on" title="单图上传" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui7&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui7&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui8" class="edui-box edui-button edui-for-imageleft edui-default"><div id="edui8_state" onmousedown="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui8_body" unselectable="on" title="左浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui8&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui8&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui9" class="edui-box edui-button edui-for-imageright edui-default"><div id="edui9_state" onmousedown="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui9_body" unselectable="on" title="右浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui9&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui9&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui10" class="edui-box edui-button edui-for-imagecenter edui-default"><div id="edui10_state" onmousedown="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui10_body" unselectable="on" title="居中" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui10&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui10&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui11" class="edui-box edui-separator  edui-default"></div><div id="edui12" class="edui-box edui-button edui-for-date edui-default"><div id="edui12_state" onmousedown="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui12_body" unselectable="on" title="日期" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui12&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui12&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui13" class="edui-box edui-button edui-for-time edui-default"><div id="edui13_state" onmousedown="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui13_body" unselectable="on" title="时间" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui13&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui13&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui14" class="edui-box edui-button edui-for-horizontal edui-default"><div id="edui14_state" onmousedown="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui14_body" unselectable="on" title="分隔线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui14&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui14&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui15" class="edui-box edui-separator  edui-default"></div><div id="edui16" class="edui-box edui-splitbutton edui-for-inserttable edui-default"><div title="插入表格" id="edui16_state" onmousedown="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui16_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onArrowClick();"></div></div></div></div><div id="edui19" class="edui-box edui-button edui-for-deletetable edui-default"><div id="edui19_state" onmousedown="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui19_body" unselectable="on" title="删除表格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui19&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui19&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui20" class="edui-box edui-button edui-for-insertrow edui-default"><div id="edui20_state" onmousedown="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui20_body" unselectable="on" title="前插入行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui20&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui20&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui21" class="edui-box edui-button edui-for-insertcol edui-default"><div id="edui21_state" onmousedown="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui21_body" unselectable="on" title="前插入列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui21&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui21&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui22" class="edui-box edui-button edui-for-mergeright edui-default"><div id="edui22_state" onmousedown="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui22_body" unselectable="on" title="右合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui22&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui22&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui23" class="edui-box edui-button edui-for-mergedown edui-default"><div id="edui23_state" onmousedown="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui23_body" unselectable="on" title="下合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui23&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui23&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui24" class="edui-box edui-button edui-for-deleterow edui-default"><div id="edui24_state" onmousedown="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui24_body" unselectable="on" title="删除行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui24&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui24&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui25" class="edui-box edui-button edui-for-deletecol edui-default"><div id="edui25_state" onmousedown="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui25_body" unselectable="on" title="删除列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui25&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui25&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui26" class="edui-box edui-button edui-for-splittorows edui-default"><div id="edui26_state" onmousedown="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui26_body" unselectable="on" title="拆分成行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui26&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui26&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui27" class="edui-box edui-button edui-for-splittocols edui-default"><div id="edui27_state" onmousedown="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui27_body" unselectable="on" title="拆分成列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui27&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui27&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui28" class="edui-box edui-button edui-for-splittocells edui-default"><div id="edui28_state" onmousedown="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui28_body" unselectable="on" title="完全拆分单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui28&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui28&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui29" class="edui-box edui-button edui-for-mergecells edui-default"><div id="edui29_state" onmousedown="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui29_body" unselectable="on" title="合并多个单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui29&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui29&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui36" class="edui-box edui-button edui-for-edittable edui-default"><div id="edui36_state" onmousedown="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui36_body" unselectable="on" title="表格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui36&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui36&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui41" class="edui-box edui-button edui-for-edittd edui-default"><div id="edui41_state" onmousedown="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui41_body" unselectable="on" title="单元格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui41&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui41&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui42" class="edui-box edui-separator  edui-default"></div><div id="edui43" class="edui-box edui-menubutton edui-for-lineheight edui-default"><div title="行间距" id="edui43_state" onmousedown="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui43_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onArrowClick();"></div></div></div></div><div id="edui52" class="edui-box edui-menubutton edui-for-rowspacingtop edui-default"><div title="段前距" id="edui52_state" onmousedown="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui52_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onArrowClick();"></div></div></div></div><div id="edui59" class="edui-box edui-menubutton edui-for-rowspacingbottom edui-default"><div title="段后距" id="edui59_state" onmousedown="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui59_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onArrowClick();"></div></div></div></div></div><div id="edui66" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui66&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui67" class="edui-box edui-combox edui-for-fontfamily edui-default"><div title="字体" id="edui67_state" onmousedown="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui67_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onButtonClick(event, this);">字体</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onArrowClick();"></div></div></div></div><div id="edui80" class="edui-box edui-combox edui-for-customstyle edui-default"><div title="自定义标题" id="edui80_state" onmousedown="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui80_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onButtonClick(event, this);">自定义标题</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onArrowClick();"></div></div></div></div><div id="edui86" class="edui-box edui-combox edui-for-paragraph edui-default"><div title="段落格式" id="edui86_state" onmousedown="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui86_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onButtonClick(event, this);">段落格式</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onArrowClick();"></div></div></div></div><div id="edui95" class="edui-box edui-combox edui-for-fontsize edui-default"><div title="字号" id="edui95_state" onmousedown="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui95_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onButtonClick(event, this);">字号</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onArrowClick();"></div></div></div></div><div id="edui110" class="edui-box edui-button edui-for-link edui-default"><div id="edui110_state" onmousedown="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui110_body" unselectable="on" title="超链接" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui110&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui110&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui111" class="edui-box edui-button edui-for-indent edui-default"><div id="edui111_state" onmousedown="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui111_body" unselectable="on" title="首行缩进" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui111&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui111&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui112" class="edui-box edui-button edui-for-bold edui-default"><div id="edui112_state" onmousedown="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui112_body" unselectable="on" title="加粗" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui112&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui112&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui113" class="edui-box edui-splitbutton edui-for-forecolor edui-default edui-colorbutton"><div title="字体颜色" id="edui113_state" onmousedown="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui113_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui113_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onArrowClick();"></div></div></div></div><div id="edui116" class="edui-box edui-splitbutton edui-for-backcolor edui-default edui-colorbutton"><div title="背景色" id="edui116_state" onmousedown="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui116_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui116_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onArrowClick();"></div></div></div></div><div id="edui119" class="edui-box edui-button edui-for-italic edui-default"><div id="edui119_state" onmousedown="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui119_body" unselectable="on" title="斜体" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui119&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui119&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui120" class="edui-box edui-button edui-for-underline edui-default"><div id="edui120_state" onmousedown="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui120_body" unselectable="on" title="下划线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui120&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui120&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui121" class="edui-box edui-button edui-for-strikethrough edui-default"><div id="edui121_state" onmousedown="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui121_body" unselectable="on" title="删除线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui121&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui121&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui122" class="edui-box edui-button edui-for-superscript edui-default"><div id="edui122_state" onmousedown="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui122_body" unselectable="on" title="上标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui122&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui122&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui123" class="edui-box edui-button edui-for-subscript edui-default"><div id="edui123_state" onmousedown="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui123_body" unselectable="on" title="下标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui123&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui123&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui124" class="edui-box edui-button edui-for-removeformat edui-default"><div id="edui124_state" onmousedown="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui124_body" unselectable="on" title="清除格式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui124&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui124&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui125" class="edui-box edui-button edui-for-blockquote edui-default"><div id="edui125_state" onmousedown="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui125_body" unselectable="on" title="引用" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui125&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui125&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui126" class="edui-box edui-button edui-for-pasteplain edui-default"><div id="edui126_state" onmousedown="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui126_body" unselectable="on" title="纯文本粘贴模式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui126&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui126&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui127" class="edui-box edui-menubutton edui-for-insertorderedlist edui-default"><div title="有序列表" id="edui127_state" onmousedown="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui127_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onArrowClick();"></div></div></div></div><div id="edui140" class="edui-box edui-menubutton edui-for-insertunorderedlist edui-default"><div title="无序列表" id="edui140_state" onmousedown="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui140_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onArrowClick();"></div></div></div></div><div id="edui147" class="edui-box edui-button edui-for-cleardoc edui-default"><div id="edui147_state" onmousedown="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui147_body" unselectable="on" title="清空文档" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui147&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui147&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div></div></div></div><div id="edui1_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;"><div id="edui1_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui1&quot;].showWordImageDialog();">点击上传</div><div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui1&quot;].hideToolbarMsg();">x</div><div id="edui1_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div><div style="height:0;overflow:hidden;clear:both;" class="edui-default"></div></div><div id="edui1_message_holder" class="edui-editor-messageholder edui-default"></div></div><div id="edui1_iframeholder" class="edui-editor-iframeholder edui-default" style="width: 100%; height: 300px; z-index: 999; overflow: hidden;"><iframe id="ueditor_0" width="100%" height="100%" frameborder="0" src="javascript:void(function(){document.open();document.write(&quot;<!DOCTYPE html><html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}
body{margin:8px;font-family:sans-serif;font-size:13px;}p{margin:5px 0;}</style><link rel='stylesheet' type='text/css' href='/static/ueditor/themes/iframe.css'/></head><body class='view' ></body><script type='text/javascript'  id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant0'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);</script></html>&quot;);document.close();}())"></iframe></div><div id="edui1_bottombar" class="edui-editor-bottomContainer edui-default"><table class="edui-default"><tbody class="edui-default"><tr class="edui-default"><td id="edui1_elementpath" class="edui-editor-bottombar edui-default"><div class="edui-editor-breadcrumb">元素路径:</div></td><td id="edui1_wordcount" class="edui-editor-wordcount edui-default">字数统计</td><td id="edui1_scale" class="edui-editor-scale edui-default" style="display: none;"><div class="edui-editor-icon edui-default"></div></td></tr></tbody></table></div><div id="edui1_scalelayer" class="edui-default"></div></div></div> <div class="edit-btn"><button type="button" class="button">提交回答</button> <label data-v-0fe26aab="" for="commonCheckbox" class="checkbox"><input data-v-0fe26aab="" type="checkbox" id="commonCheckbox" name="commonCheckbox"> <i data-v-0fe26aab=""></i> 需要彈窗，請勾選</label></div></div></div> <div class="index-right"><div class="right-bk"><h3 class="com-title"><a href="javascript:;">我的最爱</a></h3> <div class="item-box clearfix"><span>錯誤，請您登入後再操作！</span></div></div> <div class="hotTag right-bk"><h3 class="com-title">热门标签</h3> <div class="hotTag-box item-box clearfix"><p>無數據</p></div></div> <div class="right-bk rank"><h3 class="com-title"><a href="javascript:;">冠軍排行榜</a></h3> <ul><li>暫無數據</li></ul></div> <div class="activeUser right-bk"><h3 class="com-title">活跃用户</h3> <div class="activeUser-box"><a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a></div></div> <img src="data:image/png;base64,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" class="to-top"></div></div> <!----> <!----> <!----> <div class="page-cnt footer"><p>T5客服知识库<span>|</span>zsk.591.com.tw</p></div></div></div><script type="text/javascript" src="/js/manifest.f192b.js"></script><script type="text/javascript" src="/js/vendor.17570.js"></script><script type="text/javascript" src="/js/app.58254.js"></script><div id="edui_fixedlayer" class="edui-default" style="position: fixed; left: 0px; top: 0px; width: 0px; height: 0px;"><div id="edui148" class="edui-popup  edui-bubble edui-default" onmousedown="return false;" style="display: none;"> <div id="edui148_body" class="edui-popup-body edui-default"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank" class="edui-default"></iframe> <div class="edui-shadow edui-default"></div> <div id="edui148_content" class="edui-popup-content edui-default">  </div> </div></div></div></body></html>