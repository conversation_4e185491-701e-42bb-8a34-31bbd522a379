<html class=" "><head><meta charset="utf-8"><title>591客服知識庫</title><link rel="stylesheet" href="/static/font/font-awesome.min.css"><link href="/css/app.e5aa4.css" rel="stylesheet"><script type="text/javascript" charset="utf-8" async="" src="/js/questionDetail.d0a6b.js"></script><style type="text/css">.que-detail-page .index-left .button{width:74px;height:32px;line-height:32px;font-size:13px;margin-right:10px}.que-detail-page .index-left .button:hover{color:#fff}.que-detail-page .detail-box{padding:20px 30px;border:1px solid #e5e5e5;border-radius:4px}.que-detail-page .detail-box h3{font-size:18px;color:#333;margin-bottom:10px}.que-detail-page .detail-box h3 span{color:#f77d24}.que-detail-page .detail-box .add-fav{cursor:pointer}.que-detail-page .d-b-menu{margin:15px 0 25px;font-size:13px;color:#999}.que-detail-page .d-b-menu b{display:inline-block;font-weight:400;margin:0 10px}.que-detail-page .d-b-menu .r{float:right;color:#f77d24}.que-detail-page .d-b-menu .r .fa{margin-right:5px;font-size:16px}.que-detail-page .d-b-cnt{margin-bottom:25px;font-size:13px;color:#666}.que-detail-page .submit-ask{display:block;margin:32px auto 0}.que-detail-page .reply-list{margin:30px 0 10px;overflow:hidden}.que-detail-page .reply-list .reply-item{padding:15px 30px 25px;margin-bottom:30px;border-radius:4px;border:1px solid #e5e5e5}.que-detail-page .reply-list .reply-item h4{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;line-height:50px;font-size:13px}.que-detail-page .reply-list .reply-item h4 img{width:40px;height:40px;border-radius:50%}.que-detail-page .reply-list .reply-item h4 .time{color:#999}.que-detail-page .reply-list .reply-item h4 .button{-ms-flex-item-align:center;align-self:center}.que-detail-page .reply-list .reply-item h4 .delete{color:#666;cursor:pointer}.que-detail-page .reply-list .reply-item .reply-detail{padding:20px 0 30px}.que-detail-page .more-answer{font-size:18px;color:#666;margin-bottom:5px}.que-detail-page .editor-content table{width:100%;border:1px solid #ddd}.que-detail-page .editor-content table td,.que-detail-page .editor-content table th{border:1px solid #ddd;padding:2px 8px;line-height:2}.que-detail-page .editor-content img{max-width:100%;vertical-align:text-bottom}.que-detail-page .edit-btn{text-align:center;margin-top:20px}.que-detail-page .edit-btn .edit-cancle{background:#aaa}</style><style type="text/css">.head-info{line-height:26px;background:#f5f5f5;font-size:13px;color:#999}.head-info a{margin-left:20px;color:#333}.head-search{margin-top:22px}.head-search img{float:left}.head-search .srh-box{float:left;margin:20px 0 0 20px}.head-search .srh-box .srh-input{float:left;width:260px;height:38px;line-height:38px;border:2px solid #37a508;border-radius:4px 0 0 4px}.head-search .srh-box a,.head-search .srh-box button{float:left;width:80px;height:38px;line-height:36px;font-size:16px;color:#fff}.head-search .srh-box .srh-btn{border-radius:0 4px 4px 0}.head-search .srh-box .ask-btn{margin-left:10px}.head-nav{float:left;margin:20px 0 0 60px;color:#fff;font-size:16px;line-height:36px}.head-nav a{display:block;height:100%;color:#333}.head-nav li{position:relative;float:left;margin-right:35px}.head-nav .active a,.head-nav li:hover a{color:#30b940}.head-nav .active:after,.head-nav li:hover:after{display:block;content:"";height:3px;width:100%;background:#30b940}.head-pic{width:100%;height:90px;margin-top:20px;background:url(/static/banner.png) top no-repeat}</style><style type="text/css">.top-breadcrumb{height:20px;margin-top:10px;margin-bottom:40px;font-size:13px;color:#666}.top-breadcrumb .t-b-icon{color:#30b940}.top-breadcrumb span{margin-left:8px}.top-breadcrumb span .fa{font-size:16px;margin-left:8px}.top-breadcrumb span:first-child{margin-left:0}.top-breadcrumb .now{color:#30b940}</style><script type="text/javascript" src="/static/ueditor/ueditor.config.js?20210319" id="configScriptTag"></script><script type="text/javascript" src="/static/ueditor/ueditor.all.js?20210319" id="editorScriptTag"></script><style type="text/css">.checkbox[data-v-0fe26aab]{word-spacing:4px;vertical-align:bottom;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.checkbox i[data-v-0fe26aab]{display:inline-block;width:14px;height:14px;border-radius:2px;border:1px solid #ccc;background:#fff;position:relative;top:1px}.checkbox input[data-v-0fe26aab]{opacity:0;width:0;height:0;margin:0}.checkbox input:checked+i[data-v-0fe26aab]{background:#30b940;border-color:#30b940}.checkbox input:checked+i[data-v-0fe26aab]:before{content:"";position:absolute;left:2px;top:1px;width:11px;height:6px;border-bottom:2px solid #fff;border-left:2px solid #fff;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}</style><style type="text/css">.popbox{position:fixed;top:50%;left:50%;z-index:1011;background:#fff;border-radius:6px;overflow:hidden}.popbox h3{font-size:16px;line-height:40px;padding:0 20px}.popbox h3 span{float:right;color:#666;font-size:13px;cursor:pointer}.popbox .pb-cnt{padding:0 20px;font-size:13px}.popbox .pb-btn-area{position:absolute;bottom:0;left:0;z-index:1;width:100%;text-align:right;padding:0 20px 20px 0}.popbox .pb-btn-area button{height:32px;line-height:32px;border:0;background:#fff;cursor:pointer;border-radius:6px;margin-left:15px;padding:0 15px}.popbox .pb-btn-area button.pb-btn-ok{background:#daf3cf;color:#279005}.popbox .pb-btn-area button.pb-btn-ok:hover{background:#279005;color:#fff}</style><style type="text/css">.shadow{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1010;background:#000}</style><style type="text/css">.footer{margin-top:70px;margin-bottom:20px;padding-top:20px;text-align:center;color:#999;border-top:1px solid #f5f5f5}.footer span{margin:0 10px}</style><script src="/static/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript" defer="defer"></script><link href="/static/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet"><script src="/static/ueditor/third-party/codemirror/codemirror.js" type="text/javascript" defer="defer"></script><link rel="stylesheet" type="text/css" href="/static/ueditor/third-party/codemirror/codemirror.css"><script src="/static/ueditor/third-party/zeroclipboard/ZeroClipboard.js" type="text/javascript" defer="defer"></script><style type="text/css">.activeUser,.favorite,.hotTag,.rank{margin-bottom:40px}</style><style type="text/css">.rank .rank-page{height:60px;line-height:60px;font-size:18px;margin-bottom:20px;text-align:center;background:#fafafa;border-radius:5px}.rank ul{padding:0 20px}.rank li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;border-bottom:1px dotted #f5f5f5;line-height:50px}.rank li .flag{position:relative;display:inline-block;width:20px;height:30px;background:#476472;vertical-align:middle;margin-right:12px;color:#fff;text-align:center;line-height:20px;font-size:13px;font-style:normal}.rank li .flag:after{content:"";position:absolute;bottom:-4px;left:-4px;border:14px solid transparent;border-bottom-color:#fff}.rank li:first-child .flag{background:#fb6757}.rank li:nth-child(2) .flag{background:#fac525}.rank li:nth-child(3) .flag{background:#00a9fb}.rank li img{width:36px;height:36px;border-radius:50%;margin-right:12px}</style><style type="text/css">.activeUser .activeUser-box a{display:inline-block;width:64px;height:64px;border-radius:50%;overflow:hidden;margin:0 6px 12px}</style><style type="text/css">.to-top{display:none;position:fixed;bottom:150px;z-index:5;width:50px;height:50px;cursor:pointer}</style></head><body style=""><div id="app" style=""><div class="que-detail-page" style=""><div class="wiki-hd"><div class="head-info"><p class="page-cnt">
      您好，欢迎来到591客服知识库
      <a href="https://www.591.com.tw/admin.php?new_url=https://zsk.591.com.tw">[请登入]</a> <!----></p></div> <div class="page-cnt head-search clearfix"><a href="/" class="router-link-active"><img src="/static/logo.png"></a> <ul class="head-nav clearfix"><li class="router-link-active active"><a href="/">591房屋交易网</a></li> <li class=""><a href="/life">客服充电站</a></li> <li class=""><a href="/notification">通知事项</a></li></ul> <div class="srh-box"><input type="text" placeholder="请输入搜索内容" class="srh-input"> <button type="button" class="button srh-btn">搜索</button> <a href="/ask/591" class="button button-orange ask-btn">提問</a></div></div> <div class="head-pic"></div></div> <div class="top-breadcrumb com-width"><span><a href="/collection/591/1302" class="">客服質檢相關<i class="fa fa-angle-double-right"></i></a></span><span><a href="/collection/591/1302/1661" class="">星級練習題<i class="fa fa-angle-double-right"></i></a></span><span><strong class="now">问题详情</strong></span></div> <div class="com-width clearfix" style=""><div class="index-left" style=""><div class="detail-box"><h3><!---->T5-各星級筆試考核參考模板</h3> <p class="clearfix d-b-menu"><span class="l">
            黃雪靜&nbsp;&nbsp;&nbsp;浏览87次&nbsp;&nbsp;&nbsp;2020-11-09 17:50:52
          </span> <span class="r"><span class="add-fav"><i class="fa fa-heart"></i>加入最爱</span></span></p> <div class="d-b-cnt editor-content big-img-parent"><div><p>二星筆試考試模板</p><p>三星筆試考核模板</p><p>四星筆試考核模板</p></div></div> <div class="d-b-button"><a href="/edit/591/4343" class="button">编辑</a> <button class="button button-orange">关闭提问</button></div></div> <div class="hr-line"></div> <ul class="reply-list big-img-parent"><li class="reply-item"><h4><div><img src="/upload/head/10177.gif?time=1556271256">&nbsp;&nbsp;&nbsp;
              <span class="user">黃雪靜</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-09 17:52:10</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="white-space: normal; text-align: center;"><strong><span style="font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">二星技能考核-理論知識試題（模板示範題）</span></strong></p><p style="white-space: normal; text-align: center;"><strong><span style="font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">姓名： ________ 客服工號：_________ 分數：__________</span></strong></p><p style="white-space: normal; text-align: center;"><span style="font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong>&nbsp;</strong></span></p><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong>一、填空題（共24分，每題2分）</strong></span></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>一筆物件僅能操作續刊__<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1</span>__次_，套餐方案_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">不可</span>_操作續刊。</span></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>即時問答和留言管理有什麼區別：_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">即：只能通過手機APP線上與房客交流；留：房客通過留言的方式進行提問，可同步到電腦查看，若刊登者回復之後內會曝光在網路上</span>&nbsp;_。</span></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>註冊資料中分別有提供_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">捐給慈善機構</span>_、_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">對中寄送</span>_、_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">開立統一編號</span>_、_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">電子發票手機載具</span>_、發票處理方式。</span></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">4.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>便利超商繳費每次儲值最低不能少於__<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">125元_</span>_不得大於__<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">10025元</span>__。</span></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">5.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>會員ATM儲值帳號除了8928還有_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">8927</span>_和_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">8929</span>__。</span></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">6.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>廣告一經開啟__<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">24小時後</span>__無法會員自行修改什麼_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">地址</span>_&nbsp; _<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">樓層</span>_&nbsp; _<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">類型</span>_。</span></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">7.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>出租住宅類黃金套餐售價為_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3980元_</span>__、筆數為_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">25筆</span>_、刊登時間為_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">30天</span>__、欄位元升級需要_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">400元</span>_。</span></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">8.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>新刊登坪數規範為：整層住家_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">10坪以上</span>_、獨立套房_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3-35坪_</span>_、分租套房__<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2-20坪_</span>_、雅房_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2-12坪</span>__。</span></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">9.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>新刊登修改獨立套房修改_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3坪以內（包含3坪）</span>_可審核通過。</span></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">10.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>新刊登/修改違規，帳號已發過三次簡訊第四次違規是_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">學習</span>_、第五次_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">限制刊登/修改3天</span>_、第六次_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">暫時停權</span>_。</span></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">11.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>檢舉者來電檢舉仲介冒充屋主、代理人刊登廣告，已聯絡表示自己仲介但不想讓別人知道，不願意配合修改，我們該如何處理_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">發手機簡訊+關閉廣告</span>_。</span></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">12.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>會員來電修改物件資料最少需核實_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2</span>_項資料，修改帳號資料最少核實_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3</span>_項資料。</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong>二、判斷題，請勾√與×，若錯請說明原因（共</strong><strong>20</strong><strong>分，每題</strong><strong>2</strong><strong>分）</strong></span></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>網站上傳圖片格式的要求為JPG、PNG、GIF、PDF（<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">x</span>）</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">錯誤原因是：_________不支持PDF _______</span></p><p style="white-space: normal;"><br></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>出租住宅/出售套餐無法使用蘋果手機APP操作開啟廣告，出租商用套餐可以使用APP操作開啟（<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">√</span>）</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">錯誤原因是：____________________________</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>客服接聽電話的流程是：接起—報問候語—查詢—溝通互動—傾聽—結束語—掛機（<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">√</span>）</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">錯誤原因是：____________________________</span></p><p style="white-space: normal;"><br></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">4.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>註冊會員資料時，姓名可以填寫中文或英文名字及數位（<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">x</span>）</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">錯誤原因是：______僅能用中文__________</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">5.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>房屋已租出並簽約成功但是廣告時間還未到期，會員可直接將廣告做關閉而不下架（<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">√</span>）</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">錯誤原因是：___________________________</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">6.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>發票二聯改三聯需要讓會員提供：折讓單+公司證明+原發票編號+折讓單寄送地址（<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">√</span>）</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">錯誤原因是：____________________________</span></p><p style="white-space: normal;"><br></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">7.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>公司名稱是數位科技股份有限公司，公司電話：02-29995691、客服電話：02-55722000、客服傳真：02-55793400（<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">√</span>）</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">錯誤原因是：___________________________</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">8.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>會員昵稱可以至會員中心帳號管理-討論區-討論區資料自行設定並隨時修改（<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">x</span>）</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">錯誤原因是：__一旦設定客服及會員均無法修改__</span></p><p style="white-space: normal;"><br></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">9.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>帳號身份為仲介目前有刊登2筆物件為：仲介，不須服務費，來電需將帳號身份變更為屋主/代理人，可以幫其變更對嗎（<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">√</span>）</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">錯誤原因是：___________________________</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left: 28px; white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">10.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>會員物件刊登5天后自行修改廣告金額、坪數、格局，新刊登修改審核人員可直接審核通過（<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">x</span>）</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">錯誤原因是：__自刊登之日起超過3天修改多項按更換處理__</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong>三、簡答題（共</strong><strong>20</strong><strong>分，每題</strong><strong>4</strong><strong>分）</strong></span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1、會員來電詢問發票是否已開立，有沒有寄出，是哪一天寄出的，現還未收到發票但著急報帳該如何處理（請寫出你的處理流程及解決方案）</span></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1.確認會員儲值的時間</span></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2.查看是否有超過15個工作日</span></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3.儲值後，發票是在隔天開立，有遇到假期順延，發票寄出時間是儲值後10個工作日</span></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">4.未超過15個工作日，會員著急報帳，可以先email，或傳真電子檔給會員</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2、會員來電回饋廣告已開啟成功，但地圖定位有誤，為什麼自己無法修改？要如何刊登才可以直接修改？（請寫出你的處理流程及解決方案）</span></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1.與會員確認廣告地址是否確認</span></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2.告知會員有填寫社區，無法自行修改，沒有填寫社區是可以自行在廣告詳細頁修改</span></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3.地圖錯誤，有填寫社區，可以幫會員修改</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3、會員來電回饋今日有收到簡訊，內容是：依據租賃住宅市場發展管理條例應核實房屋資訊，您所刊登位址無法核實資訊，請確認位址或者上傳房屋藤本。要怎麼去核實呢？如果不上傳就不可以刊登嗎？房屋藤本資料洩露了怎麼辦？（請寫出你的處理流程及解決方案）</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1.與會員確認廣告地址還有樓層，是否正確，手動幫會員核實，未核實到請會員上傳藤本，在幫其修改</span></span></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2.手動核實到資料可以不用上傳，未核實到，與會員說明核實法案的原因，請會員上傳</span></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3.告知會員只做核實資料的用途，591會保護好會員的資料，不會曝光，若會員還是擔心，建議會員遮擋個資，留下房屋資訊</span></p><p style="white-space: normal;"><br><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">4、會員刊登廣告備註多間資訊，客服直接將違規資訊發站內簡訊編輯，會員來電不滿，表示為什麼不經過會員同意就私自編輯廣告資訊並憑什麼別人的廣告就可以這樣去備註說明自己的就不行（請寫出你的處理流程及解決方案）</span></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1.安撫會員</span></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2.說明刊登規則</span></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3.告知會員在刪除資訊時有發送站內簡訊，請會員後續多留意，</span></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">4同理心，建議會員可以怎樣備註，給出符合規則的方案</span></p><p style="white-space: normal;"><br><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">5、會員來電表示自己會出國一個禮拜，詢問刊登中的廣告有什麼辦法可以關閉並保留這個時間或者是繼續刊登但可以不然房客白資訊（請寫出你的處理流程及解決方案）</span></p><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1.查看廣告刊登時間，若在3天內，可告知會員幫申請取消廣告，退還刊登的套餐或是點數，建議會員回國後，在重新填寫資料刊登</span></span></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2.時間太長，無法取消，建議會員可以暫時關閉廣告，網站有提供暫時關閉的功能，并告知暫時關閉的規則</span></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3.建議會員可以將賬號提供給朋友或是家人，請其幫管理廣告</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong>四、論述題（共</strong><strong>12</strong><strong>分，每題</strong><strong>6</strong><strong>分）</strong></span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1、會員購買出售套餐還剩餘10筆已過期未刊登，查看記錄已有幫其延長過2天並提醒需在有效期之內刊登完畢，但會員仍然未操作刊登，現已過期再次來電要求延長，已拒絕，不接受要求給合理的解釋。（請針對該場景寫完整話術）</span></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">考點：拒絕會員的原因（1、套餐購買時有提醒需在有效期效使用完&nbsp; 2、套餐到期前會有站內簡訊提醒&nbsp; 3、過期已特殊延長&nbsp; 4、延長時間為2天&nbsp; 5、有再次提醒需在有效期使用）基本答到點即可</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2、會員將廣告成交下架，並在10天之後來電回饋自己是不小心錯誤操作下架的，查看廣告時間還剩餘3天到期，已說明網站規則拒絕會員了但不接受，表示仍需將廣告恢復不然就將3天的點數折退，已告知與專員申請請稍等，申請結果是無法恢復也不在退點範圍，請問你要如何與會員順利結束該通話。（請針對該場景寫話術）</span></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">考點：網站成交操作規則、可恢復範圍、折點退還說明、給予會員一些建議</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong>五、案例分析題（共</strong><strong>24</strong><strong>分，每題</strong><strong>3</strong><strong>分）</strong></span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1、查看下圖：請問此筆廣告是否可以通過？請列出原因及處理流程</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><img src="https://zsk.591.com.tw/upload/question/20200811/1597112782293740.png" alt="image.png"></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">不通過，多間照片，第一次放待處理</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2、查看下圖：請問此筆廣告是否可以通過？請列出原因及處理流程</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><img src="https://zsk.591.com.tw/upload/question/20200811/1597112802428422.png" alt="image.png"></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">不通過，備註多間和無關網址，發站內容簡訊編輯</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3、查看下圖：請問此筆廣告是否可以通過？請列出原因及處理流程</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><img src="https://zsk.591.com.tw/upload/question/20200811/1597112817583904.png" alt="image.png"></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">通過</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">4、查看下圖：請問此筆廣告是否可以通過？請列出原因及處理流程</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><img src="https://zsk.591.com.tw/upload/question/20200811/1597112832769658.png" alt="image.png"></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">不通過，確認用途，放待處理確認</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">5、查看下圖：請問此筆廣告是否可以通過？請列出原因及處理流程</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><img src="https://zsk.591.com.tw/upload/question/20200811/1597112843623484.png" alt="image.png"></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">不通過，不可備註日租，放待處理確認</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">6、查看下圖：請問此筆廣告是否可以通過？請列出原因及處理流程</span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><img src="https://zsk.591.com.tw/upload/question/20200811/1597112857995410.png" alt="image.png"></span></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">不通過，金額上下不一致（欄位僅可填寫一般戶的租金），放待處理確認</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">7、查看下圖：請問此筆廣告是否可以通過？請列出原因及處理流程</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><img src="https://zsk.591.com.tw/upload/question/20200811/1597112868998546.png" alt="image.png"></span></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">通過</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">8、查看下圖：請問此筆廣告是否可以通過？請列出原因及第二天聯絡無人接聽的處理流程</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><img src="https://zsk.591.com.tw/upload/question/20200811/1597112879528214.png" alt="image.png"></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">不通過，確認樓高和用途；若第二天聯絡仍無人接聽發簡訊+信箱將廣告暫時關閉</span></p><p style="white-space: normal;"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/upload/head/10177.gif?time=1556271256">&nbsp;&nbsp;&nbsp;
              <span class="user">黃雪靜</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-09 17:56:13</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="white-space: normal;text-align: center"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">T5-2020年三星技能考核-理論知識試題（模板示範題）</span></strong></p><p style="white-space: normal;text-align: center"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">姓名： ________ 客服工號：_________ 分數：__________</span></strong></p><p style="white-space: normal;text-align: center"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong>&nbsp;</strong></span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong>一、填空題（共</strong><strong>30</strong><strong>分，每題</strong><strong>2</strong><strong>分）</strong></span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>出售套餐取消規則是：_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">購買時間是否超過3天，未超過直接取消，若超過優先拒絕</span>_；_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">套餐若已使用需告知物件會刪除，會員同意方可取消</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>廣告內留言什麼情況下會曝光在網路上_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">可刊登者回復留言內容之後即會曝光在網路上</span>_；留言者要刪除留言如何處理：_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">聯絡刊登者</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>會員需取消出售套餐，未跨月如何處理：_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">後臺點數明細找到對應套餐取消退還點數</span>_；已跨月取消退還點數可能會出現什麼狀況：_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">套餐已取消點數未到賬，需提交主任進行贈點</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">4.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>會員留言回饋廣告開啟時已做續刊，但在刊登第20天房屋就已經簽約成交下架，會員以遊客身份留言未留聯絡電話該如何處理：_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">直接線上回復處理結果</span>&nbsp;_；續約未使用到點數是否會退還：_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">系統會員自動退回至帳號內</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">5.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>線上回復類別有哪些分別有什麼特性：_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">留言/具有公開信；申訴、信箱/非公開，具有緊迫性</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">6.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>網站廣告圖片刊登有什麼規定最少寫4點： _<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">不可上傳多間照片</span>&nbsp;_；_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">人物照</span>_；_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">文字照</span>_；_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">圖片模糊不清晰</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">7.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>預售屋實景圖最少：_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3</span>_張；新成屋實景圖最少：_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">5</span>_張；環境圖最少：_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">7</span>_張；廣告宣傳圖儘量上傳_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">4</span>_張</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">8.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>有收到簡訊廣告備註已成交說不處理直接將其關閉，特此寫信瞭解你們的規則是怎麼樣的，第一天聯絡會員不配合該如何處理：_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">做好記錄，發手機簡訊通知</span>_；第二天仍不配合該如何處理：_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">發手機簡訊通知+信箱+廣告暫時關閉</span></span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">9.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>線上回復要求有哪些最少寫4點： _<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">無錯別字和簡體字</span>&nbsp;_；_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">符號為半型輸入法</span>_；_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">回復語句最長不超25字</span>_；_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">回復承諾會員需兌現</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">10.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;</span>留言什麼內容可直接刪除最少寫2點：_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">無關網址連結；宣傳廣告無法資訊</span>_；遇到個資內容需如何處理：_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">使用*號做隱藏</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">11.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;</span>什麼是新建案動態：_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">該欄位元可展示建案的最新進度、銷售情況等</span>_；每條動態一天可修改多少次_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">兩次</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">12.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;</span>線上回復需提醒的類型有哪些舉例兩點：_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">暫時關閉、取消物件</span>_；需申請的類型有哪些舉例兩點：_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">退現、套餐延期</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">13.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;</span>會員申訴需要出售店面修改為辦公無法修改是什麼原因：_<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">廣告開啟超過24小時會員無法自行操作修改地址、樓層、類型</span></span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">14.&nbsp; 商用出租超級套餐如何收費：_<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #FF0000;">3580</span>_元；一個欄位元升級為黃金曝光需要：_<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #FF0000;">900</span>_元；欄位到期前未使用是否會下發站內簡訊_<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #FF0000;">不會</span>_；套餐到期前是否會下發站內簡訊通知_<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #FF0000;">不會</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">15.&nbsp; 會員同一天多次申訴，但對於客服已回復的上條申訴內容並未查看，此情況該如何處理：_<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #FF0000;">電話聯絡會員說明</span>_；線上回復優先電話聯絡的類型有哪些最少寫2點：_<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #FF0000;">發票開立未收到、廣告無法搜尋</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span><br></p><p style="margin-left: 28px;white-space: normal"><br></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong>二、判斷題，請勾√與×，若錯請說明原因（共</strong><strong>20</strong><strong>分，每題</strong><strong>2</strong><strong>分）</strong></span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>會員使用普通方案將廣告開啟並升級VIP，當日即來電表示VIP廣告升級錯誤需要取消，客服告知需將廣告和VIP一併取消把點數退還（<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">x</span>）</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">錯誤原因是：__可直接取消VIP，廣告無需取消___</span></p><p style="margin-bottom: 0px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-top: 0px;margin-bottom: 0px;margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>恢復出租套餐欄位元的操作步驟：第一步選擇套餐類別輸入用戶ID；第二步輸入套餐ID填寫櫥窗位ID點擊恢復櫥窗；第三步檢查是否已恢復成功並回報群內（<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">√</span>）</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">錯誤原因是：____________</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>會員信箱回饋需要修改廣告資料我們的處理方式是：若註冊信箱來信或信箱內主動提供兩項以上會員資料且已核實的可直接修改；非註冊信箱來信需回撥註冊或刊登電話確認；電話聯絡不到，回信核實資料後再處理（<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">√</span>）</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">錯誤原因是：____________</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">4.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>仲介身份不可刊登建案廣告，註冊的建設/代銷公司與案場不符一樣可刊登廣告（<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">x</span>）</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">錯誤原因是：__建設/代銷公司與案場不符不可刊登廣告___</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">5.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>使用單筆刊登方案的廣告可在會員中心一鍵操作操作更新排序，套餐方案廣告就無法一鍵操作更新排序，需一筆一筆操作（<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">x</span>）</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">錯誤原因是：__出租類套餐方案不可以，出售套餐方案廣告可以__</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">6.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>會員目前帳號為仲介身份且開啟中有多筆物件，現申訴回饋已沒有從事仲介了需將身份變更為屋主/代理人，客服告知需將廣告暫時關閉或刊登中的物件不可收取服務費（<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">√</span>）</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">錯誤原因是：_______________</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">7.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>同個會員有發送多封郵件過來要求幫忙上傳廣告照片，客服已將照片做上傳並回復其中一封信說明已處理，將其他重複信件做刪除（<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">x</span>）</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">錯誤原因是：__需一一回復，不可私自刪除會員信件___</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">8.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>子帳號是可以在其他帳號同時註冊填寫使用，母帳號可以無限制添加子帳號（<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">x</span>）</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">錯誤原因是：____子帳號不可以在其它帳號同時註冊使用____</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 14px; background: lightgrey; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">9.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;&nbsp;</span>會員留言需要登出帳號，客服的處理流程是：回電確認是本人即可直接提交專員註銷（</span><span style="font-size: 14px; color: red; background: lightgrey; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">x</span><span style="font-size: 14px; background: lightgrey; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">）</span></span></p><p style="white-space: normal"><span style="background: lightgrey; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">錯誤原因是：___需判斷帳號是否存在糾紛，是否有點數若有需解釋說明__</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">10.<span style="font-size: 14px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp;</span>線上留言若已電話聯絡處理會員問題，則可將整筆留言做刪除，若需跟進的則記電話回撥即可（<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">√</span>）</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">錯誤原因是：_______________</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong>&nbsp;</strong></span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong>三、簡答題（共</strong><strong>15</strong><strong>分，每題</strong><strong>3</strong><strong>分）注：無需電話聯絡只根據題目內容作答</strong></span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1、根據會員的問題，請問回復的要素是什麼？</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><img src="https://zsk.591.com.tw/upload/question/20200923/1600833194428773.png" alt="image.png"></span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style="white-space: normal"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">取消廣告資料會隨之刪除</span></p><p style="white-space: normal"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">退還多少點或多少筆到會員帳號</span></p><p style="white-space: normal"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">回復內容需有物件編號</span></p><p style="white-space: normal"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2、根據會員的問題，請問回復的要素是什麼？</span></p><p style="white-space: normal"><img src="https://zsk.591.com.tw/upload/question/20200923/1600833200135990.png" alt="image.png"></p><p style="white-space: normal"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">安撫會員情緒</span></p><p style="white-space: normal"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">告知廣告暫時關閉時間會照常計算/成交下架廣告時間會隨之結束</span></p><p style="white-space: normal"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">需測試該功能是否正常並回復說明</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3、根據會員的問題，請問回復的要素是什麼？</span></p><p style="white-space: normal"><img src="https://zsk.591.com.tw/upload/question/20200923/1600833207602878.png" alt="image.png"></p><p style="white-space: normal"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">回復刷退的金額</span></p><p style="white-space: normal"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">退款預計到賬時間</span></p><p style="white-space: normal"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">提醒會員留意下期信用卡帳單</span></p><p style="white-space: normal"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">4、根據會員的問題，請問回復的要素是什麼？</span></p><p style="white-space: normal"><img src="https://zsk.591.com.tw/upload/question/20200923/1600833213911898.png" alt="image.png"></p><p style="white-space: normal"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">幫其修改為正確樓層</span></p><p style="white-space: normal"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">告知網站修改規則（開啟24小時之後位址、樓層、類型會員無法自行修改）</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">5、根據會員的問題，請問回復的要素是什麼？</span></p><p style="white-space: normal"><img src="https://zsk.591.com.tw/upload/question/20200923/1600833220396629.png" alt="image.png"></p><p style="white-space: normal"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">安撫用戶</span></p><p style="white-space: normal"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">告知會聯絡刊登者進一步核實確認</span></p><p style="white-space: normal"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">說明有結果之後會在回報</span></p><p style="white-space: normal"><span style="color: red; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong>四、論述題（共</strong><strong>15</strong><strong>分，每題</strong><strong>5</strong><strong>分）</strong></span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1、請列出處理流程和回復內容（無需按申訴格式回復）</span></p><table cellspacing="0" cellpadding="0" width="604"><tbody><tr class="firstRow"><td width="85" style="padding: 0px 7px;border-color: windowtext;background: rgb(189, 214, 238)"><p style="text-align: center;line-height: 32px"><span style="line-height: 28px; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">詳情</span></p></td><td width="520" valign="top" style="padding: 0px 7px;border-top-color: windowtext;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;background: rgb(189, 214, 238)"><p style="line-height: 32px"><span style="line-height: 28px; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">我購買的出售季約套餐還剩餘5筆到期了，沒有注意到日期可以幫忙延期嗎？</span></p></td></tr></tbody></table><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">處理流程：<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">查看帳號是否有多次延期記錄，有則拒絕會員，若無則與主任申請幫其延期</span></span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">回復內容：<span style="font-size: 14px; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">拒絕：抱歉用語，說明網站套餐購買規則需要在有效期之內使用，逾期作廢；同意：告知已跟專員申請延長到今天12點需在有效期之內使用，逾期無法再特殊處理</span></span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2、請列出處理流程和回復內容</span></p><table cellspacing="0" cellpadding="0" width="604"><tbody><tr class="firstRow"><td width="85" style="padding: 0px 7px;border-color: windowtext;background: rgb(189, 214, 238)"><p style="text-align: center;line-height: 32px"><span style="line-height: 28px; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">詳情</span></p></td><td width="520" valign="top" style="padding: 0px 7px;border-top-color: windowtext;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;background: rgb(189, 214, 238)"><p style="line-height: 32px"><span style="line-height: 28px; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">房屋之前一樣刊登，為什麼現在突然要上傳藤本，不上傳就不可以刊登了麼？</span></p></td></tr></tbody></table><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">處理流程：</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">回復內容：</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3、請列出處理流程和回復內容</span></p><table cellspacing="0" cellpadding="0" width="604"><tbody><tr class="firstRow"><td width="85" style="padding: 0px 7px;border-color: windowtext;background: rgb(189, 214, 238)"><p style="text-align: center"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">詳情</span></p></td><td width="520" valign="top" style="padding: 0px 7px;border-top-color: windowtext;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;background: rgb(189, 214, 238)"><p><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">房客來看房已經交了押金所以將廣告下架了，但今天房客又返悔了，廣告有效期還沒有到可以幫忙恢復刊登麼</span></p></td></tr></tbody></table><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">處理流程：</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">回復內容：</span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong>&nbsp;</strong></span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong>&nbsp;</strong></span></p><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong>四、案例回復題（共</strong><strong>20</strong><strong>分，每題</strong><strong>4</strong><strong>分）</strong></span></p><table cellspacing="0" cellpadding="0" width="400"><tbody><tr class="firstRow"><td width="198" style="padding: 0px 7px;border-color: windowtext;background: rgb(189, 214, 238)"><p><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">1.檢舉</span></p></td><td width="406" style="padding: 0px 7px;border-top-color: windowtext;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;background: rgb(189, 214, 238)"><p class="MsoListParagraph"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">最近老是有人在即時問答要房東加她賴，結果都在賴串貼文騷首弄姿，可能是援交的，麻煩控管一下，避免增加房東困擾</span></p></td></tr><tr><td width="400" colspan="2" valign="top" style="padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left-color: windowtext"><br></td></tr></tbody></table><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><table cellspacing="0" cellpadding="0" width="400"><tbody><tr class="firstRow"><td width="198" style="padding: 0px 7px;border-color: windowtext;background: rgb(189, 214, 238)"><p class="MsoListParagraph" style="line-height: 40px"><span style="line-height: 35px; font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">2.營業員證照</span></p></td><td width="406" style="padding: 0px 7px;border-top-color: windowtext;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;background: rgb(189, 214, 238)"><p class="MsoListParagraph"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">我是仲介,請問購買點數後po出售廣告,營業員證照一定要傳麼？如果不用那為什麼會有這個？</span></p></td></tr><tr><td width="400" colspan="2" valign="top" style="padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left-color: windowtext"><br></td></tr></tbody></table><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><table cellspacing="0" cellpadding="0" width="400"><tbody><tr class="firstRow"><td width="198" style="padding: 0px 7px;border-color: windowtext;background: rgb(189, 214, 238)"><p class="MsoListParagraph"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">3.保護電話</span></p></td><td width="406" style="padding: 0px 7px;border-top-color: windowtext;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;background: rgb(189, 214, 238)"><p class="MsoListParagraph"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">為什麼我的廣告聯絡電話顯示的不是自己的號碼，這樣會影響到我帶房客看房吧，要怎麼取消掉</span></p></td></tr><tr><td width="400" colspan="2" valign="top" style="padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left-color: windowtext"><br></td></tr></tbody></table><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><table cellspacing="0" cellpadding="0" width="400"><tbody><tr class="firstRow"><td width="198" style="padding: 0px 7px;border-color: windowtext;background: rgb(189, 214, 238)"><p><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">4.VIP標籤顯示</span></p></td><td width="406" style="padding: 0px 7px;border-top-color: windowtext;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;background: rgb(189, 214, 238)"><p><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">廣告已開啟並成功購買VIP，登入會員中心頁面查看廣告有VIP的標籤顯示，但在前臺首頁搜尋廣告未顯示VIP的標籤，請問是什麼原因</span></p></td></tr><tr><td width="400" colspan="2" valign="top" style="padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left-color: windowtext"><br></td></tr></tbody></table><p style="white-space: normal"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><table cellspacing="0" cellpadding="0" width="400"><tbody><tr class="firstRow"><td width="198" style="padding: 0px 7px;border-color: windowtext;background: rgb(189, 214, 238)"><p><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">5.暫時關閉</span></p></td><td width="406" style="padding: 0px 7px;border-top-color: windowtext;border-right-color: windowtext;border-bottom-color: windowtext;border-left: none;background: rgb(189, 214, 238)"><p><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">廣告剛開啟但突然出狀況房屋需要進行維修，是否可以將廣告先暫時關閉，待房屋維修好後再延長刊登時間呢</span></p></td></tr><tr><td width="400" colspan="2" valign="top" style="padding: 0px 7px;border-top: none;border-right-color: windowtext;border-bottom-color: windowtext;border-left-color: windowtext"><br></td></tr></tbody></table><p style="white-space: normal"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/upload/head/10177.gif?time=1556271256">&nbsp;&nbsp;&nbsp;
              <span class="user">黃雪靜</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-11-09 17:57:17</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="white-space: normal;text-align: center"><strong><span style="font-family: 微软雅黑, 'Microsoft YaHei'"></span></strong></p><p style="white-space: normal; text-align: center;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">T5-2020年四星技能考核-理論知識試題（模板示範題）</span></strong></p><p style="white-space: normal; text-align: center;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">姓名： ________ 客服工號：_________ 分數：__________</span></strong></p><p style="white-space: normal;text-align: center"><br></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px"><strong>一、填空题（共</strong><strong>40</strong><strong>分，每题</strong><strong>2</strong><strong>分）</strong></span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">1.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span>周末储值的发票何时开立：_<span style="color: red">固定周二开立</span>_；付回邮是什么意思：_<span style="color: red">会员信封内放一张邮票在信封寄送到591，收到再以邮票方式寄出</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">2.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span>国泰世华信用卡储值查询订单状态显示未储值是什么意思：_<span style="color: red">网路连线中断导致未储值成功，此订单记录银行会在3-5个工作日内进行取消</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">3.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span>信用卡储值失败常见返回代码说明什么10000：_<span style="color: red">参数错误</span>&nbsp;_；99999：_<span style="color: red">系统错误（网路繁忙）</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">4.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span>7-11储值未入账会员无法提供收据和缴费代码，但店员可提供订单编号，该如何处理：_<span style="color: red">联络台湾同事在对账时提供未入账数据，核对成功后在后台人工销案处理</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">5.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span><span style="background: lightgrey">判断电话故障类</span>型的三步骤是： _<span style="color: red">查询客服电话网络情况</span>&nbsp;_；_<span style="color: red">检查电话（话机）</span>_；_<span style="color: red">查询电话系统</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">6.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span>若需查看会员中心广告的浏览明细需满足什么条件：_<span style="color: red">广告需购买加值服务</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">7.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span>什么情况下可以退现，最少写四点：_<span style="color: red">不在使用591</span>_；_<span style="color: red">重复储值</span>_；_<span style="color: red">储值错误</span>_；_<span style="color: red">苹果手续费太高</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">8.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span>24小内使用出租套餐栏位开启广告，什么情况下物件不会显示‘新’的标签？：_<span style="color: red">开启之后有操更换物件</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">9.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span>什么情况下需要使用到纸质退款_<span style="color: red">剩余苹果点数</span>_；走纸质退款需请会员提供什么资料：_<span style="color: red">存折封面 + 折让单 + 保证书 + 注册会员身份证复印件（正反面</span>）_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">10.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;</span>信用卡申请调单的途径有_<span style="color: red">厂商通过信箱回馈</span>_和_<span style="color: red">台湾同事回馈</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">11.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;</span>修改备注已成交，第一天联络会员不配合该如何处理：_<span style="color: red">做好记录，发手机简讯通知</span>_；第二天仍不配合该如何处理：_<span style="color: red">发手机简讯通知+信箱+广告暂时关闭</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">12.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;</span>ping值正常范围是：_<span style="color: red">38~75ms</span>_；ping值大于300ms时会出现什么情况：_<span style="color: red">电话可正常进线，但接起客服无法听到会员的声音</span>_；若出现【timeout within】提示是代表什么意思：_<span style="color: red">电话网挂了</span>_；出现该情况应如何处理：_<span style="color: red">及时反馈给网管并跟进</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">13.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;</span>查询skp外线接入方式，正常为_<span style="color: red">直线</span>_，正常显示号码未_<span style="color: red">6</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">14.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;</span>网站前台无法打开需如何处理，前台：_<span style="color: red">可能会影响到会员，需紧急处理，将故障页面截图和链接提交给主任，由主任反馈给工程师</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">15.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;</span><span style="background: lightgrey">电话及网路</span>均无法使用时四星客服该做什么动作：_<span style="color: red">及时反馈给主任，进入IVR设电话并发布公告</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">16.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;</span><span style="background: lightgrey">工作日国泰世华、银行ATM储</span>值、7-11便利超商_<span style="color: red">半小时</span>_内未入账可以初步判断为故障；其余储值方式_<span style="color: red">2小时</span>&nbsp;_以上为入账，可列为疑似故障</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">17.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;</span>什么情况下允许删除会员账号_<span style="color: red">无纠纷</span>_；_<span style="color: red">无刊登中的物件</span>_；_<span style="color: red">无点数或同意放弃账号点数</span>_；该如何处理：_<span style="color: red">回电确认本人及原因核实3项后提交专员处理</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">18.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;</span>新刊登物件照片格式仅支持：_<span style="color: red">jpg、gif、png</span>_，单张照片大小控制在：_<span style="color: red">25</span>_m以内</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">19.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;</span>承租人要负担房屋税么：_<span style="color: red">由出租者承担（即房东承担所有税务，房客不比缴纳）</span>_</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">20.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;</span><span style="background: lightgrey">电话故障时，四星客服</span>需进入IVR将当天的开始/结束时间设定为：_<span style="color: red">0909</span>_；非服务时间02编辑为：_<span style="color: red">aa</span>_</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px"><strong>二、判断题，请勾√与×，若错请说明原因（共</strong><strong>20</strong><strong>分，每题</strong><strong>2</strong><strong>分）</strong></span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">1.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span><span style="background: lightgrey">银行储值未入账出现故障</span>，已初步判断是程式卡住导致未入账的，四星客服应该进入后台程式执行日志网页，若查看页面出现大面积红色字体【查无超商资料】的情况时，需勾选按压删除，等待程式二次运行正常即可（<span style="color: red">x</span>）</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">错误原因是：____【查无银行资料】________</span></p><p style="margin-bottom: 0px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="margin-top: 0px;margin-bottom: 0px;margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">2.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span>若会员物件距离捷运站2000公尺内搜寻不到，则需查看地图位置是否错误，若错误帮其修正地图位置后即可直接搜寻到（<span style="color: red">x</span>）</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">错误原因是：___数据库在凌晨更新后才可搜寻到____</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">3.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span>黄小姐一礼拜之前储值了1000点数，但未使用，现来电要求退回，客服可直接提交专员帮其刷退（<span style="color: red">x</span>）</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">错误原因是：__发票已开立，无法直接刷退需走退现流程__</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">4.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span>网友检举广告资料刊登不实，客服联络两天均无人接听，查看此账号多次（&gt;=3次）被人检<span style="color: #333333">举，现已发手机简讯通知并将账号暂时停权</span>（<span style="color: red">√</span>）</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">错误原因是：____________</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">5.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span>会员莱尔富储值未入账，有将收据提供过来，收据中的“客户代码”是指会员账号ID号码（<span style="color: red">x</span>）</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">错误原因是：__会员电话的未8码___</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">6.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span>使用单笔刊登方案的广告可在会员中心一键操作操作更新排序，套餐方案广告就无法一键操作更新排序，需一笔一笔操作（<span style="color: red">x</span>）</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">错误原因是：__出租类套餐方案不可以，出售套餐方案广告可以__</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">7.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span>国字巷一经填写，会员下次修改资料将无法保存资料，需删除巷，保存后在联络客服重新填写国字巷（<span style="color: red">√</span>）</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">错误原因是：_______________</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">8.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span>全家储值未入账的处理流程是：1.确认储值时间；2.查看点数明显是否入账；3.提高数据；4.判断储值结果；5.手动入账（<span style="color: red">√</span>）</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">错误原因是：____________</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">9.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span><span style="background: lightgrey">已初版判断网站信用卡储</span>值出现故障，处理方式为：询问其他站台该项储值功能是否异常，若异常，则优先联络厂商询问情况，并反馈给主任；若正常，则反馈给主任，由主任找工程师处理（<span style="color: red">√</span>）</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">错误原因是：____________</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">10.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;</span>调单的处理流程是：1.会员收到账单，否认此笔消费交易向银行发起争议；2.厂商收到之后，通过信箱要求网站提供资料；3.客服通过银行提供的订单编号到客服后台查询会员资料并回复厂商（<span style="color: red">x</span>）</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">错误原因是：___需进入会员后台查看刊登记录，并联络会员核实确认__</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px"><strong>&nbsp;</strong></span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px"><strong>三、简答题（共</strong><strong>20</strong><strong>分，每题</strong><strong>5</strong><strong>分）</strong></span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">1、黄小姐使用自己的行动电话注册了公司账号使用，现已离职，公司同事陈小姐来电反馈，表示账号里面的点数还资料都是属于公司的，现不知道账密无法登入使用，陈小姐希望可以找回此账号和点数，请写出联络到和联络不到的处理流程及解决方案</span></p><p style="white-space: normal"><span style="color: red;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">联络到：若黄小姐表示属实，也同意处理，则帮忙转达请双方自行协商处理</span></p><p style="white-space: normal"><span style="color: red;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">联络不到：将账号停权，建议陈小姐申请新账号使用，若表示点数是公司所属请其提供证明过来再帮其处理</span></p><p style="white-space: normal"><span style="color: red;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">2、会员来电不满套餐笔数不可用做广告续刊，表示自己还有多笔快要过期，这样就没有办法使用白白浪费了，要求延长或退费，并建议网站去优化这个功能，优化好了请回复我（请写出你的处理流程及解决方案）</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">3、会员来电反馈自己的账号并未缴费跟刊登广告，但今日登入账号查看居然有刊登中的广告还剩余200点数，现在我要刊登自己的广告请问该怎么办（请写出你的处理流程及解决方案）</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="white-space: normal"><span style="color: red;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">4、会员不满来电表示自己已经操作很多次都无法将广告资料填写保存成功，询问是否为网站内部故障了，要求确认原因并帮忙操作填写刊登（请写出你的处理流程及解决方案）</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px"><strong>四、论述题（共</strong><strong>10</strong><strong>分，每题</strong><strong>5</strong><strong>分）</strong></span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">1<span style="background: lightgrey">、会员来电反馈账号可以登</span>入，但开启中的物件在前台无法搜寻，我朋友的广告也说没有办法搜寻到，接听客服已致歉并解释说明网站有故障，已反馈，建议会员稍晚再留意看看，为此非不满意，表示广告已付费房客无法搜寻到物件就影响了广告效果，已造成损失要求赔偿，要求上级回电处理及赔偿（1.请写出内部故障处理流程；2.请写回电话术）</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">2、会员来电反馈自己刊登的广告一直没有效果有质疑？说了好多原因，觉得以前都还经常接到电话，但最近刊登的广告咨询电话很少，而且老会接到一些类似专门做业绩的人来问，感觉会不会是591自己为了业绩就出这样的手段？而且近年来关于591的诈骗消息也很多。接听客服已跟其解释，并告知591只是提供刊登广告的平台，也告知影响广告效果可能涉及的因素。但觉得客服讲的不可信，要求请主管出面联络洽谈（请写回电话术）</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="white-space: normal"><span style="color: red;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px"><strong>&nbsp;</strong></span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px"><strong>五、案例分析题（共</strong><strong>10</strong><strong>分，每题</strong><strong>2</strong><strong>分）</strong></span></p><p class="MsoListParagraph" style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">1、<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;</span>台湾同事反馈已收到会员寄回的发票改开资料，但会员后台并未修改新的统编资料，请问高级客服该如何协助台湾同事处理？</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;<img src="https://zsk.591.com.tw/upload/question/20201106/1604647694284985.png" alt="image.png"></span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p class="MsoListParagraph" style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">2、<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;</span>台湾同事反馈发票资料有特殊字体，若联络不上会员该如何处理？</span></p><p style="white-space: normal"><br></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;<img src="https://zsk.591.com.tw/upload/question/20201106/1604647702253788.png" alt="image.png"></span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p class="MsoListParagraph" style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">3、<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span>台湾同事对账时有发现重复账单，请问高级客服该如何协助台湾同事处理？</span></p><p style="white-space: normal"><br></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;<img src="https://zsk.591.com.tw/upload/question/20201106/1604647710425972.png" alt="image.png"></span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p class="MsoListParagraph" style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">4、<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span>查看下图：该笔储值状态是代表什么意思？若储值账号错误该如何处理？</span></p><p style="white-space: normal"><br></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;<img src="https://zsk.591.com.tw/upload/question/20201106/1604647716391141.png" alt="image.png"></span></p><p style="white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p class="MsoListParagraph" style="margin-left: 28px;white-space: normal"><span style="font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">5、<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;&nbsp;</span>查看下图：对账时发现蓝新有一笔账错误，请问高级客服该如何协助台湾同事处理？</span></p><p style="white-space: normal"><br></p><p style="white-space: normal"><img src="https://zsk.591.com.tw/upload/question/20201106/1604647723206832.png" alt="image.png"></p><p><br></p><p><br></p><p><br></p><p style="white-space: normal; text-align: center;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">T5-2020年四星故障实操题（满分：40分）</span></strong></p><p style="margin-right: 28px; white-space: normal; text-align: right;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">考试时长：30分钟；监考人：陈敏南、黄雪静</span></strong></p><p style="white-space: normal; text-align: center;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">姓名： ____ 客服工号：________ 分数：________</span></strong></p><p style="margin-left: 34px; white-space: normal;"><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></strong></p><p style="margin-left: 34px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>一、&nbsp;</strong><strong>电话故障（共20分，每题4分）</strong></span></p><p style="margin-left: 62px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、 当电话出现音质不好的时候应该如何判断问题</span></p><p style="margin-left: 62px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、 九点上班查看ping值正常，IVR有进线记录但电话未响，该如何查询及处理</span></p><p style="margin-left: 62px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、 电话系统如何查看及修改固定IP资料</span></p><p style="margin-left: 62px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4、 转接手机如何操作</span></p><p style="margin-left: 62px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5、 延伸题，时间如何设定</span></p><p style="margin-left: 34px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;</span></p><p style="margin-left: 34px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>二、&nbsp;</strong><strong>储值故障（共10分，每题2分）</strong></span></p><p style="margin-left: 62px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、 全家储值正常，其中一个会员未入账该如何操作处理</span></p><p style="margin-left: 62px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、 銀行未入賬的操作步骤处理方式</span></p><p style="margin-left: 62px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、 7-11储值故障，与厂商沟通时你首先要提供什么资料（可查询表格）</span></p><p style="margin-left: 62px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4、 延伸题，如何与厂商沟通（至少获取关键要素3点）</span></p><p style="margin-left: 62px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5、 国泰世华故障确认会超过半小时以上，我们该做什么</span></p><p style="margin-left: 34px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;</span></p><p style="margin-left: 34px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong>三、&nbsp;</strong><strong>网路故障（共10分，每题2分）</strong></span></p><p style="margin-left: 58px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1、打开591前台正常，后台很慢要如何判断问题及如何处理</span></p><p style="margin-left: 58px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2、某个浏览器打开591网页很慢，要如何处理</span></p><p style="margin-left: 58px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3、如何更改Dns</span></p><p style="margin-left: 58px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4、会员无法进入591，客服进入正常，要如何测试会员反馈的问题属实</span></p><p style="margin-left: 58px; white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5、 前后台打开都慢，其它站台也一样，如何判断是公司内网还是外网的问题</span></p></div></div> <div><span class="button">我要編輯</span></div></li></ul> <h3 class="more-answer">我要補充</h3> <div style=""><div id="editor_77918137094832060" class="edui-default" style=""><div id="edui1" class="edui-editor  edui-default" style="width: 100%; z-index: 999;"><div id="edui1_toolbarbox" class="edui-editor-toolbarbox edui-default"><div id="edui1_toolbarboxouter" class="edui-editor-toolbarboxouter edui-default"><div class="edui-editor-toolbarboxinner edui-default"><div id="edui2" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui2&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui3" class="edui-box edui-button edui-for-fullscreen edui-default"><div id="edui3_state" onmousedown="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui3_body" unselectable="on" title="全屏" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui3&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui3&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui4" class="edui-box edui-button edui-for-source edui-default"><div id="edui4_state" onmousedown="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui4_body" unselectable="on" title="源代码" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui4&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui4&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui5" class="edui-box edui-button edui-for-undo edui-default"><div id="edui5_state" onmousedown="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui5_body" unselectable="on" title="撤销" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui5&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui5&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui6" class="edui-box edui-button edui-for-redo edui-default"><div id="edui6_state" onmousedown="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui6_body" unselectable="on" title="重做" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui6&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui6&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui7" class="edui-box edui-button edui-for-simpleupload edui-default"><div id="edui7_state" onmousedown="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui7_body" unselectable="on" title="单图上传" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui7&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui7&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"><iframe style="display: block; width: 20px; height: 20px; overflow: hidden; border: 0px; margin: 0px; padding: 0px; position: absolute; top: 0px; left: 0px; opacity: 0; cursor: pointer;"></iframe></div></div></div></div></div><div id="edui8" class="edui-box edui-button edui-for-imageleft edui-default"><div id="edui8_state" onmousedown="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui8_body" unselectable="on" title="左浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui8&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui8&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui9" class="edui-box edui-button edui-for-imageright edui-default"><div id="edui9_state" onmousedown="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui9_body" unselectable="on" title="右浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui9&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui9&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui10" class="edui-box edui-button edui-for-imagecenter edui-default"><div id="edui10_state" onmousedown="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui10_body" unselectable="on" title="居中" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui10&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui10&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui11" class="edui-box edui-separator  edui-default"></div><div id="edui12" class="edui-box edui-button edui-for-date edui-default"><div id="edui12_state" onmousedown="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui12_body" unselectable="on" title="日期" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui12&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui12&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui13" class="edui-box edui-button edui-for-time edui-default"><div id="edui13_state" onmousedown="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui13_body" unselectable="on" title="时间" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui13&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui13&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui14" class="edui-box edui-button edui-for-horizontal edui-default"><div id="edui14_state" onmousedown="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui14_body" unselectable="on" title="分隔线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui14&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui14&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui15" class="edui-box edui-separator  edui-default"></div><div id="edui16" class="edui-box edui-splitbutton edui-for-inserttable edui-default"><div title="插入表格" id="edui16_state" onmousedown="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui16_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onArrowClick();"></div></div></div></div><div id="edui19" class="edui-box edui-button edui-for-deletetable edui-default"><div id="edui19_state" onmousedown="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui19_body" unselectable="on" title="删除表格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui19&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui19&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui20" class="edui-box edui-button edui-for-insertrow edui-default"><div id="edui20_state" onmousedown="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui20_body" unselectable="on" title="前插入行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui20&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui20&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui21" class="edui-box edui-button edui-for-insertcol edui-default"><div id="edui21_state" onmousedown="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui21_body" unselectable="on" title="前插入列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui21&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui21&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui22" class="edui-box edui-button edui-for-mergeright edui-default"><div id="edui22_state" onmousedown="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui22_body" unselectable="on" title="右合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui22&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui22&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui23" class="edui-box edui-button edui-for-mergedown edui-default"><div id="edui23_state" onmousedown="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui23_body" unselectable="on" title="下合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui23&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui23&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui24" class="edui-box edui-button edui-for-deleterow edui-default"><div id="edui24_state" onmousedown="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui24_body" unselectable="on" title="删除行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui24&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui24&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui25" class="edui-box edui-button edui-for-deletecol edui-default"><div id="edui25_state" onmousedown="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui25_body" unselectable="on" title="删除列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui25&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui25&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui26" class="edui-box edui-button edui-for-splittorows edui-default"><div id="edui26_state" onmousedown="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui26_body" unselectable="on" title="拆分成行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui26&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui26&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui27" class="edui-box edui-button edui-for-splittocols edui-default"><div id="edui27_state" onmousedown="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui27_body" unselectable="on" title="拆分成列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui27&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui27&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui28" class="edui-box edui-button edui-for-splittocells edui-default"><div id="edui28_state" onmousedown="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui28_body" unselectable="on" title="完全拆分单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui28&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui28&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui29" class="edui-box edui-button edui-for-mergecells edui-default"><div id="edui29_state" onmousedown="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui29_body" unselectable="on" title="合并多个单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui29&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui29&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui36" class="edui-box edui-button edui-for-edittable edui-default"><div id="edui36_state" onmousedown="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui36_body" unselectable="on" title="表格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui36&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui36&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui41" class="edui-box edui-button edui-for-edittd edui-default"><div id="edui41_state" onmousedown="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui41_body" unselectable="on" title="单元格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui41&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui41&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui42" class="edui-box edui-separator  edui-default"></div><div id="edui43" class="edui-box edui-menubutton edui-for-lineheight edui-default"><div title="行间距" id="edui43_state" onmousedown="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui43_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onArrowClick();"></div></div></div></div><div id="edui52" class="edui-box edui-menubutton edui-for-rowspacingtop edui-default"><div title="段前距" id="edui52_state" onmousedown="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui52_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onArrowClick();"></div></div></div></div><div id="edui59" class="edui-box edui-menubutton edui-for-rowspacingbottom edui-default"><div title="段后距" id="edui59_state" onmousedown="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui59_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onArrowClick();"></div></div></div></div></div><div id="edui66" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui66&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui67" class="edui-box edui-combox edui-for-fontfamily edui-default"><div title="字体" id="edui67_state" onmousedown="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui67_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onButtonClick(event, this);">字体</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onArrowClick();"></div></div></div></div><div id="edui80" class="edui-box edui-combox edui-for-customstyle edui-default"><div title="自定义标题" id="edui80_state" onmousedown="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui80_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onButtonClick(event, this);">自定义标题</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onArrowClick();"></div></div></div></div><div id="edui86" class="edui-box edui-combox edui-for-paragraph edui-default"><div title="段落格式" id="edui86_state" onmousedown="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui86_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onButtonClick(event, this);">段落格式</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onArrowClick();"></div></div></div></div><div id="edui95" class="edui-box edui-combox edui-for-fontsize edui-default"><div title="字号" id="edui95_state" onmousedown="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui95_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onButtonClick(event, this);">字号</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onArrowClick();"></div></div></div></div><div id="edui110" class="edui-box edui-button edui-for-link edui-default"><div id="edui110_state" onmousedown="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui110_body" unselectable="on" title="超链接" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui110&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui110&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui111" class="edui-box edui-button edui-for-indent edui-default"><div id="edui111_state" onmousedown="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui111_body" unselectable="on" title="首行缩进" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui111&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui111&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui112" class="edui-box edui-button edui-for-bold edui-default"><div id="edui112_state" onmousedown="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui112_body" unselectable="on" title="加粗" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui112&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui112&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui113" class="edui-box edui-splitbutton edui-for-forecolor edui-default edui-colorbutton"><div title="字体颜色" id="edui113_state" onmousedown="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui113_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui113_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onArrowClick();"></div></div></div></div><div id="edui116" class="edui-box edui-splitbutton edui-for-backcolor edui-default edui-colorbutton"><div title="背景色" id="edui116_state" onmousedown="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui116_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui116_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onArrowClick();"></div></div></div></div><div id="edui119" class="edui-box edui-button edui-for-italic edui-default"><div id="edui119_state" onmousedown="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui119_body" unselectable="on" title="斜体" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui119&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui119&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui120" class="edui-box edui-button edui-for-underline edui-default"><div id="edui120_state" onmousedown="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui120_body" unselectable="on" title="下划线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui120&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui120&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui121" class="edui-box edui-button edui-for-strikethrough edui-default"><div id="edui121_state" onmousedown="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui121_body" unselectable="on" title="删除线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui121&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui121&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui122" class="edui-box edui-button edui-for-superscript edui-default"><div id="edui122_state" onmousedown="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui122_body" unselectable="on" title="上标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui122&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui122&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui123" class="edui-box edui-button edui-for-subscript edui-default"><div id="edui123_state" onmousedown="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui123_body" unselectable="on" title="下标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui123&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui123&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui124" class="edui-box edui-button edui-for-removeformat edui-default"><div id="edui124_state" onmousedown="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui124_body" unselectable="on" title="清除格式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui124&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui124&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui125" class="edui-box edui-button edui-for-blockquote edui-default"><div id="edui125_state" onmousedown="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui125_body" unselectable="on" title="引用" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui125&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui125&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui126" class="edui-box edui-button edui-for-pasteplain edui-default"><div id="edui126_state" onmousedown="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui126_body" unselectable="on" title="纯文本粘贴模式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui126&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui126&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui127" class="edui-box edui-menubutton edui-for-insertorderedlist edui-default"><div title="有序列表" id="edui127_state" onmousedown="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui127_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onArrowClick();"></div></div></div></div><div id="edui140" class="edui-box edui-menubutton edui-for-insertunorderedlist edui-default"><div title="无序列表" id="edui140_state" onmousedown="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui140_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onArrowClick();"></div></div></div></div><div id="edui147" class="edui-box edui-button edui-for-cleardoc edui-default"><div id="edui147_state" onmousedown="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui147_body" unselectable="on" title="清空文档" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui147&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui147&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div></div></div></div><div id="edui1_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;"><div id="edui1_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui1&quot;].showWordImageDialog();">点击上传</div><div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui1&quot;].hideToolbarMsg();">x</div><div id="edui1_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div><div style="height:0;overflow:hidden;clear:both;" class="edui-default"></div></div><div id="edui1_message_holder" class="edui-editor-messageholder edui-default"></div></div><div id="edui1_iframeholder" class="edui-editor-iframeholder edui-default" style="width: 100%; height: 300px; z-index: 999; overflow: hidden;"><iframe id="ueditor_0" width="100%" height="100%" frameborder="0" src="javascript:void(function(){document.open();document.write(&quot;<!DOCTYPE html><html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}
body{margin:8px;font-family:sans-serif;font-size:13px;}p{margin:5px 0;}</style><link rel='stylesheet' type='text/css' href='/static/ueditor/themes/iframe.css'/></head><body class='view' ></body><script type='text/javascript'  id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant0'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);</script></html>&quot;);document.close();}())"></iframe></div><div id="edui1_bottombar" class="edui-editor-bottomContainer edui-default"><table class="edui-default"><tbody class="edui-default"><tr class="edui-default"><td id="edui1_elementpath" class="edui-editor-bottombar edui-default"><div class="edui-editor-breadcrumb">元素路径:</div></td><td id="edui1_wordcount" class="edui-editor-wordcount edui-default">字数统计</td><td id="edui1_scale" class="edui-editor-scale edui-default" style="display: none;"><div class="edui-editor-icon edui-default"></div></td></tr></tbody></table></div><div id="edui1_scalelayer" class="edui-default"></div></div></div> <div class="edit-btn"><button type="button" class="button">提交回答</button> <label data-v-0fe26aab="" for="commonCheckbox" class="checkbox"><input data-v-0fe26aab="" type="checkbox" id="commonCheckbox" name="commonCheckbox"> <i data-v-0fe26aab=""></i> 需要彈窗，請勾選</label></div></div></div> <div class="index-right"><div class="right-bk"><h3 class="com-title"><a href="javascript:;">我的最爱</a></h3> <div class="item-box clearfix"><span>暫無數據</span></div></div> <div class="hotTag right-bk"><h3 class="com-title">热门标签</h3> <div class="hotTag-box item-box clearfix"><p>無數據</p></div></div> <div class="right-bk rank"><h3 class="com-title"><a href="javascript:;">冠軍排行榜</a></h3> <ul><li><div><span class="number">1</span> <img src="/static/timg.jpeg"> <span>王菊芳</span></div> <span class="score">612分</span></li><li><div><span class="number">2</span> <img src="/upload/head/10177.gif?time=1556271256"> <span>黃雪靜</span></div> <span class="score">258分</span></li><li><div><span class="number">3</span> <img src="/static/timg.jpeg"> <span>田霞</span></div> <span class="score">240分</span></li><li><div><span class="number">4</span> <img src="/static/timg.jpeg"> <span>曹超雲</span></div> <span class="score">168分</span></li><li><div><span class="number">5</span> <img src="/static/timg.jpeg"> <span>文宇</span></div> <span class="score">96分</span></li><li><div><span class="number">6</span> <img src="/upload/head/10217.png?time=1548059679"> <span>黃興興</span></div> <span class="score">78分</span></li><li><div><span class="number">7</span> <img src="/static/timg.jpeg"> <span>黃佳麗</span></div> <span class="score">62分</span></li><li><div><span class="number">8</span> <img src="/static/timg.jpeg"> <span>马泽鹏</span></div> <span class="score">46分</span></li><li><div><span class="number">9</span> <img src="/static/timg.jpeg"> <span>陳敏南</span></div> <span class="score">40分</span></li><li><div><span class="number">10</span> <img src="/static/timg.jpeg"> <span>Cs-刘婷</span></div> <span class="score">10分</span></li><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></ul></div> <div class="activeUser right-bk"><h3 class="com-title">活跃用户</h3> <div class="activeUser-box"><a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a></div></div> <img src="data:image/png;base64,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" class="to-top"></div></div> <!----> <!----> <!----> <div class="page-cnt footer"><p>T5客服知识库<span>|</span>zsk.591.com.tw</p></div></div></div><script type="text/javascript" src="/js/manifest.f192b.js"></script><script type="text/javascript" src="/js/vendor.17570.js"></script><script type="text/javascript" src="/js/app.58254.js"></script><div id="edui_fixedlayer" class="edui-default" style="position: fixed; left: 0px; top: 0px; width: 0px; height: 0px;"><div id="edui148" class="edui-popup  edui-bubble edui-default" onmousedown="return false;" style="display: none;"> <div id="edui148_body" class="edui-popup-body edui-default"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank" class="edui-default"></iframe> <div class="edui-shadow edui-default"></div> <div id="edui148_content" class="edui-popup-content edui-default">  </div> </div></div></div></body></html>