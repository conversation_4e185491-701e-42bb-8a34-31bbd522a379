<html class=" "><head><meta charset="utf-8"><title>591客服知識庫</title><link rel="stylesheet" href="/static/font/font-awesome.min.css"><link href="/css/app.e5aa4.css" rel="stylesheet"><script type="text/javascript" charset="utf-8" async="" src="/js/questionDetail.d0a6b.js"></script><style type="text/css">.que-detail-page .index-left .button{width:74px;height:32px;line-height:32px;font-size:13px;margin-right:10px}.que-detail-page .index-left .button:hover{color:#fff}.que-detail-page .detail-box{padding:20px 30px;border:1px solid #e5e5e5;border-radius:4px}.que-detail-page .detail-box h3{font-size:18px;color:#333;margin-bottom:10px}.que-detail-page .detail-box h3 span{color:#f77d24}.que-detail-page .detail-box .add-fav{cursor:pointer}.que-detail-page .d-b-menu{margin:15px 0 25px;font-size:13px;color:#999}.que-detail-page .d-b-menu b{display:inline-block;font-weight:400;margin:0 10px}.que-detail-page .d-b-menu .r{float:right;color:#f77d24}.que-detail-page .d-b-menu .r .fa{margin-right:5px;font-size:16px}.que-detail-page .d-b-cnt{margin-bottom:25px;font-size:13px;color:#666}.que-detail-page .submit-ask{display:block;margin:32px auto 0}.que-detail-page .reply-list{margin:30px 0 10px;overflow:hidden}.que-detail-page .reply-list .reply-item{padding:15px 30px 25px;margin-bottom:30px;border-radius:4px;border:1px solid #e5e5e5}.que-detail-page .reply-list .reply-item h4{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;line-height:50px;font-size:13px}.que-detail-page .reply-list .reply-item h4 img{width:40px;height:40px;border-radius:50%}.que-detail-page .reply-list .reply-item h4 .time{color:#999}.que-detail-page .reply-list .reply-item h4 .button{-ms-flex-item-align:center;align-self:center}.que-detail-page .reply-list .reply-item h4 .delete{color:#666;cursor:pointer}.que-detail-page .reply-list .reply-item .reply-detail{padding:20px 0 30px}.que-detail-page .more-answer{font-size:18px;color:#666;margin-bottom:5px}.que-detail-page .editor-content table{width:100%;border:1px solid #ddd}.que-detail-page .editor-content table td,.que-detail-page .editor-content table th{border:1px solid #ddd;padding:2px 8px;line-height:2}.que-detail-page .editor-content img{max-width:100%;vertical-align:text-bottom}.que-detail-page .edit-btn{text-align:center;margin-top:20px}.que-detail-page .edit-btn .edit-cancle{background:#aaa}</style><style type="text/css">.head-info{line-height:26px;background:#f5f5f5;font-size:13px;color:#999}.head-info a{margin-left:20px;color:#333}.head-search{margin-top:22px}.head-search img{float:left}.head-search .srh-box{float:left;margin:20px 0 0 20px}.head-search .srh-box .srh-input{float:left;width:260px;height:38px;line-height:38px;border:2px solid #37a508;border-radius:4px 0 0 4px}.head-search .srh-box a,.head-search .srh-box button{float:left;width:80px;height:38px;line-height:36px;font-size:16px;color:#fff}.head-search .srh-box .srh-btn{border-radius:0 4px 4px 0}.head-search .srh-box .ask-btn{margin-left:10px}.head-nav{float:left;margin:20px 0 0 60px;color:#fff;font-size:16px;line-height:36px}.head-nav a{display:block;height:100%;color:#333}.head-nav li{position:relative;float:left;margin-right:35px}.head-nav .active a,.head-nav li:hover a{color:#30b940}.head-nav .active:after,.head-nav li:hover:after{display:block;content:"";height:3px;width:100%;background:#30b940}.head-pic{width:100%;height:90px;margin-top:20px;background:url(/static/banner.png) top no-repeat}</style><style type="text/css">.top-breadcrumb{height:20px;margin-top:10px;margin-bottom:40px;font-size:13px;color:#666}.top-breadcrumb .t-b-icon{color:#30b940}.top-breadcrumb span{margin-left:8px}.top-breadcrumb span .fa{font-size:16px;margin-left:8px}.top-breadcrumb span:first-child{margin-left:0}.top-breadcrumb .now{color:#30b940}</style><script type="text/javascript" src="/static/ueditor/ueditor.config.js?20210319" id="configScriptTag"></script><script type="text/javascript" src="/static/ueditor/ueditor.all.js?20210319" id="editorScriptTag"></script><style type="text/css">.checkbox[data-v-0fe26aab]{word-spacing:4px;vertical-align:bottom;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.checkbox i[data-v-0fe26aab]{display:inline-block;width:14px;height:14px;border-radius:2px;border:1px solid #ccc;background:#fff;position:relative;top:1px}.checkbox input[data-v-0fe26aab]{opacity:0;width:0;height:0;margin:0}.checkbox input:checked+i[data-v-0fe26aab]{background:#30b940;border-color:#30b940}.checkbox input:checked+i[data-v-0fe26aab]:before{content:"";position:absolute;left:2px;top:1px;width:11px;height:6px;border-bottom:2px solid #fff;border-left:2px solid #fff;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}</style><style type="text/css">.popbox{position:fixed;top:50%;left:50%;z-index:1011;background:#fff;border-radius:6px;overflow:hidden}.popbox h3{font-size:16px;line-height:40px;padding:0 20px}.popbox h3 span{float:right;color:#666;font-size:13px;cursor:pointer}.popbox .pb-cnt{padding:0 20px;font-size:13px}.popbox .pb-btn-area{position:absolute;bottom:0;left:0;z-index:1;width:100%;text-align:right;padding:0 20px 20px 0}.popbox .pb-btn-area button{height:32px;line-height:32px;border:0;background:#fff;cursor:pointer;border-radius:6px;margin-left:15px;padding:0 15px}.popbox .pb-btn-area button.pb-btn-ok{background:#daf3cf;color:#279005}.popbox .pb-btn-area button.pb-btn-ok:hover{background:#279005;color:#fff}</style><style type="text/css">.shadow{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1010;background:#000}</style><style type="text/css">.footer{margin-top:70px;margin-bottom:20px;padding-top:20px;text-align:center;color:#999;border-top:1px solid #f5f5f5}.footer span{margin:0 10px}</style><script src="/static/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript" defer="defer"></script><link href="/static/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet"><script src="/static/ueditor/third-party/codemirror/codemirror.js" type="text/javascript" defer="defer"></script><link rel="stylesheet" type="text/css" href="/static/ueditor/third-party/codemirror/codemirror.css"><script src="/static/ueditor/third-party/zeroclipboard/ZeroClipboard.js" type="text/javascript" defer="defer"></script><style type="text/css">.activeUser,.favorite,.hotTag,.rank{margin-bottom:40px}</style><style type="text/css">.rank .rank-page{height:60px;line-height:60px;font-size:18px;margin-bottom:20px;text-align:center;background:#fafafa;border-radius:5px}.rank ul{padding:0 20px}.rank li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;border-bottom:1px dotted #f5f5f5;line-height:50px}.rank li .flag{position:relative;display:inline-block;width:20px;height:30px;background:#476472;vertical-align:middle;margin-right:12px;color:#fff;text-align:center;line-height:20px;font-size:13px;font-style:normal}.rank li .flag:after{content:"";position:absolute;bottom:-4px;left:-4px;border:14px solid transparent;border-bottom-color:#fff}.rank li:first-child .flag{background:#fb6757}.rank li:nth-child(2) .flag{background:#fac525}.rank li:nth-child(3) .flag{background:#00a9fb}.rank li img{width:36px;height:36px;border-radius:50%;margin-right:12px}</style><style type="text/css">.activeUser .activeUser-box a{display:inline-block;width:64px;height:64px;border-radius:50%;overflow:hidden;margin:0 6px 12px}</style><style type="text/css">.to-top{display:none;position:fixed;bottom:150px;z-index:5;width:50px;height:50px;cursor:pointer}</style></head><body style=""><div id="app" style=""><div class="que-detail-page" style=""><div class="wiki-hd"><div class="head-info"><p class="page-cnt">
      您好，欢迎来到591客服知识库
      <a href="https://www.591.com.tw/admin.php?new_url=https://zsk.591.com.tw">[请登入]</a> <!----></p></div> <div class="page-cnt head-search clearfix"><a href="/" class="router-link-active"><img src="/static/logo.png"></a> <ul class="head-nav clearfix"><li class="router-link-active active"><a href="/">591房屋交易网</a></li> <li class=""><a href="/life">客服充电站</a></li> <li class=""><a href="/notification">通知事项</a></li></ul> <div class="srh-box"><input type="text" placeholder="请输入搜索内容" class="srh-input"> <button type="button" class="button srh-btn">搜索</button> <a href="/ask/591" class="button button-orange ask-btn">提問</a></div></div> <div class="head-pic"></div></div> <div class="top-breadcrumb com-width"><span><a href="/collection/591/1185" class="">產品支持專員<i class="fa fa-angle-double-right"></i></a></span><span><a href="/collection/591/1185/1644" class="">作業指導書<i class="fa fa-angle-double-right"></i></a></span><span><strong class="now">问题详情</strong></span></div> <div class="com-width clearfix" style=""><div class="index-left" style=""><div class="detail-box"><h3><!---->公文</h3> <p class="clearfix d-b-menu"><span class="l">
            马泽鹏&nbsp;&nbsp;&nbsp;浏览101次&nbsp;&nbsp;&nbsp;2019-12-24 17:35:27
          </span> <span class="r"><span class="add-fav"><i class="fa fa-heart"></i>加入最爱</span></span></p> <div class="d-b-cnt editor-content big-img-parent"><div><p style="white-space: normal;"><br></p><p style="white-space: normal;"><img src="/upload/question/20191226/1577360153274564.png" alt="image.png"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong style="color: rgb(12, 12, 12); font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; white-space: normal; font-size: 18px;">&nbsp;<img src="/upload/question/20191226/1577333044414488.png" alt="image.png"><br><br><img src="/upload/question/20191225/1577268022220055.png" alt="image.png"></strong></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong style="color: rgb(12, 12, 12); font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; white-space: normal; font-size: 18px;"><br></strong></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><strong style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; white-space: normal;"><span style="color: #0C0C0C;"><img src="/upload/question/20191226/1577333514138497.png" alt="image.png"></span></strong></p><p style="white-space: normal;"><br></p><p style="white-space: normal;">&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<img src="/upload/question/20191226/1577329732566874.png" alt="image.png"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">● <span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px; color: #000000; background-color: #FFFFFF;">位置：</span>192.168.8.29PublicFile591房屋交易网（台湾）【12】客服相关【0105】产品支持公文<br><br></span></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; ● <span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px; background-color: #FFFFFF;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px; background-color: #FFFFFF;">公文 - 目錄.xlsx ：</span>用</span>於記錄每年的公文，需要查找公文時使用此表格搜尋即可<br><br></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px; background-color: #FFFFFF;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;"> &nbsp; &nbsp; &nbsp; ● <span style="font-size: 16px; color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">公文 - 會員個資調閱申請單.xlsx ：</span></span>公司內部如有他人調取會員個資，用於提供給他人填寫申請單<br><br></span></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; ● 公文 - 會員個資調閱登記表.xlsx ：用於記錄公司內部他人的資料<br><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; ● 模板 - 會員資料.xlsx ：處理索取資料類公文使用（會員基本資料+歷史刊登資料）<br><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; ● 模板 - 歷史資料.xlsx ：處理索取資料類公文使用（歷史刊登資料的整理）<br><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; ● 模板 - 文字說明.xlsx ：文字描述備註說明使用（索取資料類、消基會類、協助下架類、）<br><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; ● 模板 - 建議性回覆.xlsx ：處理功能建議類公文使用<br><br></span></p><p style="white-space: normal;"><span style="background-color: #FFFFFF;"><span style="font-size: 16px; color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; ● </span><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000; background-color: #FFFFFF;">模板 - 公平交易回覆（會員刊登）.xlsx ：</span><span style="font-size: 16px; color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">處理公平交易類公文使用（建案刊登者為會員）<br><br></span></span></p><p style="white-space: normal;"><span style="font-size: 16px; color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; ● 模板 - 公平交易回覆（編輯刊登）.xlsx ：處理</span><span style="font-size: 16px; color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">公平交易類公文使用（建案刊登者為編輯）</span></p><p style="white-space: normal;"><span style="font-size: 16px; color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong style="white-space: normal; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px;"><span style="color: #0C0C0C;"><img src="/upload/question/20191226/1577333527407602.png" alt="image.png"></span></strong></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px; color: #000000; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp;1. 公文儲存位置：<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000; font-size: 14px; background-color: #FFFFFF;">192.168.8.29PublicFile591房屋交易网（台湾）【12】客服相关【0105】产品支持公文台湾</span></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px; color: #000000; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp;2.&nbsp;公文分類：</span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; ● 一級目錄，按年分類（ 命名為 2019年、2018年、2017年等等.. ）<br></span></p><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; ● 二級目錄，按月分類（&nbsp;命名為&nbsp;1月、2月、3月、4月等等.. ）</span></p><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; ● 三級目標，按日分類（&nbsp;命名為&nbsp;0104-591公文2份、0109-591公文2份等等.. ）<br></span></p><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; ● 四级目錄，按份分類（命名為&nbsp;ID：33006 王學文（回覆 新北市政府地政局 函））</span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp;&nbsp;<img src="https://zsk.591.com.tw/upload/question/20191226/1577329873551152.png" alt="image.png" width="753" height="383" style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; white-space: normal; width: 753px; height: 383px;"></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp;3. 接收的公文 - 保存規則：</span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; ● 接送到的公文，均已壓縮文件夾的形式接收公文</span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; ●&nbsp;通常文件夾名為0104-591公文2份，意思為接收時間為1月4日，內有591的公文共2份</span></p><p style="white-space: normal;"><span style="font-size: 16px; color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; ● 接收後按月份進行保存公文即可，例如文件夾名為0104-591公文2份，保存在當月的一月份即可</span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px; background-color: #FFFFFF;"><br></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px; background-color: #FFFFFF;"><br></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp;4. 命名說明（單個公文）：</span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; ● 公文內容針對某個會員，命名格式：ID：xxx 姓名（回覆 xx政府 函）<br></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;例如：ID：33006 王學文（回覆 新北市政府地政局 函）<br><br></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; ● 公文內容未針對某個會員，命名格式：（回覆 xx政府 函）</span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;例如：回覆 新北市政府地政局 函</span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"></span></p><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px;"><strong><span style="color: #0C0C0C;"><img src="/upload/question/20191226/1577333538181898.png" alt="image.png"></span></strong></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; ● 索取資料類&nbsp; &nbsp;（ 來函要求我們協助提供會員基本資料、歷史刊登資料、物件刊登頁截圖等等.. ）</span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; ● 公平交易類&nbsp; &nbsp;（ 網站有出現資訊不實的建案，來函要求說明物件來源等等.. ）</span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; ● 消費點數爭議類 （ 會員至消基會或地方政府檢舉消費爭議之事，之後來函要求我們與會員妥善處理）<br></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; ● 功能建議類&nbsp; &nbsp;（ 政府發現網站有不合理的功能，會來函建議我們做調整 ）</span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; ● 協助下架類&nbsp; &nbsp;（ 政府發現網站有違反相關法律的廣告，會來函要求我們做下架 ）</span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><br></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><br></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><br></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><br></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><br></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><br></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><br></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><br></span></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><br></span></p></div></div> <div class="d-b-button"><a href="/edit/591/3942" class="button">编辑</a> <button class="button button-orange">关闭提问</button></div></div> <div class="hr-line"></div> <ul class="reply-list big-img-parent"><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">马泽鹏</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2019-12-26 10:20:31</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="white-space: normal;"><img src="/upload/question/20191226/1577334060619879.png" alt="image.png"></p><p style="white-space: normal;"><strong style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px;"><span style="color: #0C0C0C;"><br></span></strong></p><p style="white-space: normal;"><span style="color: #0C0C0C; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<img src="/upload/question/20191226/1577339888696776.png" alt="image.png"></span></p><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp;</span><strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFF00;">● 介紹</span></strong><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFF00;"><strong>：</strong></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="background-color: #FDEADA;">通常來調取會員資料的有兩種，一種是違反經紀業管理條例的（非仲介身份刊登收取服務費的物件）<br><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span>從而需要從我們這裡調取會員資料，還有一種是警察局處理的刑案與賬號有關聯，所以來函調取會員</span></span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style="background-color: #FDEADA; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">資料。</span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; <span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFF00;">●&nbsp;<strong>快速讀取技巧：</strong></span><br></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">1. 查看主旨，了解來函原因（辦理不動產經紀業管理業務）</span><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">了解來函需求（所屬會員相關資料）</span><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<img src="https://zsk.591.com.tw/upload/question/20191225/1577272201477756.png" alt="image.png"></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">2. 查看說明三，了解索取的會員賬號（物件編號：R7065338）</span><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">了解詳細的需求（提供廣告刊登者之會員資料、刊登明細）</span><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">了解是否有附件資料（隨函檢附前揭網頁廣告供參）</span><br><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<img src="https://zsk.591.com.tw/upload/question/20191225/1577272261481016.png" alt="image.png"><br><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;"> 3. 簡化這份公文的意思：</span><br><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">我有收到民眾檢舉這筆廣告刊登者並非仲介從業人員，但是廣告身份以仲介刊登，</span><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">已經違反了相關法律，請你提供這筆廣告刊登者的賬號基本資料、歷史刊登資料。</span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; <span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFF00;">●&nbsp;<strong>處理流程：</strong></span></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">1. 需要使用到的文件：&lt;模板 - 會員資料.xlsx&gt;&nbsp; &lt;模板 - 歷史資料.xlsx&gt;</span><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577273463603112.png" alt="image.png"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">2. 利用公文中的物件編號，查詢是哪個會員賬號，進入後台會員詳細資料頁面</span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577273533561794.png" alt="image.png" width="572" height="112" style="width: 572px; height: 112px;"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">3. 進入會員詳細資料頁面後，打開如下圖的三個頁面&lt;點數明細&gt;&lt;發票資訊&gt;&lt;總筆數&gt;</span></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577273674443495.png" alt="image.png" width="692" height="429" style="width: 692px; height: 429px;"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">4. 打開文件，&lt;模板 - 會員資料.xlsx&gt;&nbsp;</span></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577273869510138.png" alt="image.png"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">● TO：填寫來函的政府單位（例如：台北市政府地政局）</span></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">● 賬號、姓名、行動電話、電子郵箱、註冊時間與IP、最後登入時間與IP（如下圖）</span><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;"><br></span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577274199316854.png" alt="image.png" width="679" height="231" style="width: 679px; height: 231px;"></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">● 發票資料，進入後台&lt;發票資訊&gt;頁（如下圖）</span><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;"><br></span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577274295390182.png" alt="image.png" width="676" height="315" style="width: 676px; height: 315px;"></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ps：若賬號發票資料屬於個人發票，沒有公司名稱的，直接將表格的&lt;公司抬頭&gt;欄位刪除即可</span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">5. 進入後台&lt;點數明細&gt;頁面，複雜賬號歷史支出的所有數據（如下圖）</span><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577274701186470.png" alt="image.png" width="666" height="513" style="width: 666px; height: 513px;"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">6. 複製以後，打開文件，&lt;模板 - 歷史資料.xlsx&gt;的&lt;三合一&gt;工作表（如下圖）</span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577274874764850.png" alt="image.png"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">7. 將複製的內容，粘貼至此表單A列處（如下圖）</span><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;"><br></span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577274953889135.png" alt="image.png" width="648" height="386" style="width: 648px; height: 386px;"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">8. 進入後台&lt;總筆數&gt;頁面，複雜賬號歷史刊登所有數據（如下圖）</span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;"><br></span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577275101165820.png" alt="image.png" width="669" height="406" style="width: 669px; height: 406px;"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">8.&nbsp;複製以後，進入文件，&lt;模板 - 歷史資料.xlsx&gt;的&lt;三合一&gt;工作表（如下圖）</span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577274874764850.png" alt="image.png"><br><br><br><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">9.&nbsp;將複製的內容，粘貼至此表單E列處（如下圖）</span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;"><br></span></span></p><p style="white-space: normal;"><img src="https://zsk.591.com.tw/upload/question/20191225/1577275185838026.png" alt="image.png" width="1" height="1" style="width: 1px; height: 1px;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577275270609700.png" alt="image.png" width="651" height="282" style="width: 651px; height: 282px;"><br><br><br><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">10. 對比兩邊的物件數據是否一一對應，不對應的數據需要刪除（如下圖）</span><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;"><br></span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577275441626800.png" alt="image.png" width="661" height="201" style="width: 661px; height: 201px;"><br><br><br><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">11. 對比數據後，再刪除B、C、D、G列數據（如下圖）</span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577275621467876.png" alt="image.png" width="667" height="162" style="width: 667px; height: 162px;"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">12. 刪除B、C、D、G列數據後（如下圖）</span><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;"><br></span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577275737486405.png" alt="image.png" width="658" height="308" style="width: 658px; height: 308px;"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">13. 將D列的數據中去除物件標題的文字（具體操作如下）</span><br></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">14. 框選D列數據 （如下圖）</span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577276022328997.png" alt="image.png" width="652" height="276" style="width: 652px; height: 276px;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">15. 使用excel中的&lt;查找和替換工具&gt;，查詢內容：&lt;*路&gt;&nbsp; &nbsp; 替換為&lt;路&gt; ，之後點擊&lt;全部替換&gt;</span><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">（如下圖）</span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;"><br></span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577276302304389.png" alt="image.png"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">16. 替換後的效果如下圖，查看到還有一條數據沒變，使用同樣的方法用&lt;街*&gt;替換&lt;街&gt;即可</span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;"><br></span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577276341460326.png" alt="image.png" width="687" height="303" style="width: 687px; height: 303px;"><br><br><br><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">17. 最終效果如下圖，已去除所有廣告標題</span><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;"><br></span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577276449570568.png" alt="image.png" width="680" height="275" style="width: 680px; height: 275px;"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">18. 複製B、D列至工作表&lt;公式套&gt;&nbsp; &nbsp; （如下圖）</span><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;"><br></span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577276578334075.png" alt="image.png" width="677" height="258" style="width: 677px; height: 258px;"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">19. 複製D列的數據 （如下圖）</span><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;"><br></span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577276639964555.png" alt="image.png" width="682" height="191" style="width: 682px; height: 191px;"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">20. 回到工作表&lt;二合一&gt;，將複製的數據，選擇性粘貼至D列，選擇&lt;值和數字格式&gt;</span><br><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577276733104126.png" alt="image.png"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">21. 之後將B列（物件編號）數據刪除，得到如下效果&nbsp; （如下圖）</span><br><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577276873869741.png" alt="image.png" width="661" height="198" style="width: 661px; height: 198px;"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">22. 將E列物件狀態名稱做變更</span><br></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;"><br></span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ●&nbsp; 回收站&nbsp; 改為&nbsp; 已刪除</span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;●&nbsp; 關閉&nbsp; &nbsp;改為&nbsp; &nbsp;已關閉</span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;●&nbsp; 空格&nbsp; &nbsp;改為&nbsp; &nbsp;已過期</span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">23. 將A列刊登方案中空格欄位改為&lt;套餐&gt;，（例圖賬號沒有此類物件，不做演示）</span></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">24. 最終效果 （如下圖）</span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;"><br></span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577277157742382.png" alt="image.png"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">25. 將A、B、C、D、E列數據進行複製至工作表&lt;套&gt;中，最終得到如下數據</span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;"><br></span></span></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577277268227449.png" alt="image.png"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">26. 再將此表J、K、L、M、N列數據進行複製至&lt;模板 - 會員資料.xlsx&gt; 中的&lt;物件刊登資料&gt;即可</span><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">最終效果頁面如下</span></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191225/1577277786887997.png" alt="image.png"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">27. 最後將物件&lt;模板 - 會員資料.xlsx&gt; 重命名為&lt;ID：33006 王學文(回覆 新北市政府地政局 函<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">)</span>&gt;</span><br><br><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">28. 再新建一個文件夾，重命名為&lt;ID：33006 王學文（回覆 新北市政府地政局 函）&gt;</span><br><br><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">29. 將會員資料及公文文件放入文件夾中，最後壓縮文件夾，發送有度591公文群即可</span></span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">马泽鹏</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2019-12-26 13:44:49</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><img src="/upload/question/20191226/1577339088311859.png" alt="image.png"></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp;<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px; color: #000000;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span></span><img src="/upload/question/20191226/1577352053936593.png" alt="image.png"></p><p><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span><img src="/upload/question/20191226/1577352083838295.png" alt="image.png"></p><p><br></p><p><br></p><p><br></p><p><strong><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<span style="background-color: #FFFF00; font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">● 此類公文介紹</span></span></strong></p><p><br></p><p><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; 公平交易委員會主要負責查新建案的資訊不實問題，如果有收到公平交易委員會的公文，<br>&nbsp; &nbsp; &nbsp; &nbsp; 基本就是和新建案相關的。</span></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;"><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;"><br></span></p><p><strong><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<span style="background-color: #FFFF00; font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">●&nbsp;看公文技巧</span></span></strong></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;"><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; <span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #FF0000;">公文內容可以分成四個板塊進行理解，從每個中抽取核心內容</span><br><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;板塊一、索取來函原因、目的<br><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（ <span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #FF0000;">建案&lt;&nbsp;華登金捷殿 &gt;違反公平交易法，就說明三所列事項提出陳述書及相關證明</span> ）<br><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;板塊二、來函詳細原因<br><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（ <span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #FF0000;">傢配圖中疑將2至5樓陽台空間規劃為室內住宅空間使用</span> ）<br><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;板塊三、來函詳細目的<br><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（ <span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #FF0000;">說明591的經營目的？建案由誰製作？刊登時間？以及提供完整的廣告資料一份</span> ）</span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;"><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;板塊四、相關法律條款<br><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（ <span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #FF0000;">通常情況下，可以忽略不看</span> ）</span></p><p><br></p><p><br></p><p><strong><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<span style="background-color: #FFFF00; font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">● 處理流程</span></span></strong></p><p><span style="background-color: #FFFF00; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;"><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; <span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000; background-color: #FDEADA;">1. 首先查詢公文提及的建案是由誰進行刊登的</span><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000; background-color: #FDEADA;">分別會有兩種情況，一種是由我們的編輯刊登，一種是由會員自己刊登。</span></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000; background-color: #FDEADA;">ps：如果是編輯刊登的，會員賬號的公司名稱為：數字科技</span></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;"><br><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; <span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000; background-color: #FDEADA;">2. 如果是編輯刊登，使用這個模板（ 模板 - 公平建案回覆（編輯刊登）.docx ）</span><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000; background-color: #FDEADA;">如果是會員刊登，使用這個模板（&nbsp;模板 - 公平建案回覆（會員刊登）.docx&nbsp; ）</span><br><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;"><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000; background-color: #FDEADA;">3. 編輯刊登的模板：</span></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;"><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<img src="/upload/question/20191226/1577357641723635.png" alt="image.png"></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <img src="/upload/question/20191226/1577357666191258.png" alt="image.png"></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;"><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000; background-color: #FDEADA;">帶“*”號的屬於要填寫的內容</span></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（1）填寫處理公文的當天日期，需要填寫民國時間（例如民國108年1月1日）</span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（2）同樣填寫日期（例如第1080101001號）<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ●&nbsp; 前三位為年份<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ●&nbsp; 中間四位為哪月哪日<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ●&nbsp; 後三位為公文號，默認001</span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（3）建案名稱</span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（4）建案編號</span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（5）建設公司名稱</span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（6）銷售公司名稱</span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（7）該筆建案的刊登賬號名稱（編輯姓名）<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（8）銷售公司名稱</span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（9）建案刊登時間</span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（10）建案刊登狀態（刊登中、已關閉等等..）</span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（11）填寫處理公文的當天日期<br><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #FF0000;">ps：注意文字格式，編輯進去的文字需要和模板的格式一致</span></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;"><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;"><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;"><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000; background-color: #FDEADA;">4. 會員刊登的模板：</span></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;"><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;"><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<img src="/upload/question/20191226/1577359207473173.png" alt="image.png"></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<img src="/upload/question/20191226/1577359238996639.png" alt="image.png"></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;"><br></span></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;"></span></p><p style="white-space: normal;"><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000; background-color: #FDEADA;">帶“*”號的屬於要填寫的內容</span></span></p><p style="white-space: normal;"><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（1）填寫處理公文的當天日期，需要填寫民國時間（例如民國108年1月1日）</span></p><p style="white-space: normal;"><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（2）同樣填寫日期（例如第1080101001號）<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ●&nbsp; 前三位為年份<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ●&nbsp; 中間四位為哪月哪日<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ●&nbsp; 後三位為公文號，默認001</span></p><p style="white-space: normal;"><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（3）建案名稱</span></p><p style="white-space: normal;"><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（4）建案編號</span></p><p style="white-space: normal;"><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（5）賬號註冊的行動電話</span></p><p style="white-space: normal;"><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（6）賬號註冊的會員姓名</span></p><p style="white-space: normal;"><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（7）建案刊登時間</span></p><p style="white-space: normal;"><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（8）建案刊登狀態（刊登中、已關閉等等..）</span></p><p style="white-space: normal;"><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（9）賬號註冊的會員姓名</span></p><p style="white-space: normal;"><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（10）填寫處理公文的當天日期</span></p><p style="white-space: normal;"><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;"><br></span></p><p style="white-space: normal;"><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #FF0000;">ps：注意文字格式，編輯進去的文字需要和模板的格式一致</span></span></p><p><br></p><p><br></p><p><span style="background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000; background-color: #FDEADA;">5. 模板帶“*”號的內容填寫完後另存文件名為：ID：xxx xxx(回覆 公平交易委員會 函)</span><br></span></p><p><br></p><p><br></p><p><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000; background-color: #FDEADA;">6. 再留意公文中的需求，是否有需要附件（建案的刊登畫面截圖等等..）需求</span><br></span></p><p><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000; background-color: #FDEADA;">如果有此類需求，需要截圖提供圖片文件</span><br><br><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000; background-color: #FDEADA;">7. 之後將回覆的內容與公文放在一個文件夾，如下圖</span></span></p><p><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;"><br></span></p><p><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span><img src="/upload/question/20191226/1577358696490544.png" alt="image.png"></p><p><br></p><p><br></p><p><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000; background-color: #FDEADA;">8. 最後將文件夾進行壓縮至rar格式</span><br><br></span></p><p><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><img src="/upload/question/20191226/1577358760169930.png" alt="image.png"></p><p><br></p><p><br></p><p><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000; background-color: #FDEADA;">9. 將壓縮文件發送至有度591公文群即可</span></span></p><p><br></p><p><br></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">马泽鹏</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2019-12-26 13:45:04</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><img src="/upload/question/20191226/1577360249436983.png" alt="image.png"></p><p>&nbsp;<img src="/upload/question/20191226/1577360674720887.png" alt="image.png"></p><p><img src="/upload/question/20191226/1577360704404699.png" alt="image.png"></p><p><br></p><p><br></p><p><br></p><p><strong style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<span style="background-color: #FFFF00; font-size: 18px;">● 此類公文介紹</span></span></strong></p><p><strong style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFF00; font-size: 18px;"><br></span></strong></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 1. 消費點數爭議指的是會員在591網站發生相關消費使用不滿意的情況<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 來電客服中心要求賠償，但會員的賠償要求不合理我們無法同意<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 最終導致會員二次申述至相關政府單位，再由政府單位來函要求我們做妥善處理。<br><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2. 通常我們的態度是，會員二次申述至相關政府單位了<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 我們會再次放寬條件給予會員一定的補償，但如果是太高的要求，我們一樣是無法同意。</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;"><strong style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<span style="background-color: #FFFF00; font-size: 18px;">● 處理流程</span></span></strong></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;"><strong style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFF00; font-size: 18px;"></span></strong></span></p><p><br></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 1. 查看公文內容（<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;">如上圖</span>）通常第二頁會有消費爭議申述資料單，<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 先了解會員的需求是什麼</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2. 再將該會員二次申述至政府單位的事情同步給督导，</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3. 由督导與會員聯絡確認他的二次申述需求</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 4. 如果有讓步方案，電話中告知我們最終可以給到他的是什麼</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ●&nbsp; 會員同意談妥，接收我們的補償方案，則消費爭議結束</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ●&nbsp; 會員不同意談妥，無法接收我們的補償方案，則記錄會員的需求</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5. 回覆公函，如實說明事件起因、後續的解決方案、最終是否有談妥等等..</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;"><strong style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; white-space: normal;">&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<span style="background-color: #FFFF00;">● 公文回覆說明</span></strong></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;"><strong style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; white-space: normal;"><span style="background-color: #FFFF00;"><br></span></strong></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA; font-size: 16px;">1.&nbsp;打开&lt;母版 - 文字说明.docx&gt;文档，如下图</span></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<img src="/upload/question/20191230/1577694081544604.png" alt="image.png"></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;"><br></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;"></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="">帶“*”號的屬於要填寫的內容<br><br></span></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; background-color: #FFFFFF; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（1）填寫來函的政府單位（例如台北市政府地政局）<br><br></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="background-color: #FFFFFF; color: #000000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（2）填寫回覆公文當天的民國日期（例如108年10月1日 = 2019年10月1日）<br><br></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（3）同樣填寫日期（例如第1080101001號）<br><br></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（4）主旨針對政府來函的主旨進行填寫，沒有固定的模板。<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<img src="/upload/question/20191230/1577693288711806.png" alt="image.png" width="501" height="118" style="width: 501px; height: 118px;"></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: #000000; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;●&nbsp; 例如上圖，主旨可以這樣填寫：「回覆關於貴局來函反饋蘇泳銨女士之消費爭議案之回覆」<br><br></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="color: #000000; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（5）內容填寫介紹</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="color: #000000; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;●&nbsp; 通常第一段以「茲查」為開頭語</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="color: #000000; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;●&nbsp;&nbsp;</span>之後如實說明事件起因、後續解決方案、最終會員是否同意的進行說明即可</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="color: #000000; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;●&nbsp; 公文常用的公文術語含義：<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;之 = 的 （消費爭議案之回覆 = 消費爭議案的回覆）<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;惟 = 但 （表示轉折語氣，凡是用但的地方都要用惟代替）</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="color: #000000; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;於 = 在 （物件於xxxx日進行關閉 = 物件在xxxx日進行關閉）</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="color: #000000; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;●&nbsp; 通常最後一段以「以上稱述皆有據可依，敬請查明！」<br><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;●&nbsp; 如果有附件證明文件，另起一段說明「隨函附檢xxxx文檔供參。」</span></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="color: #000000; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;"><br></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="color: #000000; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（6）<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">填寫來函的政府單位（例如台北市政府地政局）</span></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="color: #000000; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;"><br></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="color: #000000; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;（7）填寫大寫的回覆公文日期</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="color: #000000; font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;"><br></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="font-size: 16px; color: #FF0000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;ps：注意文字格式，編輯進去的文字需要和模板的格式一致</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="font-size: 16px; color: #FF0000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;"><br></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="font-size: 16px; color: #FF0000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;"></span></p><p style="white-space: normal;"><span style="background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;">2. 模板帶“*”號的內容填寫完後另存文件名為：ID：xxx xxx(回覆 公平交易委員會 函)</span><br></span></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA; font-size: 16px;">3. 再留意公文中的需求，是否有需要附件（建案的刊登畫面截圖等等..）需求</span><br></span></p><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA; font-size: 16px;">如果有此類需求，需要截圖提供圖片文件</span><br><br><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA; font-size: 16px;">4. 之後將回覆的內容與公文放在一個文件夾，如下圖</span></span></p><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><br></span></p><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191226/1577358696490544.png" alt="image.png"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA; font-size: 16px;">5. 最後將文件夾進行壓縮至rar格式</span><br><br></span></p><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><img src="https://zsk.591.com.tw/upload/question/20191226/1577358760169930.png" alt="image.png"></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><br></p><p style="white-space: normal;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA; font-size: 16px;">6. 將壓縮文件發送至有度591公文群即可</span></span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FDEADA;"><br></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="font-size: 16px; color: #FF0000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;"><br></span><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">马泽鹏</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2019-12-26 13:45:13</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><img src="/upload/question/20191226/1577339112551782.png" alt="image.png"></p><p><img src="/upload/question/20191230/1577696404758878.png" alt="image.png"></p><p>&nbsp; &nbsp; &nbsp;<img src="/upload/question/20191230/1577696417316552.png" alt="image.png"></p><p><img src="/upload/question/20191230/1577696433575627.png" alt="image.png"></p><p><br></p><p><br></p><p><span style="font-size: 16px;"><strong style="box-sizing: border-box; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<strong style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; white-space: normal;"><span style="background-color: #FFFF00;">● 介紹</span></strong></span></strong></span></p><p><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;"><br></span></p><p><span style="color: #333333; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 16px; color: #000000;">通常是地政局對於網站現有的功能提出優化性的建議<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 如上圖查看主旨即知是針對網站使用坪數的定義問題做優化<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 說明四中便有說明“建請貴公司以「租賃面積」代替租賃廣告之「坪數」、「房屋現況坪數」”</span></span></p><p><br></p><p><br></p><p>&nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><span style="font-size: 18px; background-color: #FFFF00;">● 處理流程</span></strong></span></p><p><span style="font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><span style="font-size: 18px; background-color: #FFFF00;"><br></span></strong></span></p><p><span style="color: #000000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 1. 此類公文同步給產品進行確認是否需要做優化調整，如果需要做調整，確認優化上線的時間。<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 如果不需要做調整，確認不需要調整的原因。</span></p><p><span style="color: #000000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 16px;"><br></span></p><p><span style="color: #000000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2. 回覆公文，說明是否對此進行優化調整，確認優化上線的時間或不考慮做優化的原因。</span></p><p><span style="color: #000000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 16px;"><br></span></p><p><span style="color: #000000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 16px;"><br></span></p><p><span style="color: #000000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp;<span style="font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><span style="background-color: #FFFF00;">● 公文回覆說明</span></strong></span></span></p><p><span style="color: #000000; background-color: #FFFFFF; font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong><span style="background-color: #FFFF00;"><br></span></strong></span></p><p><span style="color: #000000; background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 1. 使用&lt;母版 - 文字說明.docx&gt;進行回覆<br><br></span></p><p><span style="color: #000000; background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2. 回覆內容的注意要點可以參考公平交易類的公文回覆說明</span></span></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">马泽鹏</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2019-12-26 13:45:23</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><img src="/upload/question/20191226/1577339121611150.png" alt="image.png"></p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<img src="/upload/question/20191230/1577699771339813.png" alt="image.png">&nbsp;</p><p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<img src="/upload/question/20191230/1577699788713825.png" alt="image.png"></p><p><br></p><p><br></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-size: 16px;"><strong style="box-sizing: border-box; font-size: 13px;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<strong style="box-sizing: border-box; font-size: 18px;"><span style="box-sizing: border-box; background-color: #FFFF00;">● 介紹</span></strong></span></strong></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;"><br style="box-sizing: border-box;"></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 18px; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="box-sizing: border-box; font-size: 16px; color: #000000;">通常是違反政策、詐騙、損害他人權益的物件，政府會來函要求我們立刻做下架處理或甚至賬號停權</span></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><br style="box-sizing: border-box;"></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><br style="box-sizing: border-box;"></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;">&nbsp; &nbsp; &nbsp; &nbsp;<span style="box-sizing: border-box; font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong style="box-sizing: border-box;"><span style="box-sizing: border-box; background-color: #FFFF00;">● 處理流程</span></strong></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong style="box-sizing: border-box;"><span style="box-sizing: border-box; background-color: #FFFF00;"><br style="box-sizing: border-box;"></span></strong></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; color: #000000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 1. 首先確認公文提及的物件是否與公文提及的下架理由一致。</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; color: #000000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 16px;"><br style="box-sizing: border-box;"></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; color: #000000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2. 確認沒問題再操作處理，然後再發手機簡訊通知會員以及備註來電記錄說明即可。</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; color: #000000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 16px;"><br style="box-sizing: border-box;"></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; color: #000000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 16px;"><br style="box-sizing: border-box;"></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; color: #000000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp;<span style="box-sizing: border-box; font-size: 18px;"><strong style="box-sizing: border-box;"><span style="box-sizing: border-box; background-color: #FFFF00;">● 公文回覆說明</span></strong></span></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; color: #000000; background-color: #FFFFFF; font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;;"><strong style="box-sizing: border-box;"><span style="box-sizing: border-box; background-color: #FFFF00;"><br style="box-sizing: border-box;"></span></strong></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; color: #000000; background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 1. 使用&lt;母版 - 文字說明.docx&gt;進行回覆<br style="box-sizing: border-box;"><br style="box-sizing: border-box;"></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; color: #000000; background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><span style="box-sizing: border-box;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2. 回覆說明我們在什麼時候做了什麼事情</span></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; color: #000000; background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><span style="box-sizing: border-box;"><br></span></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, sans-serif; font-size: 13px; white-space: normal;"><span style="box-sizing: border-box; color: #000000; background-color: #FFFFFF; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><span style="box-sizing: border-box;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; background-color: #FFFFFF;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3. 其他</span>回覆注意要點可以參考公平交易類的公文回覆說明</span></span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li></ul> <h3 class="more-answer">我要補充</h3> <div style=""><div id="editor_14760048529799218" class="edui-default" style=""><div id="edui1" class="edui-editor  edui-default" style="width: 100%; z-index: 999;"><div id="edui1_toolbarbox" class="edui-editor-toolbarbox edui-default"><div id="edui1_toolbarboxouter" class="edui-editor-toolbarboxouter edui-default"><div class="edui-editor-toolbarboxinner edui-default"><div id="edui2" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui2&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui3" class="edui-box edui-button edui-for-fullscreen edui-default"><div id="edui3_state" onmousedown="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui3_body" unselectable="on" title="全屏" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui3&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui3&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui4" class="edui-box edui-button edui-for-source edui-default"><div id="edui4_state" onmousedown="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui4_body" unselectable="on" title="源代码" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui4&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui4&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui5" class="edui-box edui-button edui-for-undo edui-default"><div id="edui5_state" onmousedown="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui5_body" unselectable="on" title="撤销" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui5&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui5&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui6" class="edui-box edui-button edui-for-redo edui-default"><div id="edui6_state" onmousedown="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui6_body" unselectable="on" title="重做" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui6&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui6&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui7" class="edui-box edui-button edui-for-simpleupload edui-default"><div id="edui7_state" onmousedown="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui7_body" unselectable="on" title="单图上传" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui7&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui7&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui8" class="edui-box edui-button edui-for-imageleft edui-default"><div id="edui8_state" onmousedown="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui8_body" unselectable="on" title="左浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui8&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui8&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui9" class="edui-box edui-button edui-for-imageright edui-default"><div id="edui9_state" onmousedown="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui9_body" unselectable="on" title="右浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui9&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui9&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui10" class="edui-box edui-button edui-for-imagecenter edui-default"><div id="edui10_state" onmousedown="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui10_body" unselectable="on" title="居中" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui10&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui10&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui11" class="edui-box edui-separator  edui-default"></div><div id="edui12" class="edui-box edui-button edui-for-date edui-default"><div id="edui12_state" onmousedown="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui12_body" unselectable="on" title="日期" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui12&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui12&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui13" class="edui-box edui-button edui-for-time edui-default"><div id="edui13_state" onmousedown="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui13_body" unselectable="on" title="时间" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui13&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui13&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui14" class="edui-box edui-button edui-for-horizontal edui-default"><div id="edui14_state" onmousedown="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui14_body" unselectable="on" title="分隔线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui14&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui14&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui15" class="edui-box edui-separator  edui-default"></div><div id="edui16" class="edui-box edui-splitbutton edui-for-inserttable edui-default"><div title="插入表格" id="edui16_state" onmousedown="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui16_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onArrowClick();"></div></div></div></div><div id="edui19" class="edui-box edui-button edui-for-deletetable edui-default"><div id="edui19_state" onmousedown="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui19_body" unselectable="on" title="删除表格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui19&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui19&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui20" class="edui-box edui-button edui-for-insertrow edui-default"><div id="edui20_state" onmousedown="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui20_body" unselectable="on" title="前插入行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui20&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui20&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui21" class="edui-box edui-button edui-for-insertcol edui-default"><div id="edui21_state" onmousedown="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui21_body" unselectable="on" title="前插入列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui21&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui21&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui22" class="edui-box edui-button edui-for-mergeright edui-default"><div id="edui22_state" onmousedown="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui22_body" unselectable="on" title="右合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui22&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui22&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui23" class="edui-box edui-button edui-for-mergedown edui-default"><div id="edui23_state" onmousedown="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui23_body" unselectable="on" title="下合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui23&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui23&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui24" class="edui-box edui-button edui-for-deleterow edui-default"><div id="edui24_state" onmousedown="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui24_body" unselectable="on" title="删除行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui24&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui24&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui25" class="edui-box edui-button edui-for-deletecol edui-default"><div id="edui25_state" onmousedown="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui25_body" unselectable="on" title="删除列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui25&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui25&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui26" class="edui-box edui-button edui-for-splittorows edui-default"><div id="edui26_state" onmousedown="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui26_body" unselectable="on" title="拆分成行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui26&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui26&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui27" class="edui-box edui-button edui-for-splittocols edui-default"><div id="edui27_state" onmousedown="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui27_body" unselectable="on" title="拆分成列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui27&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui27&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui28" class="edui-box edui-button edui-for-splittocells edui-default"><div id="edui28_state" onmousedown="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui28_body" unselectable="on" title="完全拆分单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui28&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui28&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui29" class="edui-box edui-button edui-for-mergecells edui-default"><div id="edui29_state" onmousedown="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui29_body" unselectable="on" title="合并多个单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui29&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui29&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui36" class="edui-box edui-button edui-for-edittable edui-default"><div id="edui36_state" onmousedown="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui36_body" unselectable="on" title="表格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui36&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui36&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui41" class="edui-box edui-button edui-for-edittd edui-default"><div id="edui41_state" onmousedown="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui41_body" unselectable="on" title="单元格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui41&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui41&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui42" class="edui-box edui-separator  edui-default"></div><div id="edui43" class="edui-box edui-menubutton edui-for-lineheight edui-default"><div title="行间距" id="edui43_state" onmousedown="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui43_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onArrowClick();"></div></div></div></div><div id="edui52" class="edui-box edui-menubutton edui-for-rowspacingtop edui-default"><div title="段前距" id="edui52_state" onmousedown="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui52_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onArrowClick();"></div></div></div></div><div id="edui59" class="edui-box edui-menubutton edui-for-rowspacingbottom edui-default"><div title="段后距" id="edui59_state" onmousedown="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui59_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onArrowClick();"></div></div></div></div></div><div id="edui66" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui66&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui67" class="edui-box edui-combox edui-for-fontfamily edui-default"><div title="字体" id="edui67_state" onmousedown="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui67_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onButtonClick(event, this);">字体</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onArrowClick();"></div></div></div></div><div id="edui80" class="edui-box edui-combox edui-for-customstyle edui-default"><div title="自定义标题" id="edui80_state" onmousedown="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui80_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onButtonClick(event, this);">自定义标题</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onArrowClick();"></div></div></div></div><div id="edui86" class="edui-box edui-combox edui-for-paragraph edui-default"><div title="段落格式" id="edui86_state" onmousedown="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui86_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onButtonClick(event, this);">段落格式</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onArrowClick();"></div></div></div></div><div id="edui95" class="edui-box edui-combox edui-for-fontsize edui-default"><div title="字号" id="edui95_state" onmousedown="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui95_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onButtonClick(event, this);">字号</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onArrowClick();"></div></div></div></div><div id="edui110" class="edui-box edui-button edui-for-link edui-default"><div id="edui110_state" onmousedown="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui110_body" unselectable="on" title="超链接" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui110&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui110&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui111" class="edui-box edui-button edui-for-indent edui-default"><div id="edui111_state" onmousedown="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui111_body" unselectable="on" title="首行缩进" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui111&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui111&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui112" class="edui-box edui-button edui-for-bold edui-default"><div id="edui112_state" onmousedown="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui112_body" unselectable="on" title="加粗" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui112&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui112&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui113" class="edui-box edui-splitbutton edui-for-forecolor edui-default edui-colorbutton"><div title="字体颜色" id="edui113_state" onmousedown="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui113_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui113_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onArrowClick();"></div></div></div></div><div id="edui116" class="edui-box edui-splitbutton edui-for-backcolor edui-default edui-colorbutton"><div title="背景色" id="edui116_state" onmousedown="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui116_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui116_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onArrowClick();"></div></div></div></div><div id="edui119" class="edui-box edui-button edui-for-italic edui-default"><div id="edui119_state" onmousedown="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui119_body" unselectable="on" title="斜体" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui119&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui119&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui120" class="edui-box edui-button edui-for-underline edui-default"><div id="edui120_state" onmousedown="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui120_body" unselectable="on" title="下划线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui120&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui120&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui121" class="edui-box edui-button edui-for-strikethrough edui-default"><div id="edui121_state" onmousedown="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui121_body" unselectable="on" title="删除线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui121&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui121&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui122" class="edui-box edui-button edui-for-superscript edui-default"><div id="edui122_state" onmousedown="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui122_body" unselectable="on" title="上标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui122&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui122&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui123" class="edui-box edui-button edui-for-subscript edui-default"><div id="edui123_state" onmousedown="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui123_body" unselectable="on" title="下标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui123&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui123&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui124" class="edui-box edui-button edui-for-removeformat edui-default"><div id="edui124_state" onmousedown="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui124_body" unselectable="on" title="清除格式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui124&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui124&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui125" class="edui-box edui-button edui-for-blockquote edui-default"><div id="edui125_state" onmousedown="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui125_body" unselectable="on" title="引用" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui125&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui125&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui126" class="edui-box edui-button edui-for-pasteplain edui-default"><div id="edui126_state" onmousedown="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui126_body" unselectable="on" title="纯文本粘贴模式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui126&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui126&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui127" class="edui-box edui-menubutton edui-for-insertorderedlist edui-default"><div title="有序列表" id="edui127_state" onmousedown="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui127_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onArrowClick();"></div></div></div></div><div id="edui140" class="edui-box edui-menubutton edui-for-insertunorderedlist edui-default"><div title="无序列表" id="edui140_state" onmousedown="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui140_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onArrowClick();"></div></div></div></div><div id="edui147" class="edui-box edui-button edui-for-cleardoc edui-default"><div id="edui147_state" onmousedown="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui147_body" unselectable="on" title="清空文档" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui147&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui147&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div></div></div></div><div id="edui1_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;"><div id="edui1_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui1&quot;].showWordImageDialog();">点击上传</div><div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui1&quot;].hideToolbarMsg();">x</div><div id="edui1_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div><div style="height:0;overflow:hidden;clear:both;" class="edui-default"></div></div><div id="edui1_message_holder" class="edui-editor-messageholder edui-default"></div></div><div id="edui1_iframeholder" class="edui-editor-iframeholder edui-default" style="width: 100%; height: 300px; z-index: 999; overflow: hidden;"><iframe id="ueditor_0" width="100%" height="100%" frameborder="0" src="javascript:void(function(){document.open();document.write(&quot;<!DOCTYPE html><html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}
body{margin:8px;font-family:sans-serif;font-size:13px;}p{margin:5px 0;}</style><link rel='stylesheet' type='text/css' href='/static/ueditor/themes/iframe.css'/></head><body class='view' ></body><script type='text/javascript'  id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant0'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);</script></html>&quot;);document.close();}())"></iframe></div><div id="edui1_bottombar" class="edui-editor-bottomContainer edui-default"><table class="edui-default"><tbody class="edui-default"><tr class="edui-default"><td id="edui1_elementpath" class="edui-editor-bottombar edui-default"><div class="edui-editor-breadcrumb">元素路径:</div></td><td id="edui1_wordcount" class="edui-editor-wordcount edui-default">字数统计</td><td id="edui1_scale" class="edui-editor-scale edui-default" style="display: none;"><div class="edui-editor-icon edui-default"></div></td></tr></tbody></table></div><div id="edui1_scalelayer" class="edui-default"></div></div></div> <div class="edit-btn"><button type="button" class="button">提交回答</button> <label data-v-0fe26aab="" for="commonCheckbox" class="checkbox"><input data-v-0fe26aab="" type="checkbox" id="commonCheckbox" name="commonCheckbox"> <i data-v-0fe26aab=""></i> 需要彈窗，請勾選</label></div></div></div> <div class="index-right"><div class="right-bk"><h3 class="com-title"><a href="javascript:;">我的最爱</a></h3> <div class="item-box clearfix"><span>暫無數據</span></div></div> <div class="hotTag right-bk"><h3 class="com-title">热门标签</h3> <div class="hotTag-box item-box clearfix"><p>無數據</p></div></div> <div class="right-bk rank"><h3 class="com-title"><a href="javascript:;">冠軍排行榜</a></h3> <ul><li><div><span class="number">1</span> <img src="/static/timg.jpeg"> <span>王菊芳</span></div> <span class="score">612分</span></li><li><div><span class="number">2</span> <img src="/upload/head/10177.gif?time=1556271256"> <span>黃雪靜</span></div> <span class="score">258分</span></li><li><div><span class="number">3</span> <img src="/static/timg.jpeg"> <span>田霞</span></div> <span class="score">240分</span></li><li><div><span class="number">4</span> <img src="/static/timg.jpeg"> <span>曹超雲</span></div> <span class="score">168分</span></li><li><div><span class="number">5</span> <img src="/static/timg.jpeg"> <span>文宇</span></div> <span class="score">96分</span></li><li><div><span class="number">6</span> <img src="/upload/head/10217.png?time=1548059679"> <span>黃興興</span></div> <span class="score">78分</span></li><li><div><span class="number">7</span> <img src="/static/timg.jpeg"> <span>黃佳麗</span></div> <span class="score">62分</span></li><li><div><span class="number">8</span> <img src="/static/timg.jpeg"> <span>马泽鹏</span></div> <span class="score">46分</span></li><li><div><span class="number">9</span> <img src="/static/timg.jpeg"> <span>陳敏南</span></div> <span class="score">40分</span></li><li><div><span class="number">10</span> <img src="/static/timg.jpeg"> <span>Cs-刘婷</span></div> <span class="score">10分</span></li><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></ul></div> <div class="activeUser right-bk"><h3 class="com-title">活跃用户</h3> <div class="activeUser-box"><a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a></div></div> <img src="data:image/png;base64,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" class="to-top"></div></div> <!----> <!----> <!----> <div class="page-cnt footer"><p>T5客服知识库<span>|</span>zsk.591.com.tw</p></div></div></div><script type="text/javascript" src="/js/manifest.f192b.js"></script><script type="text/javascript" src="/js/vendor.17570.js"></script><script type="text/javascript" src="/js/app.58254.js"></script><div id="edui_fixedlayer" class="edui-default" style="position: fixed; left: 0px; top: 0px; width: 0px; height: 0px;"><div id="edui148" class="edui-popup  edui-bubble edui-default" onmousedown="return false;" style="display: none;"> <div id="edui148_body" class="edui-popup-body edui-default"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank" class="edui-default"></iframe> <div class="edui-shadow edui-default"></div> <div id="edui148_content" class="edui-popup-content edui-default">  </div> </div></div></div></body></html>