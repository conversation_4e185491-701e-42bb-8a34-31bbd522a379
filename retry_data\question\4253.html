<html class=" "><head><meta charset="utf-8"><title>591客服知識庫</title><link rel="stylesheet" href="/static/font/font-awesome.min.css"><link href="/css/app.e5aa4.css" rel="stylesheet"><script type="text/javascript" charset="utf-8" async="" src="/js/questionDetail.d0a6b.js"></script><style type="text/css">.que-detail-page .index-left .button{width:74px;height:32px;line-height:32px;font-size:13px;margin-right:10px}.que-detail-page .index-left .button:hover{color:#fff}.que-detail-page .detail-box{padding:20px 30px;border:1px solid #e5e5e5;border-radius:4px}.que-detail-page .detail-box h3{font-size:18px;color:#333;margin-bottom:10px}.que-detail-page .detail-box h3 span{color:#f77d24}.que-detail-page .detail-box .add-fav{cursor:pointer}.que-detail-page .d-b-menu{margin:15px 0 25px;font-size:13px;color:#999}.que-detail-page .d-b-menu b{display:inline-block;font-weight:400;margin:0 10px}.que-detail-page .d-b-menu .r{float:right;color:#f77d24}.que-detail-page .d-b-menu .r .fa{margin-right:5px;font-size:16px}.que-detail-page .d-b-cnt{margin-bottom:25px;font-size:13px;color:#666}.que-detail-page .submit-ask{display:block;margin:32px auto 0}.que-detail-page .reply-list{margin:30px 0 10px;overflow:hidden}.que-detail-page .reply-list .reply-item{padding:15px 30px 25px;margin-bottom:30px;border-radius:4px;border:1px solid #e5e5e5}.que-detail-page .reply-list .reply-item h4{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;line-height:50px;font-size:13px}.que-detail-page .reply-list .reply-item h4 img{width:40px;height:40px;border-radius:50%}.que-detail-page .reply-list .reply-item h4 .time{color:#999}.que-detail-page .reply-list .reply-item h4 .button{-ms-flex-item-align:center;align-self:center}.que-detail-page .reply-list .reply-item h4 .delete{color:#666;cursor:pointer}.que-detail-page .reply-list .reply-item .reply-detail{padding:20px 0 30px}.que-detail-page .more-answer{font-size:18px;color:#666;margin-bottom:5px}.que-detail-page .editor-content table{width:100%;border:1px solid #ddd}.que-detail-page .editor-content table td,.que-detail-page .editor-content table th{border:1px solid #ddd;padding:2px 8px;line-height:2}.que-detail-page .editor-content img{max-width:100%;vertical-align:text-bottom}.que-detail-page .edit-btn{text-align:center;margin-top:20px}.que-detail-page .edit-btn .edit-cancle{background:#aaa}</style><style type="text/css">.head-info{line-height:26px;background:#f5f5f5;font-size:13px;color:#999}.head-info a{margin-left:20px;color:#333}.head-search{margin-top:22px}.head-search img{float:left}.head-search .srh-box{float:left;margin:20px 0 0 20px}.head-search .srh-box .srh-input{float:left;width:260px;height:38px;line-height:38px;border:2px solid #37a508;border-radius:4px 0 0 4px}.head-search .srh-box a,.head-search .srh-box button{float:left;width:80px;height:38px;line-height:36px;font-size:16px;color:#fff}.head-search .srh-box .srh-btn{border-radius:0 4px 4px 0}.head-search .srh-box .ask-btn{margin-left:10px}.head-nav{float:left;margin:20px 0 0 60px;color:#fff;font-size:16px;line-height:36px}.head-nav a{display:block;height:100%;color:#333}.head-nav li{position:relative;float:left;margin-right:35px}.head-nav .active a,.head-nav li:hover a{color:#30b940}.head-nav .active:after,.head-nav li:hover:after{display:block;content:"";height:3px;width:100%;background:#30b940}.head-pic{width:100%;height:90px;margin-top:20px;background:url(/static/banner.png) top no-repeat}</style><style type="text/css">.top-breadcrumb{height:20px;margin-top:10px;margin-bottom:40px;font-size:13px;color:#666}.top-breadcrumb .t-b-icon{color:#30b940}.top-breadcrumb span{margin-left:8px}.top-breadcrumb span .fa{font-size:16px;margin-left:8px}.top-breadcrumb span:first-child{margin-left:0}.top-breadcrumb .now{color:#30b940}</style><script type="text/javascript" src="/static/ueditor/ueditor.config.js?20210319" id="configScriptTag"></script><script type="text/javascript" src="/static/ueditor/ueditor.all.js?20210319" id="editorScriptTag"></script><style type="text/css">.checkbox[data-v-0fe26aab]{word-spacing:4px;vertical-align:bottom;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.checkbox i[data-v-0fe26aab]{display:inline-block;width:14px;height:14px;border-radius:2px;border:1px solid #ccc;background:#fff;position:relative;top:1px}.checkbox input[data-v-0fe26aab]{opacity:0;width:0;height:0;margin:0}.checkbox input:checked+i[data-v-0fe26aab]{background:#30b940;border-color:#30b940}.checkbox input:checked+i[data-v-0fe26aab]:before{content:"";position:absolute;left:2px;top:1px;width:11px;height:6px;border-bottom:2px solid #fff;border-left:2px solid #fff;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}</style><style type="text/css">.popbox{position:fixed;top:50%;left:50%;z-index:1011;background:#fff;border-radius:6px;overflow:hidden}.popbox h3{font-size:16px;line-height:40px;padding:0 20px}.popbox h3 span{float:right;color:#666;font-size:13px;cursor:pointer}.popbox .pb-cnt{padding:0 20px;font-size:13px}.popbox .pb-btn-area{position:absolute;bottom:0;left:0;z-index:1;width:100%;text-align:right;padding:0 20px 20px 0}.popbox .pb-btn-area button{height:32px;line-height:32px;border:0;background:#fff;cursor:pointer;border-radius:6px;margin-left:15px;padding:0 15px}.popbox .pb-btn-area button.pb-btn-ok{background:#daf3cf;color:#279005}.popbox .pb-btn-area button.pb-btn-ok:hover{background:#279005;color:#fff}</style><style type="text/css">.shadow{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1010;background:#000}</style><style type="text/css">.footer{margin-top:70px;margin-bottom:20px;padding-top:20px;text-align:center;color:#999;border-top:1px solid #f5f5f5}.footer span{margin:0 10px}</style><script src="/static/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript" defer="defer"></script><link href="/static/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet"><script src="/static/ueditor/third-party/codemirror/codemirror.js" type="text/javascript" defer="defer"></script><link rel="stylesheet" type="text/css" href="/static/ueditor/third-party/codemirror/codemirror.css"><script src="/static/ueditor/third-party/zeroclipboard/ZeroClipboard.js" type="text/javascript" defer="defer"></script><style type="text/css">.activeUser,.favorite,.hotTag,.rank{margin-bottom:40px}</style><style type="text/css">.rank .rank-page{height:60px;line-height:60px;font-size:18px;margin-bottom:20px;text-align:center;background:#fafafa;border-radius:5px}.rank ul{padding:0 20px}.rank li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;border-bottom:1px dotted #f5f5f5;line-height:50px}.rank li .flag{position:relative;display:inline-block;width:20px;height:30px;background:#476472;vertical-align:middle;margin-right:12px;color:#fff;text-align:center;line-height:20px;font-size:13px;font-style:normal}.rank li .flag:after{content:"";position:absolute;bottom:-4px;left:-4px;border:14px solid transparent;border-bottom-color:#fff}.rank li:first-child .flag{background:#fb6757}.rank li:nth-child(2) .flag{background:#fac525}.rank li:nth-child(3) .flag{background:#00a9fb}.rank li img{width:36px;height:36px;border-radius:50%;margin-right:12px}</style><style type="text/css">.activeUser .activeUser-box a{display:inline-block;width:64px;height:64px;border-radius:50%;overflow:hidden;margin:0 6px 12px}</style><style type="text/css">.to-top{display:none;position:fixed;bottom:150px;z-index:5;width:50px;height:50px;cursor:pointer}</style></head><body style=""><div id="app" style=""><div class="que-detail-page" style=""><div class="wiki-hd"><div class="head-info"><p class="page-cnt">
      您好，欢迎来到591客服知识库
      <a href="https://www.591.com.tw/admin.php?new_url=https://zsk.591.com.tw">[请登入]</a> <!----></p></div> <div class="page-cnt head-search clearfix"><a href="/" class="router-link-active"><img src="/static/logo.png"></a> <ul class="head-nav clearfix"><li class="router-link-active active"><a href="/">591房屋交易网</a></li> <li class=""><a href="/life">客服充电站</a></li> <li class=""><a href="/notification">通知事项</a></li></ul> <div class="srh-box"><input type="text" placeholder="请输入搜索内容" class="srh-input"> <button type="button" class="button srh-btn">搜索</button> <a href="/ask/591" class="button button-orange ask-btn">提問</a></div></div> <div class="head-pic"></div></div> <div class="top-breadcrumb com-width"><span><a href="/collection/591/1209" class="">客服督導相關<i class="fa fa-angle-double-right"></i></a></span><span><a href="/collection/591/1209/1658" class="">業務作業指導<i class="fa fa-angle-double-right"></i></a></span><span><strong class="now">问题详情</strong></span></div> <div class="com-width clearfix" style=""><div class="index-left" style=""><div class="detail-box"><h3><!---->新刊登作業指導書</h3> <p class="clearfix d-b-menu"><span class="l">
            王菊芳&nbsp;&nbsp;&nbsp;浏览338次&nbsp;&nbsp;&nbsp;2020-10-10 09:57:27
          </span> <span class="r"><span class="add-fav"><i class="fa fa-heart"></i>加入最爱</span></span></p> <div class="d-b-cnt editor-content big-img-parent"><div><p><span style="font-family: 微软雅黑, Microsoft YaHei;">如題</span><br></p></div></div> <div class="d-b-button"><a href="/edit/591/4253" class="button">编辑</a> <button class="button button-orange">关闭提问</button></div></div> <div class="hr-line"></div> <ul class="reply-list big-img-parent"><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-19 10:48:52</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-size: 16px; line-height: 150%;">1．<span style="font-style: normal; font-variant: normal; font-weight: normal; font-stretch: normal; font-size: 9px; line-height: normal; font-size-adjust: none; font-kerning: auto; font-language-override: normal; font-feature-settings: normal;">&nbsp; </span></span></strong><strong><span style="font-size: 16px; line-height: 150%;">作業目的</span></strong></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;text-indent:28px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, Microsoft YaHei;">通過新刊登審核，保證網站資訊相對真實有效，給房客提供良好的找屋環境，使591值得信賴；並可防止一筆多刊的問題來維護網站收益。</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;text-indent:28px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, Microsoft YaHei;"><br></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px; line-height: 150%;">2．<span style="font: normal normal normal normal 9px 微软雅黑, Microsoft YaHei;">&nbsp; </span></span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px; line-height: 150%;">適用對象及範圍</span></strong></span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom: 8px;margin-left:28px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, Microsoft YaHei;">房屋交易事業部→客服部→一星含以上客服</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px; line-height: 150%;"><br></span></strong></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px; line-height: 150%;">3．<span style="font: normal normal normal normal 9px 微软雅黑, Microsoft YaHei;">&nbsp; </span></span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px; line-height: 150%;">新刊登審核</span></strong></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">3.1 </span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">審核規範</span></strong></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">3.1.1</span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">房屋用途名詞解釋：</span></strong></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"></span></strong></span></p><table cellspacing="0" cellpadding="0"><tbody><tr style=";height:32px" class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px;" width="116" height="32"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center;line-height:115%"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 115%;">房屋用途</span></strong></span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="517" height="32"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center;line-height:115%"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 115%;">名詞解釋</span></strong></span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="116" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center;line-height:115%"><span style="line-height: 115%; font-family: 微软雅黑, Microsoft YaHei;">整層住家</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="517" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:115%"><span style="line-height: 115%; font-family: 微软雅黑, Microsoft YaHei;">一個房間以上，以居家生活為主的建築，如別墅、透天厝、公寓</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="116" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center;line-height:115%"><span style="line-height: 115%; font-family: 微软雅黑, Microsoft YaHei;">獨立套房</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="517" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:115%"><span style="line-height: 115%; font-family: 微软雅黑, Microsoft YaHei;">房間內有個人衛浴設備並且只是一個人使用全部設施，不與他人共用物品</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="116" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center;line-height:115%"><span style="line-height: 115%; font-family: 微软雅黑, Microsoft YaHei;">分租套房</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="517" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:115%"><span style="line-height: 115%; font-family: 微软雅黑, Microsoft YaHei;">房間內有個人衛浴設備且和他人共用如客廳、陽台以及其他設備</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="116" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center;line-height:115%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 115%;">雅&nbsp;&nbsp;&nbsp; 房</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="517" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:115%"><span style="line-height: 115%; font-family: 微软雅黑, Microsoft YaHei;">房間內無個人衛浴設備，而須與他人共用房間外之衛浴設備，稱為雅房</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="116" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center;line-height:115%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 115%;">辦&nbsp;&nbsp;&nbsp; 公</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="517" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:115%"><span style="line-height: 115%; font-family: 微软雅黑, Microsoft YaHei;">以辦公為主要用途的建築</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="116" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center;line-height:115%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 115%;">店&nbsp;&nbsp;&nbsp; 面</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="517" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:115%"><span style="line-height: 115%; font-family: 微软雅黑, Microsoft YaHei;">位於一樓的商業用途建築</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="116" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center;line-height:115%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 115%;">廠&nbsp;&nbsp;&nbsp; 房</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="517" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:115%"><span style="line-height: 115%; font-family: 微软雅黑, Microsoft YaHei;">以工業用途為主的建築</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="116" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center;line-height:115%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 115%;">土&nbsp;&nbsp;&nbsp; 地</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="517" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:115%"><span style="line-height: 115%; font-family: 微软雅黑, Microsoft YaHei;">以土地為主，無地上物或地上物為輔</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="116" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center;line-height:115%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 115%;">車&nbsp;&nbsp;&nbsp; 位</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="517" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:115%"><span style="line-height: 115%; font-family: 微软雅黑, Microsoft YaHei;">以供停汽車使用的土地或建築</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="116" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center;line-height:115%"><span style="line-height: 115%; font-family: 微软雅黑, Microsoft YaHei;">詳請請見</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="517" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:115%"><span style="line-height: 115%; font-family: 微软雅黑, Microsoft YaHei;">https://zsk.591.com.tw/question/3081</span></p></td></tr></tbody></table><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"><br></span></strong></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="line-height: 150%;">3.1.2 </span></strong><strong><span style="line-height: 150%;">各用途的坪數及金額取值範圍：</span></strong></span></p><table cellspacing="0" cellpadding="0"><tbody><tr style=";height:31px" class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px;" width="116" height="31"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong>房屋用途</strong></span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="236" height="31"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong>正常坪數範圍</strong></span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="281" height="31"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong>正常金額範圍</strong></span></p></td></tr><tr style=";height:14px"><td rowspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="116" height="14"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei;">整層住家</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="236" valign="top" height="14"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="background: yellow none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;">租：10坪以上（含10坪）</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="281" valign="top" height="14"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="background: yellow none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;">3千元 ～ 3百萬（或以上）</span></p></td></tr><tr style=";height:13px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="236" valign="top" height="13"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei;">售：10坪 ～ 5千坪（或以上）</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="281" valign="top" height="13"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei;">60萬元 ～ 5百億（或以上）</span></p></td></tr><tr style=";height:23px"><td rowspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="116" height="23"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei;">獨立套房</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="236" valign="top" height="23"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="background: yellow none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;">租：3坪 ～ 35坪</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="281" valign="top" height="23"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="background: yellow none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;">2千元 ～ 8萬元</span></p></td></tr><tr style=";height:24px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="236" valign="top" height="24"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei;">售：4坪 ～ 900坪（或以上）</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="281" valign="top" height="24"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei;">30萬元 ～ 9千萬元（或以上）</span></p></td></tr><tr style=";height:12px"><td rowspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="116" height="12"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei;">分租套房</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="236" valign="top" height="12"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="background: yellow none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;">租：2坪 ～ 20坪 </span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="281" valign="top" height="12"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="background: yellow none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;">2千元 ～ 2萬5</span></p></td></tr><tr style=";height:9px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="236" valign="top" height="9"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei;">售：無</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="281" valign="top" height="9"><br></td></tr><tr style=";height:12px"><td rowspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="116" height="12"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei;">雅&nbsp;&nbsp;&nbsp; 房</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="236" valign="top" height="12"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="background: yellow none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;">租：2坪 ～12坪</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="281" valign="top" height="12"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="background: yellow none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;">1千5 ～ 2萬</span></p></td></tr><tr style=";height:9px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="236" valign="top" height="9"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei;">售：無</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="281" valign="top" height="9"><br></td></tr><tr style=";height:11px"><td rowspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="116" height="11"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei;">辦&nbsp;&nbsp;&nbsp; 公</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="236" valign="top" height="11"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="background: yellow none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;">租：1坪 ～ 2千坪（或以上）</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="281" valign="top" height="11"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="background: yellow none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;">1千 ～ 3百萬（或以上）</span></p></td></tr><tr style=";height:9px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="236" valign="top" height="9"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei;">售：9坪 &nbsp; ～ 1萬2千坪（或以上）</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="281" valign="top" height="9"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei;">1百萬 ～ 4百億（或以上）</span></p></td></tr><tr style=";height:13px"><td rowspan="3" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="116" height="13"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei;">店&nbsp;&nbsp;&nbsp; 面</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="236" valign="top" height="13"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="background: yellow none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;">租：1坪 ～ 2千坪（或以上）</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="281" valign="top" height="13"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="background: yellow none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;">3千～&nbsp; 150萬（或以上）</span></p></td></tr><tr style=";height:7px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="236" valign="top" height="7"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei;">售：3.5坪 ～ 5千坪（或以上）</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="281" valign="top" height="7"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei;">1百萬 ～ 168億（或以上）</span></p></td></tr><tr style=";height:13px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="236" valign="top" height="13"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei;">頂：1坪 &nbsp; ～ 1萬8千坪（或以上）</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="281" valign="top" height="13"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei;">1萬 ～ 8千萬（或以上）</span></p></td></tr><tr style=";height:11px"><td rowspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="116" height="11"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei;">廠&nbsp;&nbsp;&nbsp; 房</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="236" valign="top" height="11"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="background: yellow none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;">租：1坪 ～ 3千坪（或以上）</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="281" valign="top" height="11"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="background: yellow none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;">1千元 ～ 150萬（或以上）</span></p></td></tr><tr style=";height:9px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="236" valign="top" height="9"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei;">售：15坪 ～ 4千坪</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="281" valign="top" height="9"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei;">300萬 ～ 30億（或以上）</span></p></td></tr><tr style=";height:10px"><td rowspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="116" height="10"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei;">土&nbsp;&nbsp;&nbsp; 地</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="236" valign="top" height="10"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="background: yellow none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;">租：10坪 ～ 1萬坪（或以上）</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="281" valign="top" height="10"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="background: yellow none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;">2千元 ～ 180萬（或以上）</span></p></td></tr><tr style=";height:10px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="236" valign="top" height="10"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei;">售：3坪 &nbsp; ～ 20萬坪（或以上）</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="281" valign="top" height="10"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei;">15萬 ～ 900億（或以上）</span></p></td></tr><tr style=";height:11px"><td rowspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="116" height="11"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei;">車&nbsp;&nbsp;&nbsp; 位</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="236" valign="top" height="11"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="background: yellow none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;">租：1坪 ～ 15坪</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="281" valign="top" height="11"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="background: yellow none repeat scroll 0% 0%; font-family: 微软雅黑, Microsoft YaHei;">1千元 ～ 9千元</span></p></td></tr><tr style=";height:9px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="236" valign="top" height="9"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei;">售：0.4坪 ～ 648坪（或以上）</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="281" valign="top" height="9"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="font-family: 微软雅黑, Microsoft YaHei;">40萬 ～ 8千萬（或以上）</span></p></td></tr></tbody></table><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;line-height:150%"><strong><span style=";line-height:150%;font-family:SimSun">&nbsp; </span></strong><span style="line-height: 150%; color: red; font-family: 微软雅黑, Microsoft YaHei;">注：台灣的1坪 約＝ 大陸的3.3025平方米，所以審核時要先將這個概念轉換一下。</span></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-19 10:50:44</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin-left: 28px;"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-size: 14px;">3.1.3 </span></strong><strong><span style="font-size: 14px;">主要判斷條件：</span></strong><strong><span style="font-size: 14px; color: red;">现况類型；地址；樓層；格局；坪數；金額；標題特色；房屋照片；其他類；</span></strong><strong><span style="font-size: 14px;">審核時請參照以下審核規範</span></strong></span></p><p style="margin-left: 28px;"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-size: 14px;"></span></strong></span></p><table cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px;" width="888" valign="top"><p style="text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong>現況類型</strong></span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="888" valign="top"><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">【可通過】</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1、店面可備註攤位</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2、商用類現況可相互備註</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">3、分組套房與雅房相互備註</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">4、廣告欄位為住家，備註套房</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">5、整層住家，標題或特色備註分租，直接將“分改為合”（不納入違規次數）</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">6、住宅類與商用類相互備註，結合廣告描述、照片等可判斷為住店或住辦通過</span></p><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">【不可通過】</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1、廣告欄位為套房，備註住家格局為2房或以上（注：挑高、樓中樓等除外）</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2、廣告欄位為獨立套房，備註分租套房/雅房</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">3、結合坪數、金額、標題或其他描述與現況用途不符</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">4、住宅類與商用類相互備註，結合廣告內容無法判斷為住店或住辦</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">5、刊登房屋廣告，備註買地送屋/買地送厝（確認無實際房屋出租，則需刊登在土地）</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">注：以上不通過類型，均放待處理撥出確認</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="888" valign="top"><p style="text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong>地址</strong></span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="888" valign="top"><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">【可通過】</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1、特色或標題備註兩個位址一起出租/售，其中一位址與欄位相同</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2、廣告位址與特色或標題備註的街道不一致或多個街道，結合地圖判斷是備註附近多個街道介紹</span></p><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">【不可通過】</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1、明顯虛假錯誤位址</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2、結合廣告內容判斷位址上下不一致</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">注：以上不通過類型，均放待處理撥出確認</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="888" valign="top"><p style="text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong>樓層</strong></span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="888" valign="top"><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">【可通過】</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1、廣告樓層透天與別墅可相互備註</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2、欄位出租/售樓層為+1(頂樓加蓋)，總樓高為5F，標題或者特色備註6F/6F</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">3、形態為透天或別墅，結合廣告內容與照片可判斷為整棟出租/售，直接修改通過</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">4、結廣告照片或描述可判斷有增建，例如：總樓層高寫2F，查看照片樓高是3F但明顯是增建</span></p><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">【不可通過】</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1、結合廣告內容判斷樓層上下不一致</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2、樓層與形態不符，例如：“形態為電梯大樓，總樓層為2樓”等</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">3、形態為透天或別墅，結合廣告內容與照片無法判斷為整棟出租/售</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">4、總樓層填寫6F（實際是B1-5F）需確認，因建築總高不包含地下層的層高</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">注：以上不通過類型，均放待處理撥出確認</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="888" valign="top"><p style="text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong>格局</strong></span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="888" valign="top"><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">【可通過】</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1、商用類和新房屋廣告，衛浴填寫不完整</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2、欄位勾選開放式，標題或特色描述格局不完整</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">3、格局圖頂樓格局不完整，欄位樓層與格局圖不一致，例如：“格局圖顯示樓高3樓4房(3樓只有一個房間和露臺)，但欄位填寫2樓，格局填寫4房”</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">4、住家廣告欄位未勾選開放式，有上傳格局圖，可直接按格局圖修改（注：若欄位格局與格局圖相差太多，無法判斷則需放待確認）</span></p><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">【不可通過】</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1、結合廣告內容格局上下不一致</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2、頂樓有完整格局，但欄位的格局和樓層與格局圖不一致</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">3、欄位勾選開放式，標題或特色備註完整格局</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">4、刊登住宅廣告，法定用途為一般事務所，格局上下不一致</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">注：以上不通過類型，均放待處理撥出確認</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="888" valign="top"><p style="text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong>坪數</strong></span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="888" valign="top"><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">【可通過】</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1、坪數相差1坪含以內</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2、辦公出租1坪 （有實際座位出租）</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">3、欄位坪數與標題或特色不一致，但廣告內有明確解釋說明</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">4、坪數為1樓或最高樓層的坪數，特色備註1+B1，5+頂加的坪數</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">5、出租辦公、店面、廠房1坪以上，土地10坪以上，車位1-14坪範圍內</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">6、出售辦公9坪以上、店面3.5坪以上、廠房15坪以上、土地3坪以上、車位0.4坪以上</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">7、住家/宅10以上（含10坪）、獨立套房3-35坪、分租套房2-20坪、雅房2-12坪範圍內</span></p><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">【不可通過】</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1、結合廣告內容坪數上下不一致</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2、明顯虛假坪數，例如：“8888或99999”等</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">3、辦公出租1坪，借址營登無實際辦公室出租</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">4、欄位坪數與標題或特色不一致，且沒有其他參考說明</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">5、不符合坪數規範範圍的均需確認</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">注：以上不通過類型，均放待處理撥出確認</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="888" valign="top"><p style="text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong>金額</strong></span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="888" valign="top"><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">【可通過】</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1、金額上下相差1元</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2、整棟出租/售可備註分層租售金額</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">3、金額備註***元起，直接將“起”編輯（不納入違規次數）</span></p><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">【不可通過】</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1、結合廣告內容金額上下不一致</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2、整棟出租/售不可備註分間租售金額</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">3、明顯虛假金額，例如：“88888或123456”等</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">注：以上不通過類型，均放待處理撥出確認</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="888" valign="top"><p style="text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black;">標題</span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black;">/</span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black;">特色</span></strong></span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="888" valign="top"><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">【可通過】</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1、備註政府官方網網址</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2、廣告詳情明確解釋此屋為凶宅可通過</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">3、備註其他物件請參考【我的店鋪】，查看已開591通店鋪</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">4、備註專營不同區域、街道、特區/商圈、形態、類型可通過</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">5、出租住宅台南（北區、東區、南區、安平區）備註日租或民宿</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">6、除台南4個區其他地區備註：建議性（適合做民宿）或形容（民宿風套房）</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">7、出租商用、車位及出售廣告可備註民宿、旅館業、日租、周租、按天計算</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">8、備註更多物件可參考591【我的所有物件】，查看帳號有其他物件刊登可直接幫其備註物件編號</span></p><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">【不可通過】</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">1、標題/特色備註地址有誤（放待，撥出確認）</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">2、標題/特色備註地圖有誤（放待，撥出確認）</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">3、備註多間房屋資訊（編輯多間資訊，發站內簡訊，按違規處理）</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">4、備註其他無關指引性網址（編輯網址，發站內簡訊，按違規處理）</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">5、非台南地區（北區、東區、南區、安平區）備註日租、周租、按天計算或民宿等（放待處理，撥出確認）</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">6、備註專營不同社區名稱、同社區不同期、不同棟、不同樓層（編輯多間資訊，發站內簡訊，按違規處理）</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">7、備註其他物件請參考【我的店鋪】，查看未開啟591通店鋪（編輯多間資訊，發站內簡訊，按違規處理）</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">8、備註更多物件可參考591【我的所有物件】，查看帳號無其他物件刊登（編輯多間資訊，發站內簡訊，按違規處理）</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; padding: 0px 7px; word-break: break-all;" width="888" valign="top"><p style="text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black;">房屋照片（2021.7.1日生效）</span></strong></span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; padding: 0px 7px; word-break: break-all;" width="888" valign="top"><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"></span></p><p style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;white-space: normal"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px"><strong style="box-sizing: border-box"><span style="box-sizing: border-box;color: red">【可通過】</span></strong></span></p><p class="MsoListParagraph" style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;margin-left: 24px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;white-space: normal"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">1. 社區公約條款圖2張內<br style="box-sizing: border-box">2. 政府頒發的房屋相關證書及與政府官網圖3張內<br style="box-sizing: border-box">3. 房屋配套圖，如：停車場、公園、附近交通、超商等<br style="box-sizing: border-box">4. 圖片整體無底色<br style="box-sizing: border-box">5. 圖片PS類：<br style="box-sizing: border-box">• 使用箭頭、邊框標識房子的方位、方向、路線或建物（說明：箭頭實心大小不可超10px，邊框不可超過7px)<br style="box-sizing: border-box">• 文字或其他描述一處，大小限1/5内<br style="box-sizing: border-box">• 文字或其他描述兩處，每處大小限15px<br style="box-sizing: border-box">• PS美邊大小未超過10px<br style="box-sizing: border-box">• 圖片最多可PS兩處（相機自帶日期、示意圖、附近街景、外觀圖等不算PS，大小限1/5内）<br style="box-sizing: border-box">6. 圖片PS透明類：<br style="box-sizing: border-box">•&nbsp; 僅一處透明PS大小不限制（主观灵活判断，若看起来明显的按ps大小审核）<br style="box-sizing: border-box">•&nbsp; 一處透明+文字描述（一處大小限1/5，兩處每處大小限15px内）<br style="box-sizing: border-box">•&nbsp; 透明+邊框（10px內）<br style="box-sizing: border-box">7. 拼接圖片類<br style="box-sizing: border-box">• 拼接圖+PS一處（大小限1/5内）<br style="box-sizing: border-box">• 拼接圖+PS兩處（每處大小限10-15px内）<br style="box-sizing: border-box">8.土地、地圖和格局圖可標識多處方框或箭頭描述等（注：每處箭頭跟邊框大小不可超過10px）</span></p><p style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;white-space: normal"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">&nbsp;</span></p><p style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;white-space: normal"><span style="box-sizing: border-box;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px"><strong style="box-sizing: border-box"><span style="box-sizing: border-box;color: red">【不可通過】</span></strong></span></p><p class="MsoListParagraph" style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;margin-left: 24px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;white-space: normal"><span style="font-size: 14px;">不符合"可通過"審核規範的均按違規處理（包含但不僅限於）<br style="box-sizing: border-box">1. 上傳不雅照片<br style="box-sizing: border-box">2. 上傳QRCode圖片<br style="box-sizing: border-box">4. 多間不同房屋照片<br style="box-sizing: border-box">5. 上傳畫質模糊不清的圖片<br style="box-sizing: border-box">6. 活動贈送禮品圖、活動宣傳圖<br style="box-sizing: border-box">7. 網路上拍賣的家電傢俱圖，非實物圖<br style="box-sizing: border-box">8. 標識帶有非591的鏈接、網站、部落格連結。如：其他網站浮水印、網站名字、指引性網址等<br style="box-sizing: border-box">9. 上傳非當前房屋現狀的圖片、其他房屋的照片、盜用他人圖片、人物、風景、動物照等</span></p><p class="MsoListParagraph" style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;margin-left: 24px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;white-space: normal"><span style="font-size: 14px;"><br></span></p><p class="MsoListParagraph" style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;margin-left: 24px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;white-space: normal"><strong style="box-sizing: border-box"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">補充說明：</span></strong></p><p class="MsoListParagraph" style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;margin-left: 24px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;white-space: normal"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">1.10px大小可參考：六號字體大小或者截圖短邊區域在16-18之間<br style="box-sizing: border-box">2.7px大小可參考：七號字體大小或者截圖短邊區域大小在10-12之間</span></p><p style="box-sizing: border-box;margin-top: 0px;margin-bottom: 0px;padding: 0px;color: rgb(51, 51, 51);font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;white-space: normal"><span style="box-sizing: border-box;color: black;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px"><span style="box-sizing: border-box">註</span>：帳號第一次違規，放待處理撥出處理，第二次以上按違規處理流程處理</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"></span><br></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="888" valign="top"><p style="text-align:center"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black;">其他類</span></strong></span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="888" valign="top"><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: red;">【社會住宅租金】</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">營建署這邊厘清若租金標示上無明確表示為弱勢戶租金,業者會有違反公平交易法之嫌疑,以及造成民眾觀感不佳,故目前營建署這邊已宣導業者租金標示部分要以一般戶租金標示,標題部分可以以最低金額xxx起，當社會住宅廣告欄位未填寫一般戶租金時需放待處理進行確認</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: red;">【權利屋】</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">只有使用權的房子，只要權利範圍是合法即可刊登，即第1.2種</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">1</span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">、早年眷村或是安之老兵公務員的土地</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">2</span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">、早期租地建屋的地上權（分為有無被設定地上權，有租金跟無租金）</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">3</span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">、無權佔用</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">4</span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">、產權糾紛</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">地上權：是指物權，其取得、設定、喪失及變更（如買賣、贈與等）皆應以書面為之，並須向地政機關登記，否則不算真正取得權利，且經過登記後，地址機關會向權利人【他項權利證明書】，故可詢問是否保留該證明書（若遺失可申請補發）或書面契約，另可向地政機關調取建物及土地藤本，若有地上權設定，於土地藤本上之【他項權利】欄必定會有注記，並可觀察其上是否有就地組、存續期間或其他約定的注記，已厘清權利義務關係</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: red;">【備註FB】</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">1</span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">、會員僅備註FB帳號名稱可通過，例如：“FB中信房屋內壢店”“FB我的范範屋”的資訊</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">2</span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">、備註備註請關注、瞭解更多物件、搜尋FB中信房屋內壢店等關鍵指引性詞就不可通過需編輯</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">【房屋無權狀，僅出售地上物及承租權】</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">與法務確認，違建一樣可進行買賣，但會有被拆除的風險，需放待處理與刊登者說明風險需自行承擔，跟進兩天，第一天發手機簡訊通知，第二天若仍然無人接聽，不再發手機簡訊則直接做好記錄通過</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: red;">【權利分割租售】</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">與法務確認，可以進行買賣，重點在後續，例如：公同共用需要其他全體共有人同意才能進行全屋/土地異動行為，所以我們月臺不用限制分別共有，公同共有都能買賣</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: red;">【法定用途，一般事務所，刊登住宅】</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">一般律師事務所，刊登住宅，格局勾選開放式，特色備註格局，會員怕被罰款而不想填寫，確認流程</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">房子在藤本上的主要用途是一般事務所，可分兩種情形：</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">1</span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">、使用分區是商業區，雖然是一般事務所，但可以供住家使用</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">2</span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">、使用分區是工業區，就會有工業住宅違法性的問題</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">如果是仲介明明工業區刊登住宅用，會被處罰6-30萬元的罰款</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">第一種：請會員如實填寫格局；第二種：需改到辦公刊登，就算不填寫格局也可能會被罰，請會員依照藤本如實填寫即可</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: red;">【求租審核】</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">1</span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">、出租刊登成求租，刪除廣告發站內簡訊</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">2</span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">、填寫虛假金額，將欄位金額改為“0”即可</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">3</span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">、仲介刊登此類廣告、發仲介冒充房客違規簡訊，刪除廣告做好記錄</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">4</span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">、廣告為不同範圍坪數且較多地段，發仲介冒充房客違規簡訊，刪除廣告做好記錄</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">5</span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px; color: black; font-weight: normal;">、仲介身份不能刊登此類廣告，（屋主、代理人、投資客）只要沒有洗版，諮詢內容不真實情況下均可開放刊登權利</span></strong></span></p></td></tr></tbody></table><p style="margin-left: 28px;"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-size: 14px;"></span></strong></span><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-19 10:56:34</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-indent:16px;line-height:150%"><a name="OLE_LINK24"></a><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px; line-height: 150%;">3.2</span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 16px; line-height: 150%;">審核案例說明</span></strong></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">3.2.1 </span></strong><strong><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">“需放待處理電話聯絡確認”案例</span></strong></span></p><p style="margin: 8px 0px 8px 24px; line-height: 150%;"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">1）樓層上下不一致(請見附圖)；</span></p><p><img src="/upload/question/20201019/1603075891705454.png" alt="圖片.png"><span style="font-size: 14px; font-family: &quot;Times New Roman&quot;, serif;"><br></span></p><p><br></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">2）現況及坪數填寫有誤(請見附圖)；</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"><img src="/upload/question/20201019/1603075933753572.png" alt="圖片.png"></span></p><p><br><span style="font-size: 14px; font-family: &quot;Times New Roman&quot;, serif;"></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">3）刊登多間照片，賬號內尚未聯絡過的(請見附圖)；<img src="/upload/question/20201019/1603075971203146.png" alt="圖片.png"></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><a name="OLE_LINK1"></a><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">4）照片為文字圖，PS多個位置，賬號內尚未聯絡過的(請見附圖)；</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"><img src="/upload/question/20201019/1603076006459618.png" alt="圖片.png"><br></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">5）照片備註其他網站名稱或網址，賬號內未聯絡過(請見附圖)；</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"><img src="/upload/question/20201019/1603076042139738.png" alt="圖片.png"><br></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">6）照片為拼接圖，賬號內未聯絡過(請見附圖)；</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"><img src="/upload/question/20201019/1603076131450554.png" alt="圖片.png"><br></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><a name="OLE_LINK4"></a><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">7）整層住家，坪數小於10坪(請見附圖)；</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"><img src="/upload/question/20201019/1603076159677073.png" alt="圖片.png"><br></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">8）格局上下不一致(請見附圖)；</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"><img src="/upload/question/20201019/1603076190947335.png" alt="圖片.png"><br></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><br></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-19 11:00:24</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin: 8px 0px 8px 24px; line-height: 150%;"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="line-height: 150%;">3.2.2 </span></strong><strong><span style="line-height: 150%;">“發站內簡訊通知”案例</span></strong></span></p><p style="margin: 8px 0px 8px 24px; line-height: 150%;"><strong><span style=";line-height:150%;font-family:SimSun"></span></strong><strong><span style=";line-height:150%;font-family:SimSun"></span></strong></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">1）備註其他資訊(請見附圖)；</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"><img src="/upload/question/20201019/1603076279454679.png" alt="圖片.png"><br></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">2）備註非591網址或網站名稱(請見附圖)；</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><br><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"></span><img src="/upload/question/20201019/1603076409150421.png" alt="圖片.png"><br></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><br></p><p style="margin: 8px 0px 8px 24px; line-height: 150%;"><strong><span style=";line-height:150%;font-family:SimSun"><br></span></strong><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-19 11:01:19</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="line-height: 150%;">3.3</span></strong><strong><span style="line-height: 150%;">違規處理流程</span></strong></span></p><h4><span style="font-size: 14px; line-height: 155%; font-family: 微软雅黑, Microsoft YaHei;">&nbsp;&nbsp; 3.3.1違規處理流程</span><span style="font-size: 14px; line-height: 155%; font-family: 微软雅黑, Microsoft YaHei;"></span></h4><table cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; padding: 0px 7px;" width="340" valign="top"><p><span style="color: black; font-family: 微软雅黑, Microsoft YaHei;">違規次數</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="375" valign="top"><p><span style="color: black; font-family: 微软雅黑, Microsoft YaHei;">處罰規則</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="340" valign="top"><p><span style="color: black; font-family: 微软雅黑, Microsoft YaHei;">第一至三次</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="375" valign="top"><p><span style="color: black; font-family: 微软雅黑, Microsoft YaHei;">網站將違規資訊編輯，發站內簡訊警告</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="340" valign="top"><p><span style="color: black; font-family: 微软雅黑, Microsoft YaHei;">第四次</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="375" valign="top"><p><span style="color: black; font-family: 微软雅黑, Microsoft YaHei;">帳號學習，刊登時需通過網站試題考核，考核通過方可複權</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="340" valign="top"><p><span style="color: black; font-family: 微软雅黑, Microsoft YaHei;">第五次</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="375" valign="top"><p><span style="color: black; font-family: 微软雅黑, Microsoft YaHei;">帳號限制刊登3天，3天后系統自動複權</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="340" valign="top"><p><span style="color: black; font-family: 微软雅黑, Microsoft YaHei;">第六次</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="375" valign="top"><p><span style="color: black; font-family: 微软雅黑, Microsoft YaHei;">第六次以上（含第六次）帳號暫時停權，請聯絡客服中心</span></p></td></tr><tr><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="907" valign="top"><p><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; color: red;">注</span></strong><span style="font-family: 微软雅黑, Microsoft YaHei; color: red;">：帳號當天多次出現違規只記一次，第二天仍出現違規則按流程處理</span></span></p><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei;">需操作【限制或停權】時請先發註冊手機簡訊通知會員（從6月5日開始執行）</span></p><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei;">限制簡訊模板：【591】親愛的會員：您帳號新刊登違規第五次，現新刊登功能將被限制使用3天，3天後系統自動復權，如有疑問，請您聯絡客服中心，591感謝您！</span></p><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei;">停權簡訊模板：【591】親愛的會員：您帳號新刊登違規已累計第*次，現已被停權，請您聯絡客服中心瞭解並復權，591感謝您！</span></p></td></tr></tbody></table></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-19 11:08:22</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-size: 14px;">3.3.2 會員詳細頁面違規設定操作步驟</span></strong></span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-size: 14px;"><img src="/upload/question/20201019/1603076899738249.png" alt="圖片.png"></span></strong></span><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-19 11:09:51</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-size: 16px; line-height: 150%;">4．<span style="font-style: normal; font-variant: normal; font-weight: normal; font-stretch: normal; font-size: 9px; line-height: normal; font-size-adjust: none; font-kerning: auto; font-language-override: normal; font-feature-settings: normal;">&nbsp; </span></span></strong><strong><span style="font-size: 16px; line-height: 150%;">新刊登審核操作</span></strong></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">4.1 審核基本操作</span></p><p style="margin: 8px 0px 8px 24px; line-height: 150%;"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">4.1.1路徑：我們需要進入“客服後臺→客服相關→資料審覈→房屋新刊登”列表中操作。請見圖示：</span></p><p style="margin: 8px 0px 8px 24px; line-height: 150%;"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"><img src="/upload/question/20201019/1603077015922833.png" alt="圖片.png"><br></span></p><p style="margin: 8px 0px 8px 24px; line-height: 150%;"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"><img src="/upload/question/20201019/1603077036311226.png" alt="圖片.png"><br></span></p><p style="margin: 8px 0px 8px 24px; line-height: 150%;"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"><img src="/upload/question/20201019/1603077074751400.png" alt="圖片.png"></span></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-19 11:12:19</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin: 8px 0px 8px 24px; line-height: 150%;"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">4.1.2新刊登審核詳細頁：修改、審核商品、發送站內簡訊</span></p><p style="margin: 8px 0px 8px 24px; line-height: 150%;"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"><img src="/upload/question/20201019/1603077124554251.png" alt="圖片.png"><br></span></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-19 11:19:11</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-indent:21px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">4.2違規物件操作步驟</span></p><p style="margin: 8px 0px; text-indent: 21px; line-height: 150%;"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">4.2.1 放入待處理操作步驟</span></p><p style="margin: 8px 0px; text-indent: 21px; line-height: 150%;"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"><img src="/upload/question/20201019/1603077446980825.png" alt="圖片.png"><br></span></p><p style="margin: 8px 0px; text-indent: 21px; line-height: 150%;"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"></span></p><p style="margin-left:14px"><span style="font-family: 微软雅黑, Microsoft YaHei;">4.2.2從第四次開始記違規需做好記錄，如：第5次違規刊登，帳號限制刊登三天，特色備註“多戶可選”，已編輯，點擊“加入違規統計”客服記錄操作</span></p><p style="margin: 8px 0px; text-indent: 21px; line-height: 150%;"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"><img src="/upload/question/20201019/1603077497467791.png" alt="圖片.png"><br></span></p><p style="margin: 8px 0px; text-indent: 21px; line-height: 150%;"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-indent:21px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei;"><span style="line-height: 150%;">4.2.3站內簡訊發送頁<span style="color: red;">（</span></span><span style="color: red;">前三次違規發站內簡訊通知</span><span style="line-height: 150%; color: red;">）</span></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-indent:21px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei;"><span style="line-height: 150%; color: red;"><img src="/upload/question/20201019/1603077543322835.png" alt="圖片.png"><br></span></span></p><p style="margin: 8px 0px; text-indent: 21px; line-height: 150%;"><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-19 11:33:07</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-size: 16px; line-height: 150%;">5</span></strong><strong><span style="font-size: 16px; line-height: 150%;">、新刊登撥出</span></strong></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei;"><strong><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">&nbsp;</span></strong><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">5.1新刊登撥出流程</span></span></p><p style="margin: 8px 0px 8px 39px; line-height: 150%;"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">l<span style="font: normal normal normal normal 9px 微软雅黑, Microsoft YaHei;">&nbsp;&nbsp; </span>資訊類</span></p><p><br></p><table width="652" cellspacing="0" cellpadding="0"><tbody><tr style=";height:65px" class="firstRow"><td rowspan="5" style="border: 1px solid windowtext; padding: 0px 7px;" width="28" height="65"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">資訊類</span></p></td><td rowspan="2" style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="76" height="65"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">聯絡到</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="50" height="65"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">配合</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="472" height="65"><p><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">第一天：同意客服修改，直接處理並審核通過；需自行修改，跟蹤至第二天</span></p><p><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">第二天：查看是否修改正確，若正確審核通過，不正確則依照記錄再聯絡或編輯（聯絡無人接聽需發手機簡訊通知）</span></p><p><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">第三天：查看是否修改正確，若仍未修改，發手機簡訊+郵件通知關閉廣告</span></p></td></tr><tr style=";height:66px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="50" height="66"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">不配合</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="472" height="66"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">第一天：聯絡不配合處理，則做好記錄，發手機簡訊通知</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">第二天：再次聯絡仍然不配合，發手機簡訊+郵件通知關閉廣告</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">PS：特殊情況請與現場帶班主任回饋確認</span></p></td></tr><tr style=";height:37px"><td rowspan="2" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="76" height="37"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">聯絡不到（跟2天）</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="50" height="37"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">聯絡到</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="472" height="37"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">第二天：聯絡同意修改，直接處理並審核通過；需自行修改，跟蹤至第三天</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">第三天：查看是否修改正確，若仍未修改，發手機簡訊+郵件通知關閉廣告</span></p></td></tr><tr><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="50" valign="top"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">聯絡不到</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="472"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">發手機簡訊+郵件通知關閉廣告，做好記錄</span></p></td></tr><tr><td colspan="3" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="624" valign="top"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">注：住家/住宅坪數小於10坪，聯絡2天無人接聽或不配合，做好記錄審核通過</span></p></td></tr></tbody></table><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:39px;line-height:150%"><span style=";line-height:150%;font-family:Wingdings">l</span><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"><span style="font-style: normal; font-variant: normal; font-weight: normal; font-stretch: normal; font-size: 9px; line-height: normal; font-size-adjust: none; font-kerning: auto; font-language-override: normal; font-feature-settings: normal;">&nbsp;&nbsp; </span>法拍屋</span></p><table width="648" cellspacing="0" cellpadding="0"><tbody><tr style=";height:31px" class="firstRow"><td rowspan="3" style="border: 1px solid windowtext; padding: 0px 7px;" width="28" height="31"><p><span style="color: black; font-family: 微软雅黑, Microsoft YaHei;">法拍屋</span></p></td><td rowspan="2" style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="134" valign="top" height="31"><p><span style="color: black; font-family: 微软雅黑, Microsoft YaHei;">聯絡到</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="141" height="31"><p><span style="color: black; font-family: 微软雅黑, Microsoft YaHei;">承認（不管是否查到）</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="345" height="31"><p><span style="color: black; font-family: 微软雅黑, Microsoft YaHei;">協助轉至法拍屋</span></p></td></tr><tr style=";height:31px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="141" height="31"><p><span style="font-family: 微软雅黑, Microsoft YaHei; color: black;">不承認/不確定</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="345" height="31"><p><span style="font-size: 14px; color: black; font-family: 微软雅黑, Microsoft YaHei;">有查到：僅提醒</span></p><p><span style="font-size: 14px; color: black; font-family: 微软雅黑, Microsoft YaHei;">查不到：做好記錄，不再跟進</span></p></td></tr><tr style=";height:31px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="134" valign="top" height="31"><p><span style="color: black; font-family: 微软雅黑, Microsoft YaHei;">聯絡不到</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="141" height="31"><p><span style="color: black; font-family: 微软雅黑, Microsoft YaHei;">有無查到</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="345" height="31"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">發簡訊，做好記錄，不再跟進</span></p></td></tr></tbody></table><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:39px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">l<span style="font-style: normal; font-variant: normal; font-weight: normal; font-stretch: normal; font-size: 9px; line-height: normal; font-size-adjust: none; font-kerning: auto; font-language-override: normal; font-feature-settings: normal;">&nbsp;&nbsp; </span>法案類</span></p><table width="647" cellspacing="0" cellpadding="0"><tbody><tr style=";height:31px" class="firstRow"><td rowspan="3" style="border: 1px solid windowtext; padding: 0px 7px;" width="28" height="31"><p><span style="font-size: 13px; color: black; font-family: 微软雅黑, Microsoft YaHei;">法案</span></p></td><td rowspan="2" style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="85" valign="top" height="31"><p><span style="font-size: 13px; color: black; font-family: 微软雅黑, Microsoft YaHei;">已傳藤本</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="100" height="31"><p><span style="font-size: 13px; color: black; font-family: 微软雅黑, Microsoft YaHei;">匹配不一致（聯絡到）</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="383" height="31"><p><span style="font-size: 13px; font-family: 微软雅黑, Microsoft YaHei;">1、直接幫其修改資料核實或勾選未辦產權登記</span></p><p><span style="font-size: 13px; font-family: 微软雅黑, Microsoft YaHei;">2、聯絡藤本上傳有誤，則重新上傳，跟蹤至第二天，如核實正確直接通過，若未上傳做好記錄通過</span></p></td></tr><tr style=";height:31px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="100" height="31"><p><span style="font-size: 13px; color: black; font-family: 微软雅黑, Microsoft YaHei;">匹配不一致（聯絡不到）</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="383" height="31"><p><span style="font-size: 13px; font-family: 微软雅黑, Microsoft YaHei;">第一天：無人接聽，做好記錄，跟蹤至第二天</span></p><p><span style="font-size: 13px; font-family: 微软雅黑, Microsoft YaHei;">第二天：聯絡到根據結果處理；仍無人接聽，則做好記通過</span></p></td></tr><tr style=";height:31px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="85" valign="top" height="31"><p><span style="font-size: 13px; color: black; font-family: 微软雅黑, Microsoft YaHei;">未傳藤本</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="100" height="31"><p><span style="font-size: 13px; color: black; font-family: 微软雅黑, Microsoft YaHei;">/</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="383" height="31"><p><span style="font-size: 13px; color: black; font-family: 微软雅黑, Microsoft YaHei;">目前處理方式直接審核通過</span></p></td></tr><tr style=";height:31px"><td colspan="4" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="647" height="31"><p><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 13px; color: red;">註：帳號同一天出現多筆物件違規放待，只聯絡一次（當天聯絡之後在放待處理的物件今日無需再次聯絡和發簡訊通知），直接做好記錄，第二天按正常流程處理。</span></p></td></tr></tbody></table><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:39px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">l<span style="font-style: normal; font-variant: normal; font-weight: normal; font-stretch: normal; font-size: 9px; line-height: normal; font-size-adjust: none; font-kerning: auto; font-language-override: normal; font-feature-settings: normal;">&nbsp;&nbsp; </span>照片類</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:39px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"></span></p><table width="650" cellspacing="0" cellpadding="0"><tbody><tr style=";height:31px" class="firstRow"><td rowspan="4" style="border: 1px solid windowtext; padding: 0px 7px;" width="28" height="31"><p><span style="color: black; font-family: 微软雅黑, Microsoft YaHei;">照片類</span></p></td><td rowspan="2" style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="84" height="31"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">聯絡到</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="66" height="31"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">配合</span></p></td><td style="border-color: windowtext windowtext windowtext currentcolor; border-style: solid solid solid none; border-width: 1px 1px 1px medium; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="471" height="31"><p><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">第一天：同意客服修改，直接處理並審核通過；需自行修改，跟蹤至第二天</span></p><p><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">第二天：查看是否修改，若已改審核通過，未修改則依照記錄再聯絡或編輯</span></p><p><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">第三天：查看是否修改正確，若仍未修改，直接編輯違規照片，審核通過</span></p></td></tr><tr style=";height:31px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="66" height="31"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">不配合</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="471" height="31"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">第一天：聯絡不配合處理則做好記錄，發手機簡訊通知</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">第二天：聯絡仍不同意處理，直接刪除違規照片，審核通過</span></p></td></tr><tr style=";height:31px"><td rowspan="2" style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="84" height="31"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">聯絡不到（跟2天）</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="66" height="31"><p><span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">配合</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="471" height="31"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">根據結果處理</span></p></td></tr><tr style=";height:31px"><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="66" height="31"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">不配合</span></p></td><td style="border-color: currentcolor windowtext windowtext currentcolor; border-style: none solid solid none; border-width: medium 1px 1px medium; padding: 0px 7px;" width="471" height="31"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">發站內簡訊，直接編輯違規照片，做好記錄審核通過</span></p></td></tr><tr style=";height:31px"><td colspan="4" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="650" height="31"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">注：</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">1、聯絡2天均無人接聽，發簡訊編輯刪除，後續同一帳號審核到照片違規仍需放待處理聯絡</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; color: red;">2、圖片類僅撥一次（不同類型的未撥過的第一次仍需放待處理，但無需撥出只發註冊手機簡訊通知，跟進一天，第二天未處理可直接編輯；同類型已聯絡過的，再次出現違規則按違規次數刪除即可）</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; color: red;">3、同個會員同一天圖片出現一個類型聯絡過一個類型未聯絡過的處理流程：</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; color: red;">a.審核到兩種類型或以上，則一併放待處理發手機簡訊通知，第二天跟進處理</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; color: red;">b.先審核到之前未聯絡過的類型，已發簡訊通知，後又審核到之前聯絡過的類型，無需再發手機簡訊，直接放待處理記錄今日不算違規次數，第二天一併處理，詳情可參考該筆物件：http://591.com.tw/fcj74</span></p></td></tr></tbody></table><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:39px;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"><br></span><br></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-19 11:38:56</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin: 8px 0px; line-height: 150%;"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">5.2新刊登撥出參考話術</span></p><table width="640" cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td colspan="2" style="border: 1px solid windowtext; padding: 0px 7px;" width="633" valign="top"><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei;">開頭問候語：</span></p></td><td style=";border:none;padding:0 0 0 0" width="8"><br></td></tr><tr><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="633" valign="top"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">您好！這邊是591客服中心~請問是XX先生/小姐對嗎？不好意思打擾~</span></p></td><td style=";border:none;padding:0 0 0 0" width="8"><br></td></tr><tr><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="633" valign="top"><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei;">確認話術</span></p></td><td style=";border:none;padding:0 0 0 0" width="8"><br></td></tr><tr><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="633" valign="top"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">請問您有在591網站上刊登一則三重區重新路五段（位址）8000元（金額）的套房（類型）出租廣告嗎？</span></p></td><td style=";border:none;padding:0 0 0 0" width="8"><br></td></tr><tr><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="633" valign="top"><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei;">非會員本人接聽</span></p></td><td style=";border:none;padding:0 0 0 0" width="8"><br></td></tr><tr><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="633" valign="top"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">1.瞭解，請問可以幫忙轉告一下刊登者，讓他來電591確認下廣告資料嗎</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">2.好的，請問他有別的聯絡電話嗎</span></p></td><td style=";border:none;padding:0 0 0 0" width="8"><br></td></tr><tr><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="633" valign="top"><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei;">要求撥打其他人聯絡問題，例如秘書</span></p></td><td style=";border:none;padding:0 0 0 0" width="8"><br></td></tr><tr><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="633" valign="top"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">1.好的，麻煩您提供聯絡電話，他怎麼稱呼呢，客服聯絡他確認唷</span></p></td><td style=";border:none;padding:0 0 0 0" width="8"><br></td></tr><tr><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="633" valign="top"><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei;">獨立套房放多間照片</span></p></td><td style=";border:none;padding:0 0 0 0" width="8"><br></td></tr><tr><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="633" valign="top"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">1.不好意思唷，查看您有放不同房間照片，請問是分租套房嗎</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">2.獨立套房是一房一廳一衛的嗎，請問有獨立門牌號嗎，只能放一間房屋照片唷</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">3.網站有區分獨立套房和分租套房，獨立套房是獨立門牌號，有單獨的權狀，不需要和別人共用走道或廚房的唷，分租套房為一個住家裡面分開的房間出租，需要和別人共用一個門牌號的，可以備註多個房間出租唷</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">4.獨立套房僅能放一間房屋照片，備註一個房間出租唷，請問可以麻煩您刪除不同房間照片嗎？</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">5.查看冷氣和窗戶位置都不一樣，您需要放現況要出租的那間房屋的照片唷，可以麻煩您今天處理一下嗎？</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">6.很抱歉，可能是之前系統審核通過了，由於目前查看確實放了不同房間照片，請問您是整棟有多個房間出租，或者可以改成分租套房刊登嗎，刊登分租套房可備註多個房間出租的唷</span></p></td><td style=";border:none;padding:0 0 0 0" width="8"><br></td></tr><tr><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="633" valign="top"><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei;">會員亦可分租亦可整租</span></p></td><td style=";border:none;padding:0 0 0 0" width="8"><br></td></tr><tr><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="633" valign="top"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">1.不好意思唷，這邊想確認下您是整層住家要租，還是只出租其中一個房間呢？</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">2.若你是整層住家，是需要填寫整層住家坪數與租金和格局唷，若只是出租其中一個房間，是需要填寫一個房間的坪數和租金唷。因為廣告只能用一種類型刊登唷，請問您想用什麼類型刊登呢？</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">3.若您是需要兩種方式刊登是需要刊登兩筆廣告的，再互相備註廣告唷，客服建議您在電話裡和房客說亦可整層出租醬紫唷</span></p></td><td style=";border:none;border-bottom:solid windowtext 1px" width="8"><br></td></tr><tr><td style=";border:none;padding:0 0 0 0" width="7"><br></td><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="633" valign="top"><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei;">資訊有誤</span></p></td></tr><tr><td style=";border:none;padding:0 0 0 0" width="7"><br></td><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="633" valign="top"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">1.查看廣告欄位填寫5000元，特色備註5500元，請問正確的租金是多少呢</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">2.不好意思唷，查看樓層填寫為整棟出租，但廣告填寫的租金是一樓，請問您是5000元可以租到整棟房屋的嗎？</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">3.很抱歉，因為我們是以欄位資訊為准，若您需整棟出租要填寫整棟租金，若您填寫一樓的租金，樓層也僅能填寫一樓出租，是不能在廣告備註整棟出租唷</span></p></td></tr><tr><td style=";border:none;padding:0 0 0 0" width="7"><br></td><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="633" valign="top"><p><span style="color: red; font-family: 微软雅黑, Microsoft YaHei;">會員懷疑是詐騙電話</span></p></td></tr><tr><td style=";border:none;padding:0 0 0 0" width="7"><br></td><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="633" valign="top"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">1.由於廣告資訊有誤，客服才會電話聯絡您確認唷</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">2.很抱歉，若您怕是詐騙電話，請您方便時來電客服中心處理一下此問題唷</span></p></td></tr><tr><td style=";border:none;padding:0 0 0 0" width="7"><br></td><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="633" valign="top"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">會員不同意刪除不符合規範的照片</span></p></td></tr><tr><td style=";border:none;padding:0 0 0 0" width="7"><br></td><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="633" valign="top"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">1.查看廣告第一張照片上有標識了一些文字，文字的部分有點太大，遮住了照片原本的樣子，我們目前對於標識文字的大小要求是在照片的1/5內唷，還需要麻煩您幫忙將照片做一下修改</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">2.非常抱歉，如果文字浮水印太大，影響照片查看的效果，也影響房客的找屋體驗，為了給大家提供良好的租屋環境，所以我們有做了醬紫的刊登規則，還需要請您多多配合下</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">3.瞭解，若您擔心照片會被盜用的問題，在不影響照片的品質下，PS的文字可以標識部分在照片1/5內，照片中間不能帶有底色的照片也是可以的，建議您可以參考一下591自帶的浮水印效果唷，非常感謝您的理解</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">4.我們會根據文字大小分為五等分，不能占照片長度或寬度超過五分之一，建議您至網站刊登規則內查看判斷的標準，規則內有符合規範照片給您做參考唷，網站對所有會員均一視同仁，其他會員放此類照片也一樣需要刪除唷，很抱歉麻煩您了。</span></p></td></tr><tr><td style=";border:none;padding:0 0 0 0" width="7"><br></td><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="633" valign="top"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">會員要求自行處理</span></p></td></tr><tr><td style=";border:none;padding:0 0 0 0" width="7"><br></td><td colspan="2" style="border-color: currentcolor windowtext windowtext; border-style: none solid solid; border-width: medium 1px 1px; border-image: none 100% / 1 / 0 stretch; -moz-border-top-colors: none; -moz-border-left-colors: none; -moz-border-bottom-colors: none; -moz-border-right-colors: none; padding: 0px 7px;" width="633" valign="top"><p><span style="font-family: 微软雅黑, Microsoft YaHei;">1.麻煩您今天修改，客服明天會查看唷</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">2.請問您方便今天處理嗎，客服明天查看，若您未處理客服再代為刪除唷</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">3.瞭解，那我們可以約個處理時間嗎，請問兩天時間夠嗎？</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei;">4.好的，麻煩您了，客服後天查看未處理再代為處理唷，很抱歉打擾您了</span></p></td></tr></tbody></table><p style="margin: 8px 0px; line-height: 150%;"><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-19 11:44:22</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;line-height:150%"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">5.3新刊登撥出操作步驟</span></p><p style="text-indent:13px"><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">進入客服後臺→客服相關→電話撥出→無法案問題。請見圖</span></p><p><img src="/upload/question/20201019/1603078966346923.png" alt="圖片.png"></p><p><img src="/upload/question/20201019/1603078997119442.png" alt="圖片.png"></p><p><img src="/upload/question/20201019/1603079051479841.png" alt="圖片.png"></p><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-19 11:46:22</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">5.4 新刊登手機簡訊發送模板</span></p><p><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"><img src="/upload/question/20201019/1603079349799538.png" alt="圖片.png"><br></span></p><p><img src="/upload/question/20201019/1603079414441945.png" alt="圖片.png"><br><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"></span></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/static/timg.jpeg">&nbsp;&nbsp;&nbsp;
              <span class="user">王菊芳</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-10-19 12:00:10</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p style="margin: 8px 0px; text-indent: 28px; line-height: 150%;"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;">5.5新刊登郵件發送模板</span></p><p style="margin: 8px 0px; text-indent: 28px; line-height: 150%;"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"><img src="/upload/question/20201019/1603079944426796.png" alt="圖片.png"><br></span></p><p style="margin: 8px 0px; text-indent: 28px; line-height: 150%;"><span style="font-family: 微软雅黑, Microsoft YaHei; line-height: 150%;"><img src="/upload/question/20201019/1603079961180530.png" alt="圖片.png"></span></p></div></div> <div><span class="button">我要編輯</span></div></li></ul> <h3 class="more-answer">我要補充</h3> <div style=""><div id="editor_37592273099055896" class="edui-default" style=""><div id="edui1" class="edui-editor  edui-default" style="width: 100%; z-index: 999;"><div id="edui1_toolbarbox" class="edui-editor-toolbarbox edui-default"><div id="edui1_toolbarboxouter" class="edui-editor-toolbarboxouter edui-default"><div class="edui-editor-toolbarboxinner edui-default"><div id="edui2" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui2&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui3" class="edui-box edui-button edui-for-fullscreen edui-default"><div id="edui3_state" onmousedown="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui3_body" unselectable="on" title="全屏" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui3&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui3&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui4" class="edui-box edui-button edui-for-source edui-default"><div id="edui4_state" onmousedown="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui4_body" unselectable="on" title="源代码" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui4&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui4&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui5" class="edui-box edui-button edui-for-undo edui-default"><div id="edui5_state" onmousedown="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui5_body" unselectable="on" title="撤销" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui5&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui5&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui6" class="edui-box edui-button edui-for-redo edui-default"><div id="edui6_state" onmousedown="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui6_body" unselectable="on" title="重做" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui6&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui6&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui7" class="edui-box edui-button edui-for-simpleupload edui-default"><div id="edui7_state" onmousedown="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui7_body" unselectable="on" title="单图上传" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui7&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui7&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui8" class="edui-box edui-button edui-for-imageleft edui-default"><div id="edui8_state" onmousedown="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui8_body" unselectable="on" title="左浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui8&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui8&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui9" class="edui-box edui-button edui-for-imageright edui-default"><div id="edui9_state" onmousedown="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui9_body" unselectable="on" title="右浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui9&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui9&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui10" class="edui-box edui-button edui-for-imagecenter edui-default"><div id="edui10_state" onmousedown="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui10_body" unselectable="on" title="居中" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui10&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui10&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui11" class="edui-box edui-separator  edui-default"></div><div id="edui12" class="edui-box edui-button edui-for-date edui-default"><div id="edui12_state" onmousedown="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui12_body" unselectable="on" title="日期" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui12&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui12&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui13" class="edui-box edui-button edui-for-time edui-default"><div id="edui13_state" onmousedown="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui13_body" unselectable="on" title="时间" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui13&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui13&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui14" class="edui-box edui-button edui-for-horizontal edui-default"><div id="edui14_state" onmousedown="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui14_body" unselectable="on" title="分隔线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui14&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui14&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui15" class="edui-box edui-separator  edui-default"></div><div id="edui16" class="edui-box edui-splitbutton edui-for-inserttable edui-default"><div title="插入表格" id="edui16_state" onmousedown="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui16_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onArrowClick();"></div></div></div></div><div id="edui19" class="edui-box edui-button edui-for-deletetable edui-default"><div id="edui19_state" onmousedown="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui19_body" unselectable="on" title="删除表格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui19&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui19&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui20" class="edui-box edui-button edui-for-insertrow edui-default"><div id="edui20_state" onmousedown="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui20_body" unselectable="on" title="前插入行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui20&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui20&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui21" class="edui-box edui-button edui-for-insertcol edui-default"><div id="edui21_state" onmousedown="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui21_body" unselectable="on" title="前插入列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui21&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui21&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui22" class="edui-box edui-button edui-for-mergeright edui-default"><div id="edui22_state" onmousedown="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui22_body" unselectable="on" title="右合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui22&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui22&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui23" class="edui-box edui-button edui-for-mergedown edui-default"><div id="edui23_state" onmousedown="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui23_body" unselectable="on" title="下合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui23&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui23&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui24" class="edui-box edui-button edui-for-deleterow edui-default"><div id="edui24_state" onmousedown="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui24_body" unselectable="on" title="删除行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui24&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui24&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui25" class="edui-box edui-button edui-for-deletecol edui-default"><div id="edui25_state" onmousedown="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui25_body" unselectable="on" title="删除列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui25&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui25&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui26" class="edui-box edui-button edui-for-splittorows edui-default"><div id="edui26_state" onmousedown="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui26_body" unselectable="on" title="拆分成行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui26&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui26&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui27" class="edui-box edui-button edui-for-splittocols edui-default"><div id="edui27_state" onmousedown="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui27_body" unselectable="on" title="拆分成列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui27&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui27&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui28" class="edui-box edui-button edui-for-splittocells edui-default"><div id="edui28_state" onmousedown="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui28_body" unselectable="on" title="完全拆分单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui28&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui28&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui29" class="edui-box edui-button edui-for-mergecells edui-default"><div id="edui29_state" onmousedown="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui29_body" unselectable="on" title="合并多个单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui29&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui29&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui36" class="edui-box edui-button edui-for-edittable edui-default"><div id="edui36_state" onmousedown="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui36_body" unselectable="on" title="表格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui36&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui36&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui41" class="edui-box edui-button edui-for-edittd edui-default"><div id="edui41_state" onmousedown="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui41_body" unselectable="on" title="单元格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui41&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui41&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui42" class="edui-box edui-separator  edui-default"></div><div id="edui43" class="edui-box edui-menubutton edui-for-lineheight edui-default"><div title="行间距" id="edui43_state" onmousedown="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui43_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onArrowClick();"></div></div></div></div><div id="edui52" class="edui-box edui-menubutton edui-for-rowspacingtop edui-default"><div title="段前距" id="edui52_state" onmousedown="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui52_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onArrowClick();"></div></div></div></div><div id="edui59" class="edui-box edui-menubutton edui-for-rowspacingbottom edui-default"><div title="段后距" id="edui59_state" onmousedown="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui59_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onArrowClick();"></div></div></div></div></div><div id="edui66" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui66&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui67" class="edui-box edui-combox edui-for-fontfamily edui-default"><div title="字体" id="edui67_state" onmousedown="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui67_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onButtonClick(event, this);">字体</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onArrowClick();"></div></div></div></div><div id="edui80" class="edui-box edui-combox edui-for-customstyle edui-default"><div title="自定义标题" id="edui80_state" onmousedown="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui80_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onButtonClick(event, this);">自定义标题</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onArrowClick();"></div></div></div></div><div id="edui86" class="edui-box edui-combox edui-for-paragraph edui-default"><div title="段落格式" id="edui86_state" onmousedown="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui86_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onButtonClick(event, this);">段落格式</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onArrowClick();"></div></div></div></div><div id="edui95" class="edui-box edui-combox edui-for-fontsize edui-default"><div title="字号" id="edui95_state" onmousedown="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui95_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onButtonClick(event, this);">字号</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onArrowClick();"></div></div></div></div><div id="edui110" class="edui-box edui-button edui-for-link edui-default"><div id="edui110_state" onmousedown="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui110_body" unselectable="on" title="超链接" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui110&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui110&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui111" class="edui-box edui-button edui-for-indent edui-default"><div id="edui111_state" onmousedown="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui111_body" unselectable="on" title="首行缩进" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui111&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui111&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui112" class="edui-box edui-button edui-for-bold edui-default"><div id="edui112_state" onmousedown="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui112_body" unselectable="on" title="加粗" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui112&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui112&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui113" class="edui-box edui-splitbutton edui-for-forecolor edui-default edui-colorbutton"><div title="字体颜色" id="edui113_state" onmousedown="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui113_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui113_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onArrowClick();"></div></div></div></div><div id="edui116" class="edui-box edui-splitbutton edui-for-backcolor edui-default edui-colorbutton"><div title="背景色" id="edui116_state" onmousedown="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui116_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui116_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onArrowClick();"></div></div></div></div><div id="edui119" class="edui-box edui-button edui-for-italic edui-default"><div id="edui119_state" onmousedown="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui119_body" unselectable="on" title="斜体" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui119&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui119&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui120" class="edui-box edui-button edui-for-underline edui-default"><div id="edui120_state" onmousedown="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui120_body" unselectable="on" title="下划线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui120&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui120&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui121" class="edui-box edui-button edui-for-strikethrough edui-default"><div id="edui121_state" onmousedown="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui121_body" unselectable="on" title="删除线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui121&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui121&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui122" class="edui-box edui-button edui-for-superscript edui-default"><div id="edui122_state" onmousedown="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui122_body" unselectable="on" title="上标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui122&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui122&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui123" class="edui-box edui-button edui-for-subscript edui-default"><div id="edui123_state" onmousedown="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui123_body" unselectable="on" title="下标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui123&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui123&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui124" class="edui-box edui-button edui-for-removeformat edui-default"><div id="edui124_state" onmousedown="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui124_body" unselectable="on" title="清除格式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui124&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui124&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui125" class="edui-box edui-button edui-for-blockquote edui-default"><div id="edui125_state" onmousedown="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui125_body" unselectable="on" title="引用" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui125&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui125&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui126" class="edui-box edui-button edui-for-pasteplain edui-default"><div id="edui126_state" onmousedown="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui126_body" unselectable="on" title="纯文本粘贴模式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui126&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui126&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui127" class="edui-box edui-menubutton edui-for-insertorderedlist edui-default"><div title="有序列表" id="edui127_state" onmousedown="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui127_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onArrowClick();"></div></div></div></div><div id="edui140" class="edui-box edui-menubutton edui-for-insertunorderedlist edui-default"><div title="无序列表" id="edui140_state" onmousedown="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui140_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onArrowClick();"></div></div></div></div><div id="edui147" class="edui-box edui-button edui-for-cleardoc edui-default"><div id="edui147_state" onmousedown="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui147_body" unselectable="on" title="清空文档" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui147&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui147&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div></div></div></div><div id="edui1_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;"><div id="edui1_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui1&quot;].showWordImageDialog();">点击上传</div><div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui1&quot;].hideToolbarMsg();">x</div><div id="edui1_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div><div style="height:0;overflow:hidden;clear:both;" class="edui-default"></div></div><div id="edui1_message_holder" class="edui-editor-messageholder edui-default"></div></div><div id="edui1_iframeholder" class="edui-editor-iframeholder edui-default" style="width: 100%; height: 300px; z-index: 999; overflow: hidden;"><iframe id="ueditor_0" width="100%" height="100%" frameborder="0" src="javascript:void(function(){document.open();document.write(&quot;<!DOCTYPE html><html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}
body{margin:8px;font-family:sans-serif;font-size:13px;}p{margin:5px 0;}</style><link rel='stylesheet' type='text/css' href='/static/ueditor/themes/iframe.css'/></head><body class='view' ></body><script type='text/javascript'  id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant0'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);</script></html>&quot;);document.close();}())"></iframe></div><div id="edui1_bottombar" class="edui-editor-bottomContainer edui-default"><table class="edui-default"><tbody class="edui-default"><tr class="edui-default"><td id="edui1_elementpath" class="edui-editor-bottombar edui-default"><div class="edui-editor-breadcrumb">元素路径:</div></td><td id="edui1_wordcount" class="edui-editor-wordcount edui-default">字数统计</td><td id="edui1_scale" class="edui-editor-scale edui-default" style="display: none;"><div class="edui-editor-icon edui-default"></div></td></tr></tbody></table></div><div id="edui1_scalelayer" class="edui-default"></div></div></div> <div class="edit-btn"><button type="button" class="button">提交回答</button> <label data-v-0fe26aab="" for="commonCheckbox" class="checkbox"><input data-v-0fe26aab="" type="checkbox" id="commonCheckbox" name="commonCheckbox"> <i data-v-0fe26aab=""></i> 需要彈窗，請勾選</label></div></div></div> <div class="index-right"><div class="right-bk"><h3 class="com-title"><a href="javascript:;">我的最爱</a></h3> <div class="item-box clearfix"><span>暫無數據</span></div></div> <div class="hotTag right-bk"><h3 class="com-title">热门标签</h3> <div class="hotTag-box item-box clearfix"><p>無數據</p></div></div> <div class="right-bk rank"><h3 class="com-title"><a href="javascript:;">冠軍排行榜</a></h3> <ul><li><div><span class="number">1</span> <img src="/static/timg.jpeg"> <span>王菊芳</span></div> <span class="score">612分</span></li><li><div><span class="number">2</span> <img src="/upload/head/10177.gif?time=1556271256"> <span>黃雪靜</span></div> <span class="score">258分</span></li><li><div><span class="number">3</span> <img src="/static/timg.jpeg"> <span>田霞</span></div> <span class="score">240分</span></li><li><div><span class="number">4</span> <img src="/static/timg.jpeg"> <span>曹超雲</span></div> <span class="score">168分</span></li><li><div><span class="number">5</span> <img src="/static/timg.jpeg"> <span>文宇</span></div> <span class="score">96分</span></li><li><div><span class="number">6</span> <img src="/upload/head/10217.png?time=1548059679"> <span>黃興興</span></div> <span class="score">78分</span></li><li><div><span class="number">7</span> <img src="/static/timg.jpeg"> <span>黃佳麗</span></div> <span class="score">62分</span></li><li><div><span class="number">8</span> <img src="/static/timg.jpeg"> <span>马泽鹏</span></div> <span class="score">46分</span></li><li><div><span class="number">9</span> <img src="/static/timg.jpeg"> <span>陳敏南</span></div> <span class="score">40分</span></li><li><div><span class="number">10</span> <img src="/static/timg.jpeg"> <span>Cs-刘婷</span></div> <span class="score">10分</span></li><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></ul></div> <div class="activeUser right-bk"><h3 class="com-title">活跃用户</h3> <div class="activeUser-box"><a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a></div></div> <img src="data:image/png;base64,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" class="to-top"></div></div> <!----> <!----> <!----> <div class="page-cnt footer"><p>T5客服知识库<span>|</span>zsk.591.com.tw</p></div></div></div><script type="text/javascript" src="/js/manifest.f192b.js"></script><script type="text/javascript" src="/js/vendor.17570.js"></script><script type="text/javascript" src="/js/app.58254.js"></script><div id="edui_fixedlayer" class="edui-default" style="position: fixed; left: 0px; top: 0px; width: 0px; height: 0px;"><div id="edui148" class="edui-popup  edui-bubble edui-default" onmousedown="return false;" style="display: none;"> <div id="edui148_body" class="edui-popup-body edui-default"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank" class="edui-default"></iframe> <div class="edui-shadow edui-default"></div> <div id="edui148_content" class="edui-popup-content edui-default">  </div> </div></div></div></body></html>