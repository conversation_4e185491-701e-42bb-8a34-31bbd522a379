<html class="nprogress-busy"><head><meta charset="utf-8"><title>591客服知識庫</title><link rel="stylesheet" href="/static/font/font-awesome.min.css"><link href="/css/app.e5aa4.css" rel="stylesheet"><script type="text/javascript" charset="utf-8" async="" src="/js/questionDetail.d0a6b.js"></script><style type="text/css">.que-detail-page .index-left .button{width:74px;height:32px;line-height:32px;font-size:13px;margin-right:10px}.que-detail-page .index-left .button:hover{color:#fff}.que-detail-page .detail-box{padding:20px 30px;border:1px solid #e5e5e5;border-radius:4px}.que-detail-page .detail-box h3{font-size:18px;color:#333;margin-bottom:10px}.que-detail-page .detail-box h3 span{color:#f77d24}.que-detail-page .detail-box .add-fav{cursor:pointer}.que-detail-page .d-b-menu{margin:15px 0 25px;font-size:13px;color:#999}.que-detail-page .d-b-menu b{display:inline-block;font-weight:400;margin:0 10px}.que-detail-page .d-b-menu .r{float:right;color:#f77d24}.que-detail-page .d-b-menu .r .fa{margin-right:5px;font-size:16px}.que-detail-page .d-b-cnt{margin-bottom:25px;font-size:13px;color:#666}.que-detail-page .submit-ask{display:block;margin:32px auto 0}.que-detail-page .reply-list{margin:30px 0 10px;overflow:hidden}.que-detail-page .reply-list .reply-item{padding:15px 30px 25px;margin-bottom:30px;border-radius:4px;border:1px solid #e5e5e5}.que-detail-page .reply-list .reply-item h4{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;height:50px;line-height:50px;font-size:13px}.que-detail-page .reply-list .reply-item h4 img{width:40px;height:40px;border-radius:50%}.que-detail-page .reply-list .reply-item h4 .time{color:#999}.que-detail-page .reply-list .reply-item h4 .button{-ms-flex-item-align:center;align-self:center}.que-detail-page .reply-list .reply-item h4 .delete{color:#666;cursor:pointer}.que-detail-page .reply-list .reply-item .reply-detail{padding:20px 0 30px}.que-detail-page .more-answer{font-size:18px;color:#666;margin-bottom:5px}.que-detail-page .editor-content table{width:100%;border:1px solid #ddd}.que-detail-page .editor-content table td,.que-detail-page .editor-content table th{border:1px solid #ddd;padding:2px 8px;line-height:2}.que-detail-page .editor-content img{max-width:100%;vertical-align:text-bottom}.que-detail-page .edit-btn{text-align:center;margin-top:20px}.que-detail-page .edit-btn .edit-cancle{background:#aaa}</style><style type="text/css">.head-info{line-height:26px;background:#f5f5f5;font-size:13px;color:#999}.head-info a{margin-left:20px;color:#333}.head-search{margin-top:22px}.head-search img{float:left}.head-search .srh-box{float:left;margin:20px 0 0 20px}.head-search .srh-box .srh-input{float:left;width:260px;height:38px;line-height:38px;border:2px solid #37a508;border-radius:4px 0 0 4px}.head-search .srh-box a,.head-search .srh-box button{float:left;width:80px;height:38px;line-height:36px;font-size:16px;color:#fff}.head-search .srh-box .srh-btn{border-radius:0 4px 4px 0}.head-search .srh-box .ask-btn{margin-left:10px}.head-nav{float:left;margin:20px 0 0 60px;color:#fff;font-size:16px;line-height:36px}.head-nav a{display:block;height:100%;color:#333}.head-nav li{position:relative;float:left;margin-right:35px}.head-nav .active a,.head-nav li:hover a{color:#30b940}.head-nav .active:after,.head-nav li:hover:after{display:block;content:"";height:3px;width:100%;background:#30b940}.head-pic{width:100%;height:90px;margin-top:20px;background:url(/static/banner.png) top no-repeat}</style><style type="text/css">.top-breadcrumb{height:20px;margin-top:10px;margin-bottom:40px;font-size:13px;color:#666}.top-breadcrumb .t-b-icon{color:#30b940}.top-breadcrumb span{margin-left:8px}.top-breadcrumb span .fa{font-size:16px;margin-left:8px}.top-breadcrumb span:first-child{margin-left:0}.top-breadcrumb .now{color:#30b940}</style><script type="text/javascript" src="/static/ueditor/ueditor.config.js?20210319" id="configScriptTag"></script><script type="text/javascript" src="/static/ueditor/ueditor.all.js?20210319" id="editorScriptTag"></script><style type="text/css">.checkbox[data-v-0fe26aab]{word-spacing:4px;vertical-align:bottom;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.checkbox i[data-v-0fe26aab]{display:inline-block;width:14px;height:14px;border-radius:2px;border:1px solid #ccc;background:#fff;position:relative;top:1px}.checkbox input[data-v-0fe26aab]{opacity:0;width:0;height:0;margin:0}.checkbox input:checked+i[data-v-0fe26aab]{background:#30b940;border-color:#30b940}.checkbox input:checked+i[data-v-0fe26aab]:before{content:"";position:absolute;left:2px;top:1px;width:11px;height:6px;border-bottom:2px solid #fff;border-left:2px solid #fff;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}</style><style type="text/css">.popbox{position:fixed;top:50%;left:50%;z-index:1011;background:#fff;border-radius:6px;overflow:hidden}.popbox h3{font-size:16px;line-height:40px;padding:0 20px}.popbox h3 span{float:right;color:#666;font-size:13px;cursor:pointer}.popbox .pb-cnt{padding:0 20px;font-size:13px}.popbox .pb-btn-area{position:absolute;bottom:0;left:0;z-index:1;width:100%;text-align:right;padding:0 20px 20px 0}.popbox .pb-btn-area button{height:32px;line-height:32px;border:0;background:#fff;cursor:pointer;border-radius:6px;margin-left:15px;padding:0 15px}.popbox .pb-btn-area button.pb-btn-ok{background:#daf3cf;color:#279005}.popbox .pb-btn-area button.pb-btn-ok:hover{background:#279005;color:#fff}</style><style type="text/css">.shadow{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1010;background:#000}</style><style type="text/css">.footer{margin-top:70px;margin-bottom:20px;padding-top:20px;text-align:center;color:#999;border-top:1px solid #f5f5f5}.footer span{margin:0 10px}</style><script src="/static/ueditor/lang/zh-cn/zh-cn.js" type="text/javascript" defer="defer"></script><link href="/static/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet"><script src="/static/ueditor/third-party/codemirror/codemirror.js" type="text/javascript" defer="defer"></script><link rel="stylesheet" type="text/css" href="/static/ueditor/third-party/codemirror/codemirror.css"><script src="/static/ueditor/third-party/zeroclipboard/ZeroClipboard.js" type="text/javascript" defer="defer"></script><style type="text/css">.activeUser,.favorite,.hotTag,.rank{margin-bottom:40px}</style><style type="text/css">.rank .rank-page{height:60px;line-height:60px;font-size:18px;margin-bottom:20px;text-align:center;background:#fafafa;border-radius:5px}.rank ul{padding:0 20px}.rank li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;border-bottom:1px dotted #f5f5f5;line-height:50px}.rank li .flag{position:relative;display:inline-block;width:20px;height:30px;background:#476472;vertical-align:middle;margin-right:12px;color:#fff;text-align:center;line-height:20px;font-size:13px;font-style:normal}.rank li .flag:after{content:"";position:absolute;bottom:-4px;left:-4px;border:14px solid transparent;border-bottom-color:#fff}.rank li:first-child .flag{background:#fb6757}.rank li:nth-child(2) .flag{background:#fac525}.rank li:nth-child(3) .flag{background:#00a9fb}.rank li img{width:36px;height:36px;border-radius:50%;margin-right:12px}</style><style type="text/css">.activeUser .activeUser-box a{display:inline-block;width:64px;height:64px;border-radius:50%;overflow:hidden;margin:0 6px 12px}</style><style type="text/css">.to-top{display:none;position:fixed;bottom:150px;z-index:5;width:50px;height:50px;cursor:pointer}</style></head><body style=""><div id="app" style=""><div class="que-detail-page" style=""><div class="wiki-hd"><div class="head-info"><p class="page-cnt">
      您好，欢迎来到591客服知识库
      <a href="https://www.591.com.tw/admin.php?new_url=https://zsk.591.com.tw">[请登入]</a> <!----></p></div> <div class="page-cnt head-search clearfix"><a href="/" class="router-link-active"><img src="/static/logo.png"></a> <ul class="head-nav clearfix"><li class="router-link-active active"><a href="/">591房屋交易网</a></li> <li class=""><a href="/life">客服充电站</a></li> <li class=""><a href="/notification">通知事项</a></li></ul> <div class="srh-box"><input type="text" placeholder="请输入搜索内容" class="srh-input"> <button type="button" class="button srh-btn">搜索</button> <a href="/ask/591" class="button button-orange ask-btn">提問</a></div></div> <div class="head-pic"></div></div> <div class="top-breadcrumb com-width"><span><a href="/collection/591/1302" class="">客服質檢相關<i class="fa fa-angle-double-right"></i></a></span><span><a href="/collection/591/1302/1389" class="">線上回復<i class="fa fa-angle-double-right"></i></a></span><span><strong class="now">问题详情</strong></span></div> <div class="com-width clearfix" style=""><div class="index-left" style=""><div class="detail-box"><h3><!---->線上作業流程及規範</h3> <p class="clearfix d-b-menu"><span class="l">
            黃雪靜&nbsp;&nbsp;&nbsp;浏览207次&nbsp;&nbsp;&nbsp;2019-09-06 16:42:26
          </span> <span class="r"><span class="add-fav"><i class="fa fa-heart"></i>加入最爱</span></span></p> <div class="d-b-cnt editor-content big-img-parent"><div><p>留言、信箱、申請、在線客服</p></div></div> <div class="d-b-button"><a href="/edit/591/3636" class="button">编辑</a> <button class="button button-orange">关闭提问</button></div></div> <div class="hr-line"></div> <ul class="reply-list big-img-parent"><li class="reply-item"><h4><div><img src="/upload/head/10177.gif?time=1556271256">&nbsp;&nbsp;&nbsp;
              <span class="user">黃雪靜</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2020-04-26 10:18:25</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p class="MsoListParagraph" style="margin-left:32px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong>一、</strong><strong>概述</strong></span></p><p class="MsoListParagraph" style="margin-left:32px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">線上回覆是一項會員與客服中心溝通的輔助工具，提供在非服務時間內或會員不方便使用電話時與客服中心聯絡溝通的管道，客服中心收到後將根據會員提出的問題一一解答，幫助會員解決問題使其滿意！</span></p><p class="MsoListParagraph" style="margin-left:32px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-left:32px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong>二、</strong><strong>線上回覆類別及特性</strong></span></p><table width="595" cellspacing="0" cellpadding="0"><tbody><tr style=";height:33px" class="firstRow"><td style="border: 1px solid windowtext; background: rgb(208, 206, 206); padding: 0px 7px;" width="293" height="33"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">回覆类别</span></p></td><td style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(208, 206, 206); padding: 0px 7px;" width="200" height="33"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">特性</span></p></td></tr><tr style=";height:30px"><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="293" height="30"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">留言</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="200" height="30"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">具有公開性</span></p></td></tr><tr><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="293"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">申訴</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="200"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">非公開，具有緊迫性</span></p></td></tr><tr><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="293"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">信箱</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="200"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">非公開，具有緊迫性</span></p></td></tr><tr><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="293"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">在線客服</span></p></td><td style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;" width="200"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">實時、靈活</span></p></td></tr><tr><td colspan="2" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="595"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">注：九點上班後，回覆專員需先檢查一遍留言、申訴、信箱，首先，處理留言；其次，按申訴及時性要求處理問題</span></p></td></tr></tbody></table><p class="MsoListParagraph" style="margin-left:32px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-left:32px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong>三、</strong><strong>適用對象</strong></span></p><p class="MsoListParagraph" style="margin-left:32px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">房屋交易事業部→客服部→三星或以上人員</span></p><p class="MsoListParagraph" style="margin-left:32px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;</span></p><p class="MsoListParagraph" style="margin-left:32px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 16px;"><strong>四、</strong><strong>线上回覆规范</strong></span></p><table width="595" cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; background: rgb(208, 206, 206); padding: 0px 7px;" width="595"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center;line-height:150%"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">留言處理規範</span></strong></span></p></td></tr><tr><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="595" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">【回覆完整性】</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">提醒類</span></strong><strong><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">，</span></strong><strong><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">如以下</span></strong><strong><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">：</span></strong><strong><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">（包含不僅限於）</span></strong></span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.<span style="color: black; font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>物件暫時關閉，需提醒關閉時間照常計算</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.<span style="color: black; font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>物件刊登錯誤申請取消，需提醒廣告資料會隨之刪除，需重新填寫</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3.<span style="color: black; font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>仲介需刪除頭像時，需提醒刪除後店鋪也會隨之關閉</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">解釋類，如以下：</span></strong><strong><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">（包含不僅限於）</span></strong></span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.<span style="color: black; font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; </span>會員申訴提出建議某項功能時，請粗略說明我們不提供該功能的原因，並重點重複會員的建議後記錄下來進行改善</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; </span></span><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">回覆前請理清會員的問題個數，一對一針對性回復</span></span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; &nbsp; </span></span><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">重複刊登已超出可取消範圍之回覆，請使用簡潔明瞭的原因話術：“因廣告於XX時間刊登，至今已曝光XX天”</span></span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>套餐到期需延長使用時間或是跨月的，請說明原因並適當的提供一些建議或提醒</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">【回覆主動性】</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">檢舉類</span></strong></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員線上舉報廣告不實或有誤，若留下廣告編號則需跟蹤到廣告處理完成之後再回覆舉報者處理結果；若未留下廣告編號則回覆，請舉報人留下相應廣告資訊由客服中心進行查確認</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">糾紛類</span></strong></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">若可通過客服中心來幫忙聯絡從而幫會員解決糾紛，客服中心需幫忙聯絡處理；但若不在客服中心服務範圍，或無法幫會員處理的問題，則可直接說明客服中心的立場從而拒絕，但是要盡力提供建議，安撫會員情緒</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">【回覆申請類】</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>當日重複儲值要求刷退，聯絡無人接聽時（專員同意則直接處理回覆結果）</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>要求退現、套餐延長、發票改開超過申報日等均優先申請</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>其它情況會員要求賠償等狀況優先反饋專員申請</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">【可直接編輯或刪除類】</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>留言提問中帶有網址，將網址部分編輯</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>留言內容若是宣傳打廣告等無關資訊，將整條留言刪除</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>內容已電話聯絡解決，將整條留言刪除（需跟進則記錄回撥）</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>同一用戶當天重複留言（內容全部一致），則可將重複留言內容做刪除只保留一筆記錄</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>留言中留有姓名、電話、帳號、密碼、詳細地址與郵箱等會員個人隱私資料時使用“*”號隱藏個人隱私資料</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">6.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>內容已信箱回覆，將整條留言刪除（信箱回覆格式皆見下圖）</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><img src="/upload/question/20200426/1587867500169081.png" alt="image.png"></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">【回覆要求】</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>會員留言優先電話聯絡處理</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>非會員有留聯絡電話或編號優先電話聯絡處理</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>符號為半型輸入法（格式）</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>無錯別字和簡體字</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>回覆的句子最長不超過25字</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">6.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>建議性的問題需記錄，提交至產品專員</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">7.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>回覆要相對及時、內容準確、親切全面</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">8.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>回覆承諾會員的需兌現</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">9.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>回覆內容盡量避免多次重複使用同樣語句</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">10. 回覆取消套餐使用“【】、&lt;&gt;”這樣的格式，例如：【新手套餐】、&lt;新手套餐&gt;</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">11. 取消廣告退還“筆數或點數”需回覆完整，若取消多筆廣告可用&lt;s****、s****&gt;</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">12. 繳費後明確表示需購買套餐的類型，請優先協助購買並回覆購買方法</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">13. 跟進的問題，需三天內依處理進度主動回覆給會員，不可拖延或不跟蹤</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">14. 需得到會員同意方可處理的業務，聯絡無人接聽時，不可直接操作處理，如：要求取消廣告</span></p></td></tr></tbody></table><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;line-height:150%"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;&nbsp;&nbsp; </span></strong></span></p><table width="595" cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; background: rgb(208, 206, 206); padding: 0px 7px;" width="595"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center;line-height:150%"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">申訴處理規範</span></strong></span></p></td></tr><tr><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="595" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">【回覆完整性】詳情可參考留言處理規範</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">【回覆主動性】</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">檢舉類</span></strong></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">會員線上舉報廣告不實或有誤，若留下廣告編號則需跟蹤到廣告處理完成之後再回覆舉報者處理結果；若未留下廣告編號則回覆，請舉報人留下相應廣告資訊由客服中心進行查確認</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">糾紛類</span></strong></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">若可通過客服中心來幫忙聯絡從而幫會員解決糾紛，客服中心需幫忙聯絡處理；但若不在客服中心服務範圍，或無法幫會員處理的問題，則可直接說明客服中心的立場從而拒絕，但是要盡力提供建議，安撫會員情緒</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">投訴類</span></strong><strong><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">（</span></strong><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">投訴的定義</span><strong><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">）</span></strong></span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>會員對網站使用，規則，客服服務不滿，已造成會員困擾或不便之申訴皆歸類為服務投訴</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>會員自行選擇了服務投訴，客服在回覆時需判斷是否為投訴，若否將分類修正為正對應類型</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>會員選擇為非投訴類別時，客服在回覆時需將其內容選擇為是，將其分類選擇為投訴</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">其他</span></strong><strong><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">類</span></strong></span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>會員要求電話聯絡的，需主動優先電話聯絡處理</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>會員同一天多次申訴，此情況需電話聯絡說明處理，且不可隨意刪除會員相同的申訴內容，每條申訴均需回覆</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>會員同一天多次申訴，但對於客服已回覆的上條申訴內容並未查看，此情況應電話聯絡會員確認說明</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">【回覆申請類】詳情可參考留言處理規範</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">【回覆優先電話聯絡類】</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>內容不詳細或無法理解會員問題時</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>發票已開立未收到，查詢已超過15天</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>物件無法搜尋、顯示異常、網站功能異常（客服測試均正常）</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>儲值後廣告無法/未開啟（需聯絡協助開並了解原因）</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>更改會員身份，賬號有多筆刊登物件，申訴未說明是否已離職</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">6.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>更改仲介認證資料，需告知先取消認證再重新上傳認證</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">7.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>對網站使用、規則、客服服務不滿，已造成嚴重困擾或損失時</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">8.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>需要取消套餐物件，核實當日為套餐有效期最後一日時</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">9.<span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">&nbsp; 退點，優先電話聯絡，無人接聽，內容明確要取消可直接處理，需提醒資料會隨之刪除</span></span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">10.退現，優先電話聯絡，無人接聽，用戶訴求明確可直接凍結點數，告知後續操作時效路徑<br></span></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">【回覆要求】</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>符號為半型輸入法</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>無錯別字和簡體字</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>回覆時需使用英文格式</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>回覆的句子最長不超過25字</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>建議性的問題需記錄，提交至產品專員</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">6.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>回覆要相對及時、內容準確、親切全面</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">7.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>回覆承諾會員的需兌現</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">8.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>回覆內容盡量避免多次重複使用同樣語句</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">9.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>回覆取消套餐使用“【】、&lt;&gt;”這樣的格式，例如：【新手套餐】、&lt;新手套餐&gt;</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">10. 取消廣告退還“筆數或點數”需回覆完整，若取消多筆廣告可用&lt;s****、s****&gt;</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">11. 繳費後明確表示需購買套餐的類型，請優先協助購買並回覆購買方法</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">12. 跟進的問題，需三天內依處理進度主動回覆給會員，不可拖延或不跟蹤</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">13. 需得到會員同意方可處理的業務，聯絡無人接聽時，不可直接操作處理，如：修改個人資料等</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">14.與網站核心業務無關的訴求，如：幫找房子，幫P圖，幫聯絡刊登者等需協助處理，不可推脫</span></p></td></tr></tbody></table><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;line-height:150%"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;&nbsp; </span></strong></span></p><table width="595" cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; background: rgb(208, 206, 206); padding: 0px 7px;" width="595"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center;line-height:150%"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">信箱處理規範</span></strong></span></p></td></tr><tr><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="595" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">【回覆完整性】、【回覆主動性】、【回覆申請類】、【回覆優先電話聯絡類】、【回覆要求】詳情可參考申訴處理規範</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">注意事項：</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>信箱回覆涉及個資時，請參照《</span><a href="file://192.168.8.29/publicfile/591%E6%88%BF%E5%B1%8B%E4%BA%A4%E6%98%93%E7%BD%91%EF%BC%88%E5%8F%B0%E6%B9%BE%EF%BC%89/%E3%80%9012%E3%80%91%E5%AE%A2%E6%9C%8D%E7%9B%B8%E5%85%B3/%E3%80%900104%E3%80%91%E5%AE%A2%E6%9C%8D%E8%B4%A8%E6%A3%80/01%E6%9C%8D%E5%8A%A1%E6%A0%87%E5%87%86/01%E8%A7%84%E8%8C%83/T5-%E4%BF%A1%E7%AE%B1%E8%99%95%E7%90%86%E6%B5%81%E7%A8%8B%E8%A6%8F%E7%AF%84.docx" style="text-decoration: underline; line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">信箱處理流程規範</span></a><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">》進行核實回覆</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>電話中或線上回覆引導會員將截圖、照片發送至信箱時需說明備註行動電話和問題</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>由處理信箱的客服收到直接處理</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>客服電話中或線上直接處理了則需截圖回報給處理的信箱的人回覆</span></p></td></tr></tbody></table><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;line-height:150%"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp;&nbsp;&nbsp; </span></strong></span></p><table width="595" cellspacing="0" cellpadding="0"><tbody><tr class="firstRow"><td style="border: 1px solid windowtext; background: rgb(208, 206, 206); padding: 0px 7px;" width="595"><p style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:0;text-align:center;line-height:150%"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><strong><span style="line-height: 150%; color: black; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">在線回覆規範</span></strong></span></p></td></tr><tr><td style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;" width="595" valign="top"><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">【回覆完整性】<span style="color: #FF0000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">詳情可參考留言處理規範</span></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">【回覆主動性】詳情可參考留言處理規範</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; </span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; line-height: 150%; color: red;">&nbsp; &nbsp;</span><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; line-height: 150%;">1.<span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal;">&nbsp;&nbsp; </span>會員進入機器人頁面已提問，轉入人工未再次發起對話，客服需主動解答會員的問題，例如：</span></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="font-size: 14px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; line-height: 150%;">&nbsp; &nbsp; &nbsp;<img src="/upload/question/20200514/1589447511872388.png" alt="image.png" style="width: 527px; height: 390px;" width="527" height="390"></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span><br></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">【開頭/結束語】</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>問候語：您好，客服**很高興為您服務^_^請問有什麼可以幫您？</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>結束語：感謝您使用線上客服，祝您生活愉快！</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0"><span style="color: gray; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">注：報結束語前需確認用戶是否還有其它問題，例如：請問還有什麼可以幫您的？或請問您還有其它疑問嗎？</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">【關閉對話框】</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;可關閉對話框情景：</span></span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1）發送結束語後，不管狀態是否在線，均可關閉對話框</span></p><p><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">&nbsp; &nbsp; &nbsp; 2）會員的問題已解決，並已回覆會員，但發送狀態顯示【離線發送】，可直接關閉對話框</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3）當用戶咨詢問題時，客服超時未答致用戶下線，不可直接將對話框關閉，需根據會員的問題進行離線解答或提供解決方案後，方可關閉對話框，例如：</span><br><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:28px"><img src="/upload/question/20200426/1587867565554361.png" alt="image.png"></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:25px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">回覆案例：</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:25px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">a.客服已測試可正常搜尋曝光，物件出現在***，建議重新搜尋看看，若仍然無法搜尋可再次發起在線對話或來電客服中心反饋唷</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:25px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">b.很抱歉，客服這邊測試也無法搜尋到，已將此問題反饋專員核實，待有結果會再回電說明，屆時請留意接聽（做好回電記錄）</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:25px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.不可關閉對話框情景：</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:25px"><span style="font-family:微软雅黑, Microsoft YaHei"><span style="font-size: 14px;">1）會員的問題未解決</span></span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:25px"><span style="font-family:微软雅黑, Microsoft YaHei"><span style="font-size: 14px;">2）與會員確認問題時，狀態顯示【離線發送】</span></span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:25px"><span style="font-family:微软雅黑, Microsoft YaHei"><span style="font-size: 14px;">3）與專員確認問題，且對話框已被鎖定</span></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">【帳號登出或交接或忙碌】</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>到下班時間，若用戶問題還未解決，不可直接關閉退出帳號，需將當前用戶服務完畢</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>帳號被搶登，確認好原因再次登入後需重新整理頁面，確保聊天記錄同步</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3.&nbsp; 兩線在服務時間內不可同時離線</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4.&nbsp; 點擊忙碌時間不可超過10分鐘，若有特殊情況請及時與主任或代理人或質檢報備</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>需轉接到另外一個帳號服務，需等同事登入其它帳號成功後，將對話框進行轉接不可直接關閉窗口，另需注意：</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:56px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">a)<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>第一位接線的客服在服務過程中若有承諾需回電或確認之後再回覆的用戶，但到了用餐時間需轉接到第二個客服，則該問題有結果之後需由第一位接線客服跟進回電解答</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:56px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">b)<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp;&nbsp; </span>當服務同一個用戶時第一位客服服務過程中未出現失誤，但到了用餐時間需轉接到第二個客服進行服務，在服務過程中出現不滿則該服務缺失歸納到第二位客服</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="color: #FF0000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="color: #FF0000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">【處理類規範】</span></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="color: #FF0000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"></span></span></p><p style="margin-top: 8px;margin-bottom: 8px;padding: 0px;white-space: normal;line-height: 19.5px"><span style="line-height: 21px;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px"><span style="color: #FF0000">&nbsp; &nbsp; &nbsp;<span style="line-height: 21px;color: #000000">1.</span></span>&nbsp; 取消廣告：會員內容明確要取消，可直接取消，<span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">需提醒資料會隨之刪除</span></span></p><p class="MsoListParagraph" style="margin-top: 8px;margin-bottom: 8px;margin-left: 24px;padding: 0px;white-space: normal;line-height: 19.5px"><span style="line-height: 21px;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">2.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp;&nbsp;</span>註銷/刪除帳號：需回電確認到，方可處理</span></p><p class="MsoListParagraph" style="margin-top: 8px;margin-bottom: 8px;margin-left: 24px;padding: 0px;white-space: normal;line-height: 19.5px"><span style="line-height: 21px;font-family: 微软雅黑, 'Microsoft YaHei';font-size: 14px">3.<span style="font-variant-numeric: normal;font-variant-east-asian: normal;font-stretch: normal;line-height: normal">&nbsp; 開啟廣告：會員內容明確要開啟，可直接開啟</span></span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><br></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; color: red; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">【回覆規範、要求】</span></p><p style="margin-top:8px;margin-right:0;margin-bottom:   8px;margin-left:0;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="color:#ff0000">&nbsp; &nbsp; &nbsp;&nbsp;<span style="line-height: 150%; color: #000000; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">1.</span></span>&nbsp; 無錯別字和簡體字</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">2.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>回覆內容準確、親切全面（針對多個問題回覆完整）</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">3.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>需提前5分鐘上崗，並將在線暱稱修改好</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">4.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>用戶提問需在5分钟之内回覆（特殊情况需报备说明）</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">5.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>建議性的問題需記錄，提交至產品專員</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">6.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>回覆內容盡量避免多次重複使用同樣語句</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">7.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>問題需跟蹤到位，承諾會員的需兌現</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">8.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>用戶咨詢表達不明確、不清晰時需進一步引導或提問</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">9.<span style="font: 14px 微软雅黑, &quot;Microsoft YaHei&quot;;">&nbsp; </span>遇到需與專員申請的，需先申請不可私自處理（詳情可參考留言回覆申請類）</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">10. 多使用禮貌用語和文明用語，例如：“請您，您好、謝謝、不好意思、麻煩您”等</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">11. 嚴禁出現服務禁語，例如：“不知道、不了解、不清楚、白癡、笨蛋、深圳、大陸” &nbsp; 等</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">12. 嚴禁出現質疑、反問會員語句，例如：“您確定麼？難道您還看不懂麼？”等</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">13. 嚴禁與會員討論非相關業務內容及外洩公司或他人個資等</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">14. 嚴禁與會員討論政治性等一些敏感的話題</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">15. 解答會員操作類問題，附截圖時需加文字描述說明</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">16. 服務完會員需做客服記錄</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">17. 會員反饋問題優先幫其解決，再進行教學指引或溫馨提醒</span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">18. <span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">與網站核心業務無關的訴求，如：幫找房子，幫P圖，幫聯絡刊登者等需協助處理，不可推脫</span></span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;">19.</span></span>用戶未登入賬號，但有提供物件編號、連結、行動電話等間接識別資料，可優先幫會員查詢，線上或回電核實個資（姓名、電話）可直接協助會員處理，若無法提供間接識別資料，請會員登入賬號或來電客服中心處理</p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="line-height: 150%; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;"><br></span></p><p class="MsoListParagraph" style="margin-top:8px;margin-right:0;margin-bottom:8px;margin-left:24px;line-height:150%"><span style="font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px; color: #FF0000;">PS：在線消息2分鐘內可撤回，點擊回覆內容即可彈出。</span></p></td></tr></tbody></table><p><br></p></div></div> <div><span class="button">我要編輯</span></div></li><li class="reply-item"><h4><div><img src="/upload/head/10177.gif?time=1556271256">&nbsp;&nbsp;&nbsp;
              <span class="user">黃雪靜</span>&nbsp;&nbsp;&nbsp;
              <span class="time">回答于2021-09-13 17:45:06</span></div> <div><span class="button button-orange">刪除回答</span></div></h4> <div class="reply-detail"><div class="editor-content clearfix"><p><strong><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 16px;">line回覆規範及要求：</span></strong></p><p><strong><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 16px;"></span></strong><span style="font-family: 微软雅黑,Microsoft YaHei; font-size: 14px;"><br>1、Line上不可以收集用戶個人資料，涉及請會員提供資料查詢的服務流程，應請會員提供物件編號、會員編號等無法直接識別的個人資料，若用戶無法提供，需請會員自行來電客服中心或指引會員使用線上客服處理<br>2、業務咨詢類，需根據會員問題進行完整回覆解答<br>3、用戶資詢表達不明確、不清晰時需進一步引導或提問<br>4、建議性的問題需記錄，提交至產品專員<br>5、嚴禁與會員討論政治性等一些敏感的話題<br>6、嚴禁與會員討論非相關業務內容及外洩公司或他人個資等<br>7、嚴禁出現質疑、反問會員語句，例如：“您確定麼？難道您還看不懂麼？”等<br>8、嚴禁出現服務禁語，例如：“不知道、不了解、不清楚、白癡、笨蛋、深圳、大陸”&nbsp;&nbsp; 等<br>9、無錯別字和簡體字 <br></span><br></p></div></div> <div><span class="button">我要編輯</span></div></li></ul> <h3 class="more-answer">我要補充</h3> <div style=""><div id="editor_73618873924037780" class="edui-default" style=""><div id="edui1" class="edui-editor  edui-default" style="width: 100%; z-index: 999;"><div id="edui1_toolbarbox" class="edui-editor-toolbarbox edui-default"><div id="edui1_toolbarboxouter" class="edui-editor-toolbarboxouter edui-default"><div class="edui-editor-toolbarboxinner edui-default"><div id="edui2" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui2&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui3" class="edui-box edui-button edui-for-fullscreen edui-default"><div id="edui3_state" onmousedown="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui3&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui3_body" unselectable="on" title="全屏" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui3&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui3&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui4" class="edui-box edui-button edui-for-source edui-default"><div id="edui4_state" onmousedown="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui4&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui4_body" unselectable="on" title="源代码" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui4&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui4&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui5" class="edui-box edui-button edui-for-undo edui-default"><div id="edui5_state" onmousedown="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui5&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui5_body" unselectable="on" title="撤销" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui5&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui5&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui6" class="edui-box edui-button edui-for-redo edui-default"><div id="edui6_state" onmousedown="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui6&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui6_body" unselectable="on" title="重做" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui6&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui6&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui7" class="edui-box edui-button edui-for-simpleupload edui-default"><div id="edui7_state" onmousedown="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui7&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui7_body" unselectable="on" title="单图上传" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui7&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui7&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui8" class="edui-box edui-button edui-for-imageleft edui-default"><div id="edui8_state" onmousedown="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui8&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui8_body" unselectable="on" title="左浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui8&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui8&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui9" class="edui-box edui-button edui-for-imageright edui-default"><div id="edui9_state" onmousedown="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui9&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui9_body" unselectable="on" title="右浮动" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui9&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui9&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui10" class="edui-box edui-button edui-for-imagecenter edui-default"><div id="edui10_state" onmousedown="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui10&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui10_body" unselectable="on" title="居中" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui10&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui10&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui11" class="edui-box edui-separator  edui-default"></div><div id="edui12" class="edui-box edui-button edui-for-date edui-default"><div id="edui12_state" onmousedown="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui12&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui12_body" unselectable="on" title="日期" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui12&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui12&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui13" class="edui-box edui-button edui-for-time edui-default"><div id="edui13_state" onmousedown="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui13&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui13_body" unselectable="on" title="时间" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui13&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui13&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui14" class="edui-box edui-button edui-for-horizontal edui-default"><div id="edui14_state" onmousedown="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui14&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui14_body" unselectable="on" title="分隔线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui14&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui14&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui15" class="edui-box edui-separator  edui-default"></div><div id="edui16" class="edui-box edui-splitbutton edui-for-inserttable edui-default"><div title="插入表格" id="edui16_state" onmousedown="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui16&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui16_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui16&quot;]._onArrowClick();"></div></div></div></div><div id="edui19" class="edui-box edui-button edui-for-deletetable edui-default"><div id="edui19_state" onmousedown="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui19&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui19_body" unselectable="on" title="删除表格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui19&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui19&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui20" class="edui-box edui-button edui-for-insertrow edui-default"><div id="edui20_state" onmousedown="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui20&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui20_body" unselectable="on" title="前插入行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui20&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui20&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui21" class="edui-box edui-button edui-for-insertcol edui-default"><div id="edui21_state" onmousedown="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui21&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui21_body" unselectable="on" title="前插入列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui21&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui21&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui22" class="edui-box edui-button edui-for-mergeright edui-default"><div id="edui22_state" onmousedown="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui22&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui22_body" unselectable="on" title="右合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui22&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui22&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui23" class="edui-box edui-button edui-for-mergedown edui-default"><div id="edui23_state" onmousedown="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui23&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui23_body" unselectable="on" title="下合并单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui23&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui23&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui24" class="edui-box edui-button edui-for-deleterow edui-default"><div id="edui24_state" onmousedown="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui24&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui24_body" unselectable="on" title="删除行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui24&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui24&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui25" class="edui-box edui-button edui-for-deletecol edui-default"><div id="edui25_state" onmousedown="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui25&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui25_body" unselectable="on" title="删除列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui25&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui25&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui26" class="edui-box edui-button edui-for-splittorows edui-default"><div id="edui26_state" onmousedown="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui26&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui26_body" unselectable="on" title="拆分成行" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui26&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui26&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui27" class="edui-box edui-button edui-for-splittocols edui-default"><div id="edui27_state" onmousedown="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui27&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui27_body" unselectable="on" title="拆分成列" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui27&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui27&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui28" class="edui-box edui-button edui-for-splittocells edui-default"><div id="edui28_state" onmousedown="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui28&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui28_body" unselectable="on" title="完全拆分单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui28&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui28&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui29" class="edui-box edui-button edui-for-mergecells edui-default"><div id="edui29_state" onmousedown="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui29&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui29_body" unselectable="on" title="合并多个单元格" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui29&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui29&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui36" class="edui-box edui-button edui-for-edittable edui-default"><div id="edui36_state" onmousedown="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui36&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui36_body" unselectable="on" title="表格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui36&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui36&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui41" class="edui-box edui-button edui-for-edittd edui-default"><div id="edui41_state" onmousedown="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui41&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui41_body" unselectable="on" title="单元格属性" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui41&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui41&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui42" class="edui-box edui-separator  edui-default"></div><div id="edui43" class="edui-box edui-menubutton edui-for-lineheight edui-default"><div title="行间距" id="edui43_state" onmousedown="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui43&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui43_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui43&quot;]._onArrowClick();"></div></div></div></div><div id="edui52" class="edui-box edui-menubutton edui-for-rowspacingtop edui-default"><div title="段前距" id="edui52_state" onmousedown="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui52&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui52_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui52&quot;]._onArrowClick();"></div></div></div></div><div id="edui59" class="edui-box edui-menubutton edui-for-rowspacingbottom edui-default"><div title="段后距" id="edui59_state" onmousedown="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui59&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui59_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui59&quot;]._onArrowClick();"></div></div></div></div></div><div id="edui66" class="edui-toolbar   edui-default" onselectstart="return false;" onmousedown="return $EDITORUI[&quot;edui66&quot;]._onMouseDown(event, this);" style="user-select: none;"><div id="edui67" class="edui-box edui-combox edui-for-fontfamily edui-default"><div title="字体" id="edui67_state" onmousedown="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui67&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui67_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onButtonClick(event, this);">字体</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui67&quot;]._onArrowClick();"></div></div></div></div><div id="edui80" class="edui-box edui-combox edui-for-customstyle edui-default"><div title="自定义标题" id="edui80_state" onmousedown="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui80&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui80_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onButtonClick(event, this);">自定义标题</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui80&quot;]._onArrowClick();"></div></div></div></div><div id="edui86" class="edui-box edui-combox edui-for-paragraph edui-default"><div title="段落格式" id="edui86_state" onmousedown="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui86&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui86_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onButtonClick(event, this);">段落格式</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui86&quot;]._onArrowClick();"></div></div></div></div><div id="edui95" class="edui-box edui-combox edui-for-fontsize edui-default"><div title="字号" id="edui95_state" onmousedown="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui95&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-combox-body edui-default"><div id="edui95_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onButtonClick(event, this);">字号</div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui95&quot;]._onArrowClick();"></div></div></div></div><div id="edui110" class="edui-box edui-button edui-for-link edui-default"><div id="edui110_state" onmousedown="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui110&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui110_body" unselectable="on" title="超链接" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui110&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui110&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div><div id="edui111" class="edui-box edui-button edui-for-indent edui-default"><div id="edui111_state" onmousedown="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui111&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui111_body" unselectable="on" title="首行缩进" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui111&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui111&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui112" class="edui-box edui-button edui-for-bold edui-default"><div id="edui112_state" onmousedown="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui112&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui112_body" unselectable="on" title="加粗" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui112&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui112&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui113" class="edui-box edui-splitbutton edui-for-forecolor edui-default edui-colorbutton"><div title="字体颜色" id="edui113_state" onmousedown="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui113&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui113_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui113_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui113&quot;]._onArrowClick();"></div></div></div></div><div id="edui116" class="edui-box edui-splitbutton edui-for-backcolor edui-default edui-colorbutton"><div title="背景色" id="edui116_state" onmousedown="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui116&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-splitbutton-body edui-default"><div id="edui116_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div id="edui116_colorlump" class="edui-colorlump"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui116&quot;]._onArrowClick();"></div></div></div></div><div id="edui119" class="edui-box edui-button edui-for-italic edui-default"><div id="edui119_state" onmousedown="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui119&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui119_body" unselectable="on" title="斜体" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui119&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui119&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui120" class="edui-box edui-button edui-for-underline edui-default"><div id="edui120_state" onmousedown="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui120&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui120_body" unselectable="on" title="下划线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui120&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui120&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui121" class="edui-box edui-button edui-for-strikethrough edui-default"><div id="edui121_state" onmousedown="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui121&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui121_body" unselectable="on" title="删除线" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui121&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui121&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui122" class="edui-box edui-button edui-for-superscript edui-default"><div id="edui122_state" onmousedown="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui122&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui122_body" unselectable="on" title="上标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui122&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui122&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui123" class="edui-box edui-button edui-for-subscript edui-default"><div id="edui123_state" onmousedown="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui123&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui123_body" unselectable="on" title="下标" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui123&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui123&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui124" class="edui-box edui-button edui-for-removeformat edui-default"><div id="edui124_state" onmousedown="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui124&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui124_body" unselectable="on" title="清除格式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui124&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui124&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui125" class="edui-box edui-button edui-for-blockquote edui-default"><div id="edui125_state" onmousedown="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui125&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui125_body" unselectable="on" title="引用" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui125&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui125&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui126" class="edui-box edui-button edui-for-pasteplain edui-default"><div id="edui126_state" onmousedown="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui126&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui126_body" unselectable="on" title="纯文本粘贴模式" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui126&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui126&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div></div></div></div><div id="edui127" class="edui-box edui-menubutton edui-for-insertorderedlist edui-default"><div title="有序列表" id="edui127_state" onmousedown="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui127&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui127_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui127&quot;]._onArrowClick();"></div></div></div></div><div id="edui140" class="edui-box edui-menubutton edui-for-insertunorderedlist edui-default"><div title="无序列表" id="edui140_state" onmousedown="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui140&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-menubutton-body edui-default"><div id="edui140_button_body" class="edui-box edui-button-body edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onButtonClick(event, this);"><div class="edui-box edui-icon edui-default"></div></div><div class="edui-box edui-splitborder edui-default"></div><div class="edui-box edui-arrow edui-default" onclick="$EDITORUI[&quot;edui140&quot;]._onArrowClick();"></div></div></div></div><div id="edui147" class="edui-box edui-button edui-for-cleardoc edui-default"><div id="edui147_state" onmousedown="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseDown(event, this);" onmouseup="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseUp(event, this);" onmouseover="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOver(event, this);" onmouseout="$EDITORUI[&quot;edui147&quot;].Stateful_onMouseOut(event, this);" class="edui-default"><div class="edui-button-wrap edui-default"><div id="edui147_body" unselectable="on" title="清空文档" class="edui-button-body edui-default" onmousedown="return $EDITORUI[&quot;edui147&quot;]._onMouseDown(event, this);" onclick="return $EDITORUI[&quot;edui147&quot;]._onClick(event, this);"><div class="edui-box edui-icon edui-default"></div><div class="edui-box edui-label edui-default"></div></div></div></div></div></div></div></div><div id="edui1_toolbarmsg" class="edui-editor-toolbarmsg edui-default" style="display:none;"><div id="edui1_upload_dialog" class="edui-editor-toolbarmsg-upload edui-default" onclick="$EDITORUI[&quot;edui1&quot;].showWordImageDialog();">点击上传</div><div class="edui-editor-toolbarmsg-close edui-default" onclick="$EDITORUI[&quot;edui1&quot;].hideToolbarMsg();">x</div><div id="edui1_toolbarmsg_label" class="edui-editor-toolbarmsg-label edui-default"></div><div style="height:0;overflow:hidden;clear:both;" class="edui-default"></div></div><div id="edui1_message_holder" class="edui-editor-messageholder edui-default"></div></div><div id="edui1_iframeholder" class="edui-editor-iframeholder edui-default" style="width: 100%; height: 300px; z-index: 999; overflow: hidden;"><iframe id="ueditor_0" width="100%" height="100%" frameborder="0" src="javascript:void(function(){document.open();document.write(&quot;<!DOCTYPE html><html xmlns='http://www.w3.org/1999/xhtml' class='view' ><head><style type='text/css'>.view{padding:0;word-wrap:break-word;cursor:text;height:90%;}
body{margin:8px;font-family:sans-serif;font-size:13px;}p{margin:5px 0;}</style><link rel='stylesheet' type='text/css' href='/static/ueditor/themes/iframe.css'/></head><body class='view' ></body><script type='text/javascript'  id='_initialScript'>setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant0'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);</script></html>&quot;);document.close();}())"></iframe></div><div id="edui1_bottombar" class="edui-editor-bottomContainer edui-default"><table class="edui-default"><tbody class="edui-default"><tr class="edui-default"><td id="edui1_elementpath" class="edui-editor-bottombar edui-default"><div class="edui-editor-breadcrumb">元素路径:</div></td><td id="edui1_wordcount" class="edui-editor-wordcount edui-default">字数统计</td><td id="edui1_scale" class="edui-editor-scale edui-default" style="display: none;"><div class="edui-editor-icon edui-default"></div></td></tr></tbody></table></div><div id="edui1_scalelayer" class="edui-default"></div></div></div> <div class="edit-btn"><button type="button" class="button">提交回答</button> <label data-v-0fe26aab="" for="commonCheckbox" class="checkbox"><input data-v-0fe26aab="" type="checkbox" id="commonCheckbox" name="commonCheckbox"> <i data-v-0fe26aab=""></i> 需要彈窗，請勾選</label></div></div></div> <div class="index-right"><div class="right-bk"><h3 class="com-title"><a href="javascript:;">我的最爱</a></h3> <div class="item-box clearfix"><span>暫無數據</span></div></div> <div class="hotTag right-bk"><h3 class="com-title">热门标签</h3> <div class="hotTag-box item-box clearfix"><p>無數據</p></div></div> <div class="right-bk rank"><h3 class="com-title"><a href="javascript:;">冠軍排行榜</a></h3> <ul><li>暫無數據</li></ul></div> <div class="activeUser right-bk"><h3 class="com-title">活跃用户</h3> <div class="activeUser-box"><a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a> <a href="/" title="周杰伦"><img src="/static/timg.jpeg"></a></div></div> <img src="data:image/png;base64,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" class="to-top"></div></div> <!----> <!----> <!----> <div class="page-cnt footer"><p>T5客服知识库<span>|</span>zsk.591.com.tw</p></div></div></div><script type="text/javascript" src="/js/manifest.f192b.js"></script><script type="text/javascript" src="/js/vendor.17570.js"></script><script type="text/javascript" src="/js/app.58254.js"></script><div id="edui_fixedlayer" class="edui-default" style="position: fixed; left: 0px; top: 0px; width: 0px; height: 0px;"><div id="edui148" class="edui-popup  edui-bubble edui-default" onmousedown="return false;" style="display: none;"> <div id="edui148_body" class="edui-popup-body edui-default"> <iframe style="position:absolute;z-index:-1;left:0;top:0;background-color: transparent;" frameborder="0" width="100%" height="100%" src="about:blank" class="edui-default"></iframe> <div class="edui-shadow edui-default"></div> <div id="edui148_content" class="edui-popup-content edui-default">  </div> </div></div></div><div id="nprogress"><div class="bar" role="bar" style="transform: translate3d(-40.6839%, 0px, 0px); transition: 500ms;"><div class="peg"></div></div></div></body></html>